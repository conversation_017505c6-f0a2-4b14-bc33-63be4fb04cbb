import React, { useEffect, useState } from 'react'
import PropTypes from '../PropTypes'
import { handleError } from '../helpers'

const propTypes = {
  promise: PropTypes.func.isRequired,
  render: PropTypes.func.isRequired,
}

const PromiseContent = ({ promise, render }) => {
  const [result, setResult] = useState()
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const getValue = async () => {
      setLoading(true)

      try {
        const res = await promise()
        setResult(res)
      } catch (error) {
        // handleError(error, 'PromiseContent')
      }

      setLoading(false)
    }

    getValue()
  }, [])

  if (loading) {
    return 'loading...'
  }

  return <>{render(result)}</>
}

PromiseContent.propTypes = propTypes

export default PromiseContent
