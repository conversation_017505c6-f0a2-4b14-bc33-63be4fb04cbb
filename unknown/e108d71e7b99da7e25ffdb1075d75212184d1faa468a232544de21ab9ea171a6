import React, { useState } from 'react'
import { Tabs, Card, Typography } from 'antd'
import MedicalSuppliesMapping from './MedicalSuppliesMapping'
import MedicineMapping from './MedicineMapping'
import TechnicalServicesMapping from './TechnicalServicesMapping'
import SSItemTechnicalServicesMapping from './SSItemTechnicalServicesMapping'
import { usePermission } from '../Permission/hooks/usePermission'

const { Title } = Typography

const MappingManagement = () => {
  const [activeTab, setActiveTab] = useState('1')

  // Check if user has admin permissions - will be used for future permission checks
  usePermission('ADMIN')

  const items = [
    {
      key: '1',
      label: 'VTYT Mapping',
      children: <MedicalSuppliesMapping />,
    },
    {
      key: '2',
      label: 'Thuốc Mapping',
      children: <MedicineMapping />,
    },
    {
      key: '3',
      label: 'DVKT Mapping',
      children: <TechnicalServicesMapping />,
    },
    {
      key: '4',
      label: 'Mapping VTYT và DVKT',
      children: <SSItemTechnicalServicesMapping />,
    },
  ]

  return (
    <div style={{ padding: '20px' }}>
      <Card>
        <Title level={2}>Mapping Management</Title>
        <Tabs
          slot="small"
          activeKey={activeTab}
          onChange={setActiveTab}
          items={items}
          type="card"
        />
      </Card>
    </div>
  )
}

export default MappingManagement
