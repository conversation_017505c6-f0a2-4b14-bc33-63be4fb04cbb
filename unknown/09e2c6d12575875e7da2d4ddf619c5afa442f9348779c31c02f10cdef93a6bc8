import React from 'react'
import { Table } from 'antd'

const data = [
  {
    visit_charge_detail_id: '038c7cec-cf01-404a-8e23-01807a9817bb',
    item_group_name_l: '<PERSON><PERSON><PERSON> nghiệm', // Parent group
    ss_item_code: 'LAB3-33',
    item_name_e: 'ALP - Alkaline Phosphatases',
    health_insurance_name: null,
    service_requester: '2141 - Oncology - general',
    doctor_name: '<PERSON><PERSON>',
    unit_price: 180000,
    health_insurance_unit_price: null,
    quantity: 1,
    total_before_tax: 180000,
    total_after_tax: 180000,
    health_insurance_total_before_tax: 17200,
    health_insurance_total_after_tax: 17200,
  },
  {
    visit_charge_detail_id: '038c7cec-cf01-404a-8e23-01807a9817cc',
    item_group_name_l: 'Xét nghiệm',
    ss_item_code: 'LAB3-34',
    item_name_e: 'Creatinine with eGFR (CKD-EPI)',
    health_insurance_name: null,
    service_requester: '2141 - Oncology - general',
    doctor_name: '<PERSON><PERSON>',
    unit_price: 140000,
    health_insurance_unit_price: null,
    quantity: 1,
    total_before_tax: 140000,
    total_after_tax: 140000,
    health_insurance_total_before_tax: 17440,
    health_insurance_total_after_tax: 17440,
  },
  {
    visit_charge_detail_id: '038c7cec-cf01-404a-8e23-01807a9817dd',
    item_group_name_l: 'Thủ thuật, phẫu thuật', // Another parent group
    ss_item_code: 'RT-VMA02',
    item_name_e: 'Radiotherapy: VMAT; Dose ≤ 2.2 Grays',
    health_insurance_name: null,
    service_requester: '2141 - Oncology - general',
    doctor_name: 'Tran Thi Phuong Thao',
    unit_price: 3190000,
    health_insurance_unit_price: null,
    quantity: 1,
    total_before_tax: 3190000,
    total_after_tax: 3190000,
    health_insurance_total_before_tax: 1300800,
    health_insurance_total_after_tax: 1300800,
  },
]

const groupData = (data) => {
  const grouped = {}

  // Group items by `item_group_name_l`
  data.forEach((item) => {
    if (!grouped[item.item_group_name_l]) {
      grouped[item.item_group_name_l] = {
        key: item.item_group_name_l,
        item_group_name_l: item.item_group_name_l,
        children: [],
        total_before_tax: 0,
        total_after_tax: 0,
        health_insurance_total_before_tax: 0,
        health_insurance_total_after_tax: 0,
      }
    }

    grouped[item.item_group_name_l].children.push(item)

    // Accumulate totals for the parent group
    grouped[item.item_group_name_l].total_before_tax += item.total_before_tax || 0
    grouped[item.item_group_name_l].total_after_tax += item.total_after_tax || 0
    grouped[item.item_group_name_l].health_insurance_total_before_tax +=
      item.health_insurance_total_before_tax || 0
    grouped[item.item_group_name_l].health_insurance_total_after_tax +=
      item.health_insurance_total_after_tax || 0
  })

  return Object.values(grouped)
}

const columns = [
  {
    title: 'Lượt khám',
    dataIndex: 'service_requester',
    key: 'service_requester',
  },
  {
    title: 'Mã item',
    dataIndex: 'ss_item_code',
    key: 'ss_item_code',
  },
  {
    title: 'Tên item',
    dataIndex: 'item_name_e',
    key: 'item_name_e',
  },
  {
    title: 'Đơn giá',
    dataIndex: 'unit_price',
    key: 'unit_price',
    render: (price) =>
      typeof price === 'number'
        ? price.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' })
        : 'N/A', // Handle undefined or null values
  },
  {
    title: 'Thành tiền trước thuế',
    dataIndex: 'total_before_tax',
    key: 'total_before_tax',
    render: (price) =>
      typeof price === 'number'
        ? price.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' })
        : 'N/A',
  },
  {
    title: 'Thành tiền sau thuế',
    dataIndex: 'total_after_tax',
    key: 'total_after_tax',
    render: (price) =>
      typeof price === 'number'
        ? price.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' })
        : 'N/A',
  },
  {
    title: 'Tổng tiền BHYT trước thuế',
    dataIndex: 'health_insurance_total_before_tax',
    key: 'health_insurance_total_before_tax',
    render: (price) =>
      typeof price === 'number'
        ? price.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' })
        : 'N/A',
  },
  {
    title: 'Tổng tiền BHYT sau thuế',
    dataIndex: 'health_insurance_total_after_tax',
    key: 'health_insurance_total_after_tax',
    render: (price) =>
      typeof price === 'number'
        ? price.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' })
        : 'N/A',
  },
]

const GroupedTable = () => {
  const groupedData = groupData(data)

  return (
    <Table
      size="small"
      columns={columns}
      dataSource={groupedData}
      rowKey="key"
      pagination={false}
      bordered
      expandable={{
        expandedRowRender: (record) => {
          return (
            <Table
              size="small"
              columns={columns.filter((col) => col.dataIndex !== 'item_group_name_l')}
              dataSource={record.children}
              rowKey="visit_charge_detail_id"
              pagination={false}
            />
          )
        },
        rowExpandable: (record) => record.children && record.children.length > 0,
      }}
      summary={(pageData) => (
        <>
          {pageData.map((group) => (
            <Table.Summary.Row key={group.key}>
              <Table.Summary.Cell colSpan={3}>{group.item_group_name_l}</Table.Summary.Cell>
              <Table.Summary.Cell>
                {group.total_before_tax.toLocaleString('vi-VN', {
                  style: 'currency',
                  currency: 'VND',
                })}
              </Table.Summary.Cell>
              <Table.Summary.Cell>
                {group.total_after_tax.toLocaleString('vi-VN', {
                  style: 'currency',
                  currency: 'VND',
                })}
              </Table.Summary.Cell>
              <Table.Summary.Cell>
                {group.health_insurance_total_before_tax.toLocaleString('vi-VN', {
                  style: 'currency',
                  currency: 'VND',
                })}
              </Table.Summary.Cell>
              <Table.Summary.Cell>
                {group.health_insurance_total_after_tax.toLocaleString('vi-VN', {
                  style: 'currency',
                  currency: 'VND',
                })}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          ))}
        </>
      )}
    />
  )
}

export default GroupedTable
