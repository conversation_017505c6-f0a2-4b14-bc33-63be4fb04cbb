import React, { useEffect, useMemo, useState } from 'react'
import { Form, Input, InputNumber, Popconfirm, Table, Typography } from 'antd'
import PropTypes from '../../../common/PropTypes'
import { useCommon } from '../../../store/common'
import { findEntityPropertyByName, parseMetadata } from '../helpers'
import EditableCellComponent from './EditableCellComponent'

const propTypes = {
  tableColumns: PropTypes.array.isRequired,
  tableDataSource: PropTypes.array.isRequired,
  tableEntityType: PropTypes.object,
}

const DynamicTableComponent = ({ tableColumns, tableDataSource, tableEntityType }) => {
  //hooks
  const [form] = Form.useForm()
  const { MetadataXml } = useCommon()
  //state
  const [currentEntityType, setCurrentEntityType] = useState([])
  const [currentPrimarykey, setCurrentPrimarykey] = useState(null)
  //others
  const [editingKey, setEditingKey] = useState('')
  const isEditing = (record) => record.key === editingKey
  const tableName = tableEntityType?.['@_Name']
  const primaryKeyName = tableEntityType?.Key?.PropertyRef['@_Name']
  const properties = tableEntityType?.Property

  const edit = (record) => {
    form.setFieldsValue({
      name: '',
      age: '',
      address: '',
      ...record,
    })
    setEditingKey(record.key)
  }
  const cancel = () => {
    setEditingKey('')
  }
  const save = async (key) => {
    // try {
    //   const row = await form.validateFields()
    //   const newData = [...data]
    //   const index = newData.findIndex((item) => key === item.key)
    //   if (index > -1) {
    //     const item = newData[index]
    //     newData.splice(index, 1, {
    //       ...item,
    //       ...row
    //     })
    //     setData(newData)
    //     setEditingKey('')
    //   } else {
    //     newData.push(row)
    //     setData(newData)
    //     setEditingKey('')
    //   }
    // } catch (errInfo) {
    //  console.log('Validate Failed:', errInfo)
    // }
  }
  // useEffect(() => {
  //   const fetchData = () => {
  //     const initialFormValues = {}
  //     tableDataSource.forEach((row) => {
  //       Object.keys(row).forEach((column) => {
  //         if (!initialFormValues[row.key]) {
  //           initialFormValues[row.key] = {}
  //         }
  //         initialFormValues[row.key][column] = row[column]
  //       })
  //     })
  //     form.setFieldsValue(initialFormValues)
  //   }
  //   fetchData()
  // }, [tableDataSource])

  // const handleFetchEntityData = () => {
  //   const parsedMetadata = parseMetadata(MetadataXml)
  //  console.log('DynamictableName', tableName)
  //   const entityType = findEntityPropertyByName(parsedMetadata, tableName)
  //   setCurrentEntityType(entityType)
  // }
  // useEffect(() => {
  //  console.log('tableEntityType', tableEntityType)
  // }, [tableName])

  // const test = [
  //   ...tableColumns,
  //   {
  //     title: 'operation',
  //     dataIndex: 'operation',
  //     editable: false
  //     // render: (_, record) => {
  //     //   const editable = isEditing(record)
  //     //   return editable ? (
  //     //     <span>
  //     //       <Typography.Link
  //     //         onClick={() => save(record.key)}
  //     //         style={{
  //     //           marginInlineEnd: 8
  //     //         }}>
  //     //         Save
  //     //       </Typography.Link>
  //     //       <Popconfirm title="Sure to cancel?" onConfirm={cancel}>
  //     //         <a>Cancel</a>
  //     //       </Popconfirm>
  //     //     </span>
  //     //   ) : (
  //     //     <Typography.Link disabled={editingKey !== ''} onClick={() => edit(record)}>
  //     //       Edit
  //     //     </Typography.Link>
  //     //   )
  //     // }
  //   }
  // ]
  // const mergedColumns = test.map((col) => {
  //   if (!col.editable) {
  //     return col
  //   }
  //   return {
  //     ...col,
  //     onCell: (record) => ({
  //       record,
  //       inputType: col.dataIndex === 'age' ? 'number' : 'text',
  //       dataIndex: col.dataIndex,
  //       title: col.title,
  //       editing: isEditing(record),
  //       fieldProp: properties.find((prop) => prop['@_Name'] == col.title)
  //     })
  //   }
  // })
  const mergedColumns = [
    ...tableColumns,
    {
      title: 'operation',
      dataIndex: 'operation',
      render: (_, record) => {
        const editable = isEditing(record)
        return editable ? (
          <span>
            <Typography.Link
              onClick={() => save(record.key)}
              style={{
                marginInlineEnd: 8,
              }}>
              Save
            </Typography.Link>
            <Popconfirm title="Sure to cancel?" onConfirm={cancel}>
              <a>Cancel</a>
            </Popconfirm>
          </span>
        ) : (
          <Typography.Link disabled={editingKey !== ''} onClick={() => edit(record)}>
            Edit
          </Typography.Link>
        )
      },
    },
  ]

  // const columns = [
  //   {
  //     title: 'name',
  //     dataIndex: 'name',
  //     width: '25%',
  //     editable: true
  //   },
  //   {
  //     title: 'age',
  //     dataIndex: 'age',
  //     width: '15%',
  //     editable: true
  //   },
  //   {
  //     title: 'address',
  //     dataIndex: 'address',
  //     width: '40%',
  //     editable: true
  //   },
  //   {
  //     title: 'operation',
  //     dataIndex: 'operation',
  //     render: (_, record) => {
  //       const editable = isEditing(record)
  //       return editable ? (
  //         <span>
  //           <Typography.Link
  //             onClick={() => save(record.key)}
  //             style={{
  //               marginInlineEnd: 8
  //             }}>
  //             Save
  //           </Typography.Link>
  //           <Popconfirm title="Sure to cancel?" onConfirm={cancel}>
  //             <a>Cancel</a>
  //           </Popconfirm>
  //         </span>
  //       ) : (
  //         <Typography.Link disabled={editingKey !== ''} onClick={() => edit(record)}>
  //           Edit
  //         </Typography.Link>
  //       )
  //     }
  //   }
  // ]
  // const mergedColumns = columns.map((col) => {
  //   if (!col.editable) {
  //     return col
  //   }
  //   return {
  //     ...col,
  //     onCell: (record) => ({
  //       record,
  //       inputType: col.dataIndex === 'age' ? 'number' : 'text',
  //       dataIndex: col.dataIndex,
  //       title: col.title,
  //       editing: isEditing(record)
  //     })
  //   }
  // })
  return (
    <Form form={form} component={false}>
      <div className="">
        {' '}
        Table name: {tableName} | Priamry key: {primaryKeyName}
      </div>
      <Table
        size="small"
        components={{
          body: {
            cell: EditableCellComponent,
          },
        }}
        bordered
        dataSource={tableDataSource}
        // columns={mergedColumns}
        columns={mergedColumns.map((col) => {
          if (!col.editable) {
            return col
          }
          return {
            ...col,
            onCell: (record) => ({
              record,
              inputType: col.dataIndex === 'age' ? 'number' : 'text',
              dataIndex: col.dataIndex,
              title: col.title,
              editing: isEditing(record),
              fieldProp: properties.find((prop) => prop['@_Name'] == col.title),
            }),
          }
        })}
        // columns={useMemo(
        //   () =>
        //     tableColumns.map((col) => ({
        //       ...col,
        //       onCell: (record) => ({
        //         record,
        //         inputType: col.dataIndex === 'age' ? 'number' : 'text',
        //         dataIndex: col.dataIndex,
        //         title: col.title,
        //         editing: true
        //       })
        //     })),
        //   [tableColumns]
        // )}
        rowClassName="editable-row"
        pagination={false}
      />
    </Form>
  )
}

DynamicTableComponent.propTypes = propTypes

export default DynamicTableComponent
