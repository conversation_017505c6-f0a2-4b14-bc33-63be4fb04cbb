<?xml version="1.0"?>
<configuration>
	<system.webServer>
		<rewrite>
			<rules>
				<rule name="FVH-SI" stopProcessing="true">
					<match url=".*" />
					<conditions logicalGrouping="MatchAll">
						<add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
						<!-- <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" /> -->
						<add input="{REQUEST_URI}" pattern="^/(api)" negate="true" />
					</conditions>
					<action type="Rewrite" url="/index.html" />
					<!-- <action type="Rewrite" url="/si/index.html" /> -->
				</rule>
			</rules>
		</rewrite>
	</system.webServer>
</configuration>
