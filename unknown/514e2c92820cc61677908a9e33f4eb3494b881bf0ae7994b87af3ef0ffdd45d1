import React from 'react'
import PropTypes from 'prop-types'
import { Select, Input, Button } from 'antd'
import { SearchOutlined } from '@ant-design/icons'

const { Option } = Select

/**
 * Reusable filter component for mapping screens
 */
const MappingFilter = ({
  filterType,
  setFilterType,
  searchFilter,
  setSearchFilter,
  debouncedMappingIdSearch,
  entityOptions,
  entityLabel,
  entitySearchHandler,
  entityOptionRenderer,
  itemSearchHandler,
  itemOptions,
  itemOptionRenderer,
}) => {
  return (
    <div style={{ display: 'flex', gap: '8px', width: '70%' }}>
      <Select
        placeholder={`Select ${entityLabel}`}
        style={{ width: 250 }}
        showSearch
        allowClear
        filterOption={false}
        onSearch={entitySearchHandler}
        optionLabelProp="label"
        value={searchFilter?.entity_id}
        onChange={(value) => {
          if (value) {
            setSearchFilter({ entity_id: value })
          } else {
            setSearchFilter(null)
          }
        }}>
        {entityOptions.map(entityOptionRenderer)}
      </Select>

      <Select
        placeholder="Search for Item"
        style={{ width: 250 }}
        showSearch
        allowClear
        filterOption={false}
        optionLabelProp="label"
        onSearch={itemSearchHandler}
        onChange={(value) => {
          if (value) {
            setSearchFilter({ item_id: value })
          } else {
            setSearchFilter(null)
          }
        }}>
        {itemOptions.map(itemOptionRenderer)}
      </Select>

      {/* filter by active_flag */}
      <Select
        placeholder="Filter by status"
        style={{ width: 200 }}
        allowClear
        onChange={(value) => {
          setSearchFilter((prev) => ({ ...prev, active_flag: value }))
        }}>
        <Option value={true}>Active</Option>
        <Option value={false}>Inactive</Option>
      </Select>

      <Button onClick={() => setSearchFilter(null)} icon={<SearchOutlined />}>
        Show All
      </Button>
    </div>
  )
}

MappingFilter.propTypes = {
  filterType: PropTypes.string.isRequired,
  setFilterType: PropTypes.func.isRequired,
  setSearchFilter: PropTypes.func.isRequired,
  debouncedMappingIdSearch: PropTypes.func.isRequired,
  entityOptions: PropTypes.array.isRequired,
  entityLabel: PropTypes.string.isRequired,
  entitySearchHandler: PropTypes.func.isRequired,
  entityOptionRenderer: PropTypes.func.isRequired,
  itemSearchHandler: PropTypes.func.isRequired,
  itemOptions: PropTypes.array.isRequired,
  itemOptionRenderer: PropTypes.func.isRequired,
}

export default MappingFilter
