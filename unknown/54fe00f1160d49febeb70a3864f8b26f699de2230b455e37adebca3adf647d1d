// export default HomePage
import React, { useEffect } from 'react'
import { Button, Card, Image, List } from 'antd'
import { useCommon } from '../../store/common'
import { useNavigate } from 'react-router-dom'
import imgFVBanner from '../../assets/fvbanner.png'

const HomePage = () => {
  const { AppRefList } = useCommon()
  const navigate = useNavigate()
  const navigateTo = (url, logbookTypeId) => {
    navigate(`${url}/${logbookTypeId}`)
  }

  useEffect(() => {
    navigate('his/visit')
  }, [])

  // const get
  const handleRowClick = (record) => {
    // Navigate to a new page with the record's ID
    navigate(`/ptw-list/ptw-detail/${record.ID}`)
  }
  const data = [
    {
      title: 'Title 1',
    },
    {
      title: 'Title 2',
    },
    {
      title: 'Title 3',
    },
    {
      title: 'Title 4',
    },
    {
      title: 'Title 5',
    },
    {
      title: 'Title 6',
    },
  ]
  return (
    <div className="home">
      <div className="">
        <Image src={imgFVBanner} alt="Logo" preview={false} />
      </div>
      {/* 
      <div className="flex items-center justify-center">
        <span className="mt-2 text-base font-bold  text-center text-[#1d7990]">FVH</span>
      </div> */}
      {/* <div className="row mt-2 min-h-[85vh]" style={{ backgroundColor: 'white' }}>
        <div className="row m-3">
          <List
            grid={{ gutter: 16, column: 4 }}
            dataSource={AppRefList}
            renderItem={(item) => (
              <List.Item>
                <Card
                  onClick={() => {
                    // Pass the logbook id or another identifier to the navigateTo function
                    navigateTo('/elogbook-list', item.app_rcd)
                  }}
                  title={item.name_e}>
                  {item.name_e}
                </Card>
              </List.Item>
            )}
          />
        </div>
      </div> */}

      <div style={{ height: '85%' }}>
        <Button
          onClick={() => {
            navigate('/tool')
          }}>
          {' '}
          nav to tool{' '}
        </Button>
        <Button
          onClick={() => {
            navigate('/learning')
          }}>
          {' '}
          nav to learning{' '}
        </Button>
      </div>
    </div>
  )
}

export default HomePage
