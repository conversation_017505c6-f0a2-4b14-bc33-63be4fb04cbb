import React, { useState, useEffect, useRef, forwardRef } from 'react'
import PropTypes from '../PropTypes'
import { useResize } from '../contexts/ResizeContext'
import useDeepCompareEffect from 'use-deep-compare-effect'

// Using forwardRef to properly handle refs passed from parent components
const ResizableTitle = forwardRef(({ onResize, width, ...restProps }, ref) => {
  const { setResizing, setGuidePosition } = useResize()
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [startWidth, setStartWidth] = useState(width)
  // Use the forwarded ref if provided, otherwise create a local ref
  const thRef = useRef(null)

  // Combine refs (forwarded ref and local ref)
  const setRefs = (element) => {
    // Update our local ref
    thRef.current = element
    // Update forwarded ref if it exists
    if (ref) {
      if (typeof ref === 'function') {
        ref(element)
      } else {
        ref.current = element
      }
    }
  }

  useDeepCompareEffect(() => {
    const handleMouseMove = (e) => {
      if (!isDragging) return
      e.preventDefault()
      setGuidePosition(e.clientX)
    }

    const handleMouseUp = (e) => {
      if (!isDragging) return
      setIsDragging(false)
      setResizing(false)
      document.body.classList.remove('resizing')

      const deltaX = e.clientX - startX
      const newWidth = Math.max(50, startWidth + deltaX - 50)
      onResize(e, { size: { width: newWidth } })
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.classList.add('resizing')
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.classList.remove('resizing')
    }
  }, [isDragging, startX, startWidth, onResize, setGuidePosition, setResizing])

  const handleMouseDown = (e) => {
    e.stopPropagation()
    if (!thRef.current) return

    const cellRect = thRef.current.getBoundingClientRect()

    setIsDragging(true)
    setResizing(true)
    setStartX(e.clientX)
    setStartWidth(width)
    setGuidePosition(cellRect.right)
  }

  if (!width) {
    return <th ref={setRefs} {...restProps} />
  }

  return (
    <th
      ref={setRefs}
      {...restProps}
      style={{
        position: 'relative',
        width,
        whiteSpace: 'normal',
        overflow: 'visible',
        ...restProps.style,
      }}>
      {restProps.children}
      <div
        className="react-resizable-handle"
        onMouseDown={handleMouseDown}
        onClick={(e) => e.stopPropagation()}
      />
    </th>
  )
})
const propTypes = {
  onResize: PropTypes.func,
  width: PropTypes.number,
}

ResizableTitle.propTypes = propTypes

// displayName helps with debugging
ResizableTitle.displayName = 'ResizableTitle'

export default ResizableTitle
