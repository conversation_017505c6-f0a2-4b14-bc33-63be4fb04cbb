import { displayCurrency } from '../../../common/helpers'
import { ACTION_VISIT_HISTORY } from '../VisitConstant'

export const PP_VO_CAM_OPTIONS = [
  { value: '1', label: '1. <PERSON><PERSON><PERSON> mê' },
  { value: '2', label: '2. <PERSON><PERSON><PERSON> tê' },
  { value: '3', label: '3. Châm tê' },
  { value: '4', label: '4. <PERSON><PERSON>c phương pháp vô cảm khác' },
]

export const VISIT_CHARGE_HISTORY_SNAPSHOT = {
  [ACTION_VISIT_HISTORY.MOVE_UP_CHARGE_DETAIL]: {
    makeSnapshot: (item_code) => {
      return JSON.stringify({ item_code })
    },
    getSnapshot: (rawSnapshot = '') => {
      if (!rawSnapshot) {
        return ''
      }

      const snapshot = JSON.parse(rawSnapshot)

      return {
        ...snapshot,
        displayE: `Moved up item ${snapshot?.item_code}`,
        displayL: `Đ<PERSON> move up item ${snapshot?.item_code}`,
      }
    },
  },
  [ACTION_VISIT_HISTORY.MOVE_DOWN_CHARGE_DETAIL]: {
    makeSnapshot: (item_code) => {
      return JSON.stringify({ item_code })
    },
    getSnapshot: (rawSnapshot = '') => {
      if (!rawSnapshot) {
        return ''
      }

      const snapshot = JSON.parse(rawSnapshot)

      return {
        ...snapshot,
        displayE: `Moved down item ${snapshot?.item_code}`,
        displayL: `Đã move down item ${snapshot?.item_code}`,
      }
    },
  },
  [ACTION_VISIT_HISTORY.SEND_TO_CASHIER]: {
    makeSnapshot: (totalSent, chargeDetailList) => {
      return JSON.stringify({ totalSent, chargeDetailList })
    },
    getSnapshot: (rawSnapshot = '') => {
      if (!rawSnapshot) {
        return ''
      }

      const snapshot = JSON.parse(rawSnapshot)

      return {
        ...snapshot,
        displayE: `Sent to cashier total: ${displayCurrency(snapshot?.totalSent)}`,
        displayL: `Đã gửi cashier tổng: ${displayCurrency(snapshot?.totalSent)}`,
      }
    },
  },
}
