@keyframes loading-page-spinner2 {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.loading-page-spinner2 div {
  box-sizing: border-box !important;
}
.loading-page-spinner2 > div {
  position: absolute;
  width: 144px;
  height: 144px;
  top: 28px;
  left: 28px;
  border-radius: 50%;
  border: 16px solid #000;
  border-color: #fdfdfd transparent #fdfdfd transparent;
  animation: loading-page-spinner2 1s linear infinite;
}
.loading-page-spinner2 > div:nth-child(2) {
  border-color: transparent;
}
.loading-page-spinner2 > div:nth-child(2) div {
  position: absolute;
  width: 100%;
  height: 100%;
  transform: rotate(45deg);
}
.loading-page-spinner2 > div:nth-child(2) div:before,
.loading-page-spinner2 > div:nth-child(2) div:after {
  content: '';
  display: block;
  position: absolute;
  width: 16px;
  height: 16px;
  top: -16px;
  left: 48px;
  background: #fdfdfd;
  border-radius: 50%;
  box-shadow: 0 128px 0 0 #fdfdfd;
}
.loading-page-spinner2 > div:nth-child(2) div:after {
  left: -16px;
  top: 48px;
  box-shadow: 128px 0 0 0 #fdfdfd;
}
.loading-page-spinner {
  width: 100px;
  height: 100px;
  display: inline-block;
  overflow: hidden;
  background: none;
}
.loading-page-spinner2 {
  width: 100%;
  height: 100%;
  position: relative;
  transform: translateZ(0) scale(0.5);
  backface-visibility: hidden;
  transform-origin: 0 0; /* see note above */
}
.loading-page-spinner2 div {
  box-sizing: content-box;
}
