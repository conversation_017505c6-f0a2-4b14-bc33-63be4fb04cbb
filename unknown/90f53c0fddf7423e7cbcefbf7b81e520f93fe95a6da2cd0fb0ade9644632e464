import { getItemsService, addListItemService, deleteListItemService } from '../common/services'
import lists from '../common/lists'
import config from '../common/config'

// Function to convert a base64 string to a Uint8Array
const urlBase64ToUint8Array = (base64String) => {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4)
  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/')
  const rawData = window.atob(base64)
  const outputArray = new Uint8Array(rawData.length)

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i)
  }

  return outputArray
}

// Function to convert ArrayBuffer to base64 string
const arrayBufferToBase64 = (buffer) => {
  const binary = String.fromCharCode.apply(null, new Uint8Array(buffer))
  return window.btoa(binary)
}

// This should be your VAPID public key from your server
// For testing, you can use a placeholder value
const applicationServerPublicKey =
  'BM3OqjlEW_3WwgUahPNJpfMUrPa2K5tpSuuZOcgQ96C2eM4OuIMLRvZEsCLg9YCKdqSUBvxYnNK010RIfQ2Kbv0'

// Function to request notification permission
export const requestNotificationPermission = async () => {
  try {
    const permission = await Notification.requestPermission()
    // console.log('Notification permission:', permission)
    return permission === 'granted'
  } catch (error) {
    console.error('Error requesting notification permission:', error)
    return false
  }
}

// Function to register subscription with server
export const registerSubscriptionWithServer = async (subscription, userId) => {
  try {
    // Convert subscription keys to base64 strings if they're not already
    const p256dh =
      typeof subscription.getKey === 'function'
        ? arrayBufferToBase64(subscription.getKey('p256dh'))
        : subscription.keys.p256dh

    const auth =
      typeof subscription.getKey === 'function'
        ? arrayBufferToBase64(subscription.getKey('auth'))
        : subscription.keys.auth

    // Prepare subscription data for server
    const dataSubscription = {
      user_id: userId,
      endpoint: subscription.endpoint,
      p256dh: p256dh,
      auth: auth,
      type: 'webpush',
      lu_user_id: userId,
    }

    // Check if subscription already exists for this user and endpoint
    const existingSubscriptions = await getItemsService(lists.notification_subscription, {
      filter: `user_id eq ${userId} and endpoint eq '${subscription.endpoint}'`,
    })

    if (
      existingSubscriptions &&
      existingSubscriptions.value &&
      existingSubscriptions.value.length > 0
    ) {
      // Subscription exists, update it
      const currentSubscription = existingSubscriptions.value[0]

      // If keys have changed, delete old subscription and create new one
      if (currentSubscription.p256dh !== p256dh || currentSubscription.auth !== auth) {
        await deleteListItemService(
          lists.notification_subscription,
          currentSubscription.notification_subscription_id,
        )
        await addListItemService(lists.notification_subscription, dataSubscription)
      }
      // Otherwise, no need to update as the endpoint and keys are the same
    } else {
      // No existing subscription, create new one
      await addListItemService(lists.notification_subscription, dataSubscription)
    }

    return true
  } catch (error) {
    console.error('Error registering subscription with server:', error)
    return false
  }
}

// Function to subscribe to push notifications
export const subscribeToPushNotifications = async (userId) => {
  try {
    // Check if service worker is registered
    if (!('serviceWorker' in navigator) || !window.swRegistration) {
      console.error('Service Worker not supported or not registered')
      return false
    }

    // Check if already subscribed
    const subscription = await window.swRegistration.pushManager.getSubscription()
    if (subscription) {
      // Register existing subscription with server
      await registerSubscriptionWithServer(subscription, userId)
      return subscription
    }

    // Subscribe the user
    const newSubscription = await window.swRegistration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(applicationServerPublicKey),
    })

    window.isSubscribed = true

    // Register new subscription with server
    await registerSubscriptionWithServer(newSubscription, userId)

    return newSubscription
  } catch (error) {
    console.error('Failed to subscribe to push notifications:', error)
    return null
  }
}

// Function to unsubscribe from push notifications
export const unsubscribeFromPushNotifications = async (userId) => {
  try {
    // Check if service worker is registered
    if (!('serviceWorker' in navigator) || !window.swRegistration) {
      console.error('Service Worker not supported or not registered')
      return false
    }

    // Get the subscription
    const subscription = await window.swRegistration.pushManager.getSubscription()
    if (!subscription) {
      return true
    }

    // Unsubscribe the user from the browser
    await subscription.unsubscribe()

    window.isSubscribed = false

    // Remove subscription from server
    try {
      const existingSubscriptions = await getItemsService(lists.notification_subscription, {
        filter: `user_id eq ${userId} and endpoint eq '${subscription.endpoint}'`,
      })

      if (
        existingSubscriptions &&
        existingSubscriptions.value &&
        existingSubscriptions.value.length > 0
      ) {
        const currentSubscription = existingSubscriptions.value[0]
        await deleteListItemService(
          lists.notification_subscription,
          currentSubscription.notification_subscription_id,
        )
      }
    } catch (serverError) {
      console.error('Error removing subscription from server:', serverError)
      // Continue even if server deletion fails
    }

    return true
  } catch (error) {
    console.error('Failed to unsubscribe from push notifications:', error)
    return false
  }
}

// Function to send a local notification (not a push notification)
export const sendLocalNotification = (title, options = {}) => {
  if (!('Notification' in window)) {
    console.error('This browser does not support notifications')
    return
  }

  if (Notification.permission === 'granted') {
    const notification = new Notification(title, {
      icon: config.HOME_PAGE + '/pwa-192x192.png',
      badge: config.HOME_PAGE + '/pwa-192x192.png',
      ...options,
    })

    // Handle notification click
    notification.onclick = function () {
      window.focus()
      notification.close()
      if (options.onClick) options.onClick()
    }

    return notification
  } else {
    console.warn('Notification permission not granted')
    return null
  }
}

// Function to simulate receiving a push notification (for testing)
export const simulatePushNotification = (title, options = {}) => {
  return sendLocalNotification(title, options)
}

// Function to check if the browser supports notifications
export const checkNotificationSupport = () => {
  return 'Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window
}

// Function to check notification permission status
export const getNotificationPermissionStatus = () => {
  if (!('Notification' in window)) {
    return 'unsupported'
  }
  return Notification.permission
}

// Function to send a notification with actions
export const sendNotificationWithActions = (title, options = {}) => {
  if (!window.swRegistration) {
    console.error('Service worker registration not found')
    return sendLocalNotification(title, options)
  }

  const notificationOptions = {
    icon: config.HOME_PAGE + '/pwa-192x192.png',
    badge: config.HOME_PAGE + '/pwa-192x192.png',
    vibrate: [100, 50, 100],
    ...options,
    actions: options.actions || [
      {
        action: 'view',
        title: 'View',
        icon: config.HOME_PAGE + '/pwa-192x192.png',
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: config.HOME_PAGE + '/pwa-192x192.png',
      },
    ],
  }

  return window.swRegistration.showNotification(title, notificationOptions)
}

// Function to automatically request notification permission and subscribe
export const autoRequestAndSubscribe = async (userId) => {
  const result = {
    permissionGranted: false,
    subscribed: false,
  }

  try {
    // Check if notifications are supported
    if (!checkNotificationSupport()) {
      // Notifications not supported in this browser
      return result
    }

    // Check current permission status
    const permissionStatus = getNotificationPermissionStatus()

    // If permission is already granted
    if (permissionStatus === 'granted') {
      result.permissionGranted = true

      // Check if already subscribed
      const existingSubscription = await window.swRegistration?.pushManager.getSubscription()
      if (existingSubscription) {
        // Register existing subscription with server
        await registerSubscriptionWithServer(existingSubscription, userId)
        result.subscribed = true
        window.isSubscribed = true
        return result
      }

      // Not subscribed yet, so subscribe
      const subscription = await subscribeToPushNotifications(userId)
      result.subscribed = !!subscription
      return result
    }

    // If permission is denied, we can't request again
    if (permissionStatus === 'denied') {
      // Notification permission was denied previously
      return result
    }

    // If permission status is default (not decided yet), request permission
    const granted = await requestNotificationPermission()
    result.permissionGranted = granted

    if (granted) {
      // Permission granted, subscribe to push notifications
      const subscription = await subscribeToPushNotifications(userId)
      result.subscribed = !!subscription
    }

    return result
  } catch {
    // Silent error handling for automatic subscription
    return result
  }
}
