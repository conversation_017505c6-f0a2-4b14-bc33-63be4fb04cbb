export function generateDM_BHYTTableColumns(jsonData) {
  if (!Array.isArray(jsonData) || jsonData.length === 0) {
    return []
  }
  // console.log('entityType', entityType)
  //   // Map the keys to the column configuration
  let tableColumns = jsonData.map((column, index) => ({
    title: column.col_name_display,
    dataIndex: column.table_col_name,
    key: index,
    editable: column.col_editable,
    width: '40%',
    ...column,
  }))

  return tableColumns
}
export const generateDM_BHYTTableDataSoruce = (dataSource) => {
  if (!Array.isArray(dataSource) || dataSource.length === 0) {
    return []
  }
  return dataSource.map((item, index) => {
    return { key: index + 1, ...item }
  })
}

export const generateGUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
