.pdf-viewer {
  background-color: #f1f1f1;

  &--header {
    position: sticky;
    top: 0;
    left: 0;
    background-color: white;
    z-index: 2;
    padding: 5px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 0 5px lightgray;
  }

  &--content {
  }

  &--document {
    // display: flex;
    // flex-direction: column;
    // align-items: center;
    padding: 10px;
    // width: 100vw;
    width: 100%;
    max-width: 100%;
    overflow-x: scroll;
    position: relative;
  }

  &--page {
    background-color: #f1f1f1 !important;
    margin-bottom: 10px;
    z-index: 1;

    canvas,
    .textLayer {
      box-shadow: 0 0 5px lightgray;
      margin: auto;
    }
  }
}

.sign-tag {
  font-family: Times;
  line-height: 1;
  cursor: pointer;

  & {
    padding-left: 0.3em;
  }

  &.date {
    background-color: #a4eefdd6;
    display: flex;
    align-items: flex-end;
    line-height: 1;
    padding-left: 0;
  }

  &.seal {
    background-color: #a4eefdd6;
    display: flex;
    align-items: flex-end;
  }

  &.contractNumber {
    background-color: #a4eefdd6;
  }

  &.personal {
    background-color: #f4f4f4;
  }

  &.external {
    background-color: #e4ffb5e6;
  }

  &__radio {
    top: 2.5em;
    left: 0;
    padding-left: 0.3em;
    position: absolute;
    background: #f4f4f4;
    width: 100%;
    outline: rgb(138, 138, 138) solid 0.07em;
    color: red;
    font-size: 0.9em;
    font-style: italic;

    & > div {
      display: flex;
      align-items: center;
      gap: 0.3em;
      margin-top: 0.3em;
      margin-bottom: 0.3em;

      & > label {
        width: 100%;
      }
    }
  }
}

.duplicate-btn {
  width: 2em;
  height: 1.4em;
  background: #000;
  color: white;
  right: 0;
  position: absolute;
  text-align: center;
  cursor: pointer;
  padding: 2% 0;
  font-weight: bold;
  top: -1.4em;

  &.minus {
    right: 2em;
    background: #ff5c5c;
  }

  &.plus {
    right: 0;
    background: #83a745;
  }
}
