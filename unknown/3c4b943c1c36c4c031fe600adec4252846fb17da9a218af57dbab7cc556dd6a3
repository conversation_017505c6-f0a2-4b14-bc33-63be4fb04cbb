import React from 'react'
import { <PERSON>, <PERSON>ton, <PERSON>ert, Space, Typography } from 'antd'
import { usePermission, PermissionCheck } from './hooks/usePermission'

const { Title, Text } = Typography

const PermissionExample = () => {
  // Check if user has specific permissions
  const { hasPermission: canCreateVisit, isLoading: loadingCreateVisit } =
    usePermission('CREATE_VISIT')
  const { hasPermission: canExecuteCharge, isLoading: loadingExecuteCharge } =
    usePermission('EXECUTE_CHARGE')
  const { hasPermission: canCashierMode, isLoading: loadingCashierMode } =
    usePermission('CASHIER_MODE')

  // Example of a component that requires permission
  const ProtectedComponent = () => (
    <Alert
      message="Protected Component"
      description="This component is only visible to users with CASHIER_MODE permission."
      type="success"
      showIcon
    />
  )

  return (
    <Card title="Permission Example">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Title level={4}>Permission Check Results:</Title>

        {loadingCreateVisit ? (
          <Text>Loading CREATE_VISIT permission...</Text>
        ) : (
          <Alert
            message={`CREATE_VISIT Permission: ${canCreateVisit ? 'Granted' : 'Denied'}`}
            type={canCreateVisit ? 'success' : 'error'}
            showIcon
          />
        )}

        {loadingExecuteCharge ? (
          <Text>Loading EXECUTE_CHARGE permission...</Text>
        ) : (
          <Alert
            message={`EXECUTE_CHARGE Permission: ${canExecuteCharge ? 'Granted' : 'Denied'}`}
            type={canExecuteCharge ? 'success' : 'error'}
            showIcon
          />
        )}

        {loadingCashierMode ? (
          <Text>Loading CASHIER_MODE permission...</Text>
        ) : (
          <Alert
            message={`CASHIER_MODE Permission: ${canCashierMode ? 'Granted' : 'Denied'}`}
            type={canCashierMode ? 'success' : 'error'}
            showIcon
          />
        )}

        <Title level={4}>Conditional Rendering Example:</Title>

        {/* Example of conditional rendering based on permission */}
        {canCreateVisit && (
          <Button type="primary">Create Visit (Only visible with CREATE_VISIT permission)</Button>
        )}

        {/* Using the PermissionCheck component */}
        <PermissionCheck permissionCode="EXECUTE_CHARGE">
          <Button type="primary" danger>
            Execute Charge (Only visible with EXECUTE_CHARGE permission)
          </Button>
        </PermissionCheck>

        <PermissionCheck
          permissionCode="CASHIER_MODE"
          fallback={<Alert message="You don't have CASHIER_MODE permission" type="warning" />}>
          <ProtectedComponent />
        </PermissionCheck>
      </Space>
    </Card>
  )
}

export default PermissionExample
