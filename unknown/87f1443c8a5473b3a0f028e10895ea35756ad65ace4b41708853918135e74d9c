import React, { useEffect, useState } from 'react'
import { useUI } from '../../common/UIProvider'
import { useCommon } from '../../store/common'
import { useDispatch } from 'react-redux'
import { useWatch } from 'antd/es/form/Form'
import { useDebounceValue } from 'usehooks-ts'
import { Button, DatePicker, Form, Input, Select, Table } from 'antd'
import { FORMAT_DATE } from '../../common/constant'
import {
  getItemsService,
  getMetadataService,
  sendToPortalService,
  transInvoiceViewToTableXMLService,
} from '../../common/services'
import lists from '../../common/lists'
import { handleError } from '../../common/helpers'
import {
  convertBase64ToXml,
  downloadXMLFile,
  findEntityPropertyByName,
  generateXMLToolTableColumns,
  generateXMLToolTableDataSoruce,
  parseMetadata,
} from '../../SI/helper'
import { getSystemSettingTableColumnList } from '../../SI/service'
import { siXmlToolActions } from '../../store/SI/xml_tool'
import XmlToolTable from '../Tool/XmlToolTable'
import { XML_LIST } from '../../SI/constant'
import SPPagination from '../../common/components/SPPagination'
import ExcelHandler from '../../common/components/ExcelHandler'
import DynamicTableComponent from '../../common/components/DynamicTable/DynamicTableComponent'

const LearningPage = () => {
  //hooks
  const [toolXMLForm] = Form.useForm()
  const ui = useUI()
  const { MetadataXml } = useCommon()
  const dispatch = useDispatch()
  // Filter
  const filterSSTable = useWatch('filterSSTable', toolXMLForm)
  const filterHN = useDebounceValue(useWatch('filterHN', toolXMLForm), 500)[0]
  const filterInvoiceNo = useDebounceValue(useWatch('filterInvoiceNo', toolXMLForm), 500)[0]
  const filterVisitDate = useWatch('filterVisitDate', toolXMLForm)
  // state
  const [ssTable, setSSTable] = useState([])
  const [loadingSSTable, setLoadingSSTable] = useState(false)
  const [tableMetadataEntity, setTableMetadataEntity] = useState(null)
  const [importedData, setImportedData] = useState(null)

  // others
  const { RangePicker } = DatePicker
  const handleGetSSTableData = async (skip = '', top = 10, withLoading = true) => {
    withLoading && setLoadingSSTable(true)
    //  console.log('StartGetData')
    try {
      let filter = []
      if (filterInvoiceNo) {
        filter.push(`(invoice_no_ eq '${filterInvoiceNo}')`)
      }
      if (filterHN) {
        filter.push(`(MA_BN eq '${filterHN}')`)
      }
      if (filterVisitDate) {
        filter.push(
          `NGAY_VAO ge ${filterVisitDate[0].format(
            FORMAT_DATE,
          )} and NGAY_VAO le ${filterVisitDate[1].format(FORMAT_DATE)}`,
        )
      }

      const data = await getItemsService(lists[filterSSTable], {
        filter: filter.join('and '),
        top: top,
        skip: skip,
        //  orderBy: 'lu_updated desc'
      })
      setSSTable(data.value)
      return data
    } catch (error) {
      handleError(error)
      ui.notiError('Không tìm thấy nội dung')
    }
    setLoadingSSTable(false)
  }

  useEffect(() => {
    if (filterSSTable) {
      handleGetSSTableData()
    }
  }, [filterSSTable, filterHN, filterInvoiceNo, filterVisitDate])

  const handleFetchTableMetadataEntity = async () => {
    let metadata_xml = MetadataXml
    if (!metadata_xml) {
      const response = await getMetadataService()
      metadata_xml = response.data
    }

    const parsedMetadata = await parseMetadata(metadata_xml)
    const entityType = findEntityPropertyByName(parsedMetadata, filterSSTable)
    setTableMetadataEntity(entityType)
    //  console.log(entityType)
    return entityType
  }

  const handleSendToThePortal = async (invoice_no) => {
    let response = {}
    try {
      response = await sendToPortalService(invoice_no)
      //  console.log(response)
      const data = await getItemsService(lists.tbl_file_tracking, {
        filter: `maGiaoDich eq '${response?.maGiaoDich}'`,
        top: 1,
      })
      const file_tracking = data.value[0]
      const xml_content = convertBase64ToXml(file_tracking?.file_content)
      downloadXMLFile(xml_content, file_tracking?.file_name)

      ui.notiSuccess(response?.thongDiep)
    } catch (error) {
      handleError(error)
      ui.notiError(response?.thongDiep)
    }
  }

  const handleGenerateData = async () => {
    try {
      let filterQuery = []
      if (filterInvoiceNo) {
        filterQuery.push(`invoice_no=${filterInvoiceNo}`)
      }
      if (filterHN) {
        filterQuery.push(`hn=${filterHN}`)
      }
      if (filterVisitDate?.[0]) {
        filterQuery.push(`visit_start=${filterVisitDate[0].format(FORMAT_DATE)}`)
      }
      if (filterVisitDate?.[1]) {
        filterQuery.push(`visit_end=${filterVisitDate[1].format(FORMAT_DATE)}`)
      }
      const response = await transInvoiceViewToTableXMLService(filterSSTable, filterQuery.join('&'))
      ui.notiSuccess(response?.message)
    } catch (error) {
      handleError(error)
    }
  }

  useEffect(() => {
    handleFetchTableMetadataEntity()
    //handleFetchTableMetadataEntity()
  }, [filterSSTable])

  const handleGetSystemSettingTableColumnList = async () => {
    const response = await getSystemSettingTableColumnList()
    dispatch(siXmlToolActions.setSystemSettingTableColumnList(response))
  }
  useEffect(() => {
    handleGetSystemSettingTableColumnList()
  })
  const handleImport = (importedData) => {
    //  console.log('Imported Data:', importedData)
  }

  const handleConfirmImportExcel = (data) => {
    //  console.log('Saving Data:', data)
    handleGetSSTableData()
    // Implement your save logic here
  }

  if (!ssTable) {
    return <div>Loading...</div>
  }

  return (
    <div className="container-fluid">
      <Form form={toolXMLForm}>
        <h1 className="fw-bold"> XmlToolPage </h1>
        <Button
          onClick={() => {
            //  console.log(toolXMLForm.getFieldsValue())
          }}>
          {' '}
          GET FORM VALUE{' '}
        </Button>
        <hr className="row mt-2"></hr>
        <div className="row mt-2">
          <span className="fw-bold">FILTER: </span>{' '}
          <div className="d-flex justify-content-md-start gap-5">
            <Form.Item
              // initialValue={lists.ss_table_1.listName}
              style={{ width: 200 }}
              label="Ss table:"
              name={'filterSSTable'}>
              <Select
                allowClear
                options={XML_LIST.map((item, index) => ({
                  index: index,
                  label: item.list_name,
                  value: item.list_name,
                }))}></Select>
            </Form.Item>

            <Form.Item label="HN:" name={'filterHN'}>
              <Input allowClear placeholder="Search hn"></Input>
            </Form.Item>
            <Form.Item label="Invoice no:" name={'filterInvoiceNo'}>
              <Input placeholder="Search invoice no"></Input>
            </Form.Item>
            <Form.Item label="Visit date:" name={'filterVisitDate'}>
              <RangePicker></RangePicker>
            </Form.Item>
          </div>
        </div>

        <hr className="row mt-2"></hr>
        <div className="row mt-2">
          <span className="fw-bold">ACTION:</span>
          <div className="d-flex justify-content-md-start gap-3">
            {filterSSTable == lists.ss_table_1.listName && (
              <>
                <Button
                  onClick={async () => {
                    handleGenerateData()
                  }}>
                  GENERATE DATA
                </Button>
                <Button
                  disabled={filterInvoiceNo && ssTable.length > 0 ? false : true}
                  onClick={() => {
                    if (!filterInvoiceNo || ssTable.length == 0) {
                      ui.notiWarning(
                        'Bạn chưa nhập invoice no hoặc bảng dữ liệu không được để trống',
                      )
                      return
                    }
                    handleSendToThePortal(filterInvoiceNo)
                  }}>
                  EXPORT XML
                </Button>
              </>
            )}
            <Button
              onClick={() => {
                handleGetSSTableData()
              }}>
              REFRESH DATA
            </Button>
          </div>
        </div>
        <hr className="row mt-2"></hr>
        <div className="row mt-2"></div>
        {/* {ssTable.length > 0 && (
          <DynamicTableComponent
            tableColumns={generateXMLToolTableColumns(ssTable)}
            tableDataSource={generateXMLToolTableDataSoruce(ssTable)}
            tableMetadataEntity={tableMetadataEntity}
            tablePrimaryKeyName={tableMetadataEntity?.Key?.PropertyRef['@_Name']}
            tableName={tableMetadataEntity?.['@_Name']}
            onSave={handleGetSSTableData}></DynamicTableComponent>
        )} */}
        {ssTable.length > 0 && (
          <>
            <XmlToolTable
              isNavigateToDetail={true}
              tableColumns={generateXMLToolTableColumns(ssTable)}
              tableDataSource={generateXMLToolTableDataSoruce(ssTable)}
              tableMetadataEntity={tableMetadataEntity}
              tablePrimaryKeyName={tableMetadataEntity?.Key?.PropertyRef['@_Name']}
              tableName={tableMetadataEntity?.['@_Name']}
              onSave={handleGetSSTableData}></XmlToolTable>

            <DynamicTableComponent
              tableColumns={generateXMLToolTableColumns(ssTable)}
              tableDataSource={generateXMLToolTableDataSoruce(ssTable)}
              tableMetadataEntity={tableMetadataEntity}
              isEditableTable={true}
              tablePrimaryKeyName={tableMetadataEntity?.Key?.PropertyRef['@_Name']}
              tableName={tableMetadataEntity?.['@_Name']}
              onSave={handleGetSSTableData}></DynamicTableComponent>
          </>
        )}
        {filterSSTable && (
          <SPPagination
            getItems={handleGetSSTableData}
            setItems={setSSTable}
            items={ssTable}
            loading={loadingSSTable}
            setLoading={setLoadingSSTable}></SPPagination>
        )}
        {ssTable.length > 0 && (
          <ExcelHandler
            dataToExport={ssTable}
            dataTableName={tableMetadataEntity?.['@_Name'] || null}
            onImport={handleImport}
            onSave={handleConfirmImportExcel}></ExcelHandler>
        )}
      </Form>
    </div>
  )
}

export default LearningPage
