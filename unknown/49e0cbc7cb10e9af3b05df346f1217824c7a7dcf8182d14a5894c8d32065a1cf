import React, { useEffect, useMemo, useState } from 'react'
import {
  <PERSON><PERSON>,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Popconfirm,
  Select,
  Table,
  Typography,
} from 'antd'
import DynamicTableComponent from './components/DynamicTableComponent'
import TestOnCellComponent from './components/TestOnCellComponent'
import useFetchSSTableXML from './hooks/useFetchTableXML'
import { generateXMLToolTableColumns, generateXMLToolTableDataSoruce } from './helpers'
import { XML_LIST } from './constant'
import lists from '../../common/lists'
import TestEditableTable from './components/TestEditableTable'
import DynamicTableComponentTest from './components/DynamicTableComponentTest'
import DynamicFormExample from './components/DynamicFormExample'
import TanStackComponent from './components/Learning/TanStackComponent'
import DynamicTableComponentTest3 from './components/Learning/DynamicTableComponentTest3'
import axios from 'axios'
import SPPagination from '../../common/components/SPPagination'

import { FORMAT_DATE } from '../../common/constant'
import { useWatch } from 'antd/es/form/Form'
import { useDarkMode, useDebounceValue } from 'usehooks-ts'
import { useUI } from '../../common/UIProvider'
import { getItemsService } from '../../common/services'
import { handleError } from '../../common/helpers'

export const ToolTest = () => {
  //hooks
  const [toolXMLForm] = Form.useForm()
  const ui = useUI()
  // Filter
  const filterSSTable = useWatch('filterSSTable', toolXMLForm)
  const filterHN = useDebounceValue(useWatch('filterHN', toolXMLForm), 500)[0]
  const filterInvoiceNo = useDebounceValue(useWatch('filterInvoiceNo', toolXMLForm), 500)[0]
  const filterVisitDate = useWatch('filterVisitDate', toolXMLForm)
  // Memoize filters to prevent unnecessary re-renders
  // const filters = useMemo(
  //   () => ({
  //     SSTable: filterSSTable,
  //     HN: filterHN,
  //     InvoiceNo: filterInvoiceNo,
  //     VisitDate: filterVisitDate
  //   }),
  //   [filterSSTable, filterHN, filterInvoiceNo, filterVisitDate]
  // )

  const { RangePicker } = DatePicker

  //custom hooks
  // const {
  //   ssTable,
  //   setSelectedSSTableOption
  //   // ssTableXml1Columns,
  //   // ssTableXml2Columns,
  //   // ssTableXml3Columns,
  //   // ssTableXml1DataSource
  //   // ssTableXml4Columns,
  //   // ssTableXml5Columns
  // } = useFetchSSTableXML(filters)

  // // state
  const [ssTable, setSSTable] = useState([])
  const [loadingSSTable, setLoadingSSTable] = useState(false)

  const handleGetSSTableData = async (skip = '', top = 10, withLoading = true) => {
    withLoading && setLoadingSSTable(true)
    //  console.log('StartGetData')
    try {
      const data = await getItemsService(lists[filterSSTable], { top: top, skip: skip })
      setSSTable(data.value)
      // const response = await fetchAuth({
      //   url: 'https://portal.fvhospital.com/si-api/odata/ss_table_1',
      //   method: 'get',
      //   headers: {
      //     Accept: 'application/json;odata=nometadata'
      //   }
      // })

      // // fetchAuth('https://portal.fvhospital.com/si-api/odata/ss_table_1', {
      // //   headers: {
      // //     Authorization: `Bearer ${localStorage.getItem('access_token')}`,
      // //     'Accept-Language': 'en-US',
      // //     'Content-Type': 'application/json'
      // //   }
      // // })
      // //  await axios.get('https://portal.fvhospital.com/si-api/odata/ss_table_1')
      // // Extract @odata.context
      // const odataContext = response.data['@odata.context']
      // console.log('OData Context:', odataContext)
      return data
    } catch (error) {
      handleError(error)
    }
    setLoadingSSTable(false)
  }

  useEffect(() => {
    handleGetSSTableData()
  }, [filterSSTable, filterHN, filterInvoiceNo, filterVisitDate])

  return (
    <Form form={toolXMLForm}>
      ToolTest
      <Button
        onClick={async () => {
          const response = {
            '@odata.context': 'https://portal.fvhospital.com/si-api/odata/$metadata#ss_table_1',
            value: [], // Dữ liệu khác
          }

          // Lấy URL từ @odata.context
          const metadataUrl = response['@odata.context'].split('#')[0] // Loại bỏ phần `#ss_table_1`

          try {
            // Gửi yêu cầu đến metadata URL
            const metadataResponse = await axios.get(metadataUrl, {
              headers: {
                Accept: 'application/xml', // Metadata thường là XML
              },
            })

            //  console.log('Metadata Content:', metadataResponse.data)
            return metadataResponse.data
          } catch (error) {
            console.error('Error fetching metadata:', error)
          }
        }}>
        TEST GET XML
      </Button>
      <Button
        onClick={() => {
          //  console.log(filterSSTable)
        }}></Button>
      <div className="row mt-2">
        <div className="d-flex justify-content-md-start gap-5">
          <Form.Item>
            <Input></Input>
          </Form.Item>

          <Form.Item style={{ width: '50%' }} label="Ss table:" name={'filterSSTable'}>
            <Select
              options={XML_LIST.map((item, index) => ({
                index: index,
                label: item.list_name,
                value: item.list_name,
              }))}></Select>
          </Form.Item>

          <Form.Item label="HN:" name={'filterHN'}>
            <Input allowClear placeholder="Search hn"></Input>
          </Form.Item>
        </div>
      </div>
      <div className="row mt-2">
        <div className="d-flex justify-content-md-start gap-5">
          <Form.Item label="Invoice no:" name={'filterInvoiceNo'}>
            <Input placeholder="Search invoice no"></Input>
          </Form.Item>
          <Form.Item label="Visit date:" name={'filterVisitDate'}>
            <RangePicker></RangePicker>
          </Form.Item>
        </div>
      </div>
      {/*  */}
      {/* <DynamicTableComponentTest3
        tableColumns={generateXMLToolTableColumns(BANG_1, lists.ss_table_1)}
        tableDataSource={generateXMLToolTableDataSoruce(
          [
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1
          ],
          lists.ss_table_1
        )}></DynamicTableComponentTest3> */}
      {/* <TanStackComponent
        tableColumns={generateXMLToolTableColumns(BANG_1, lists.ss_table_1)}
        tableDataSource={generateXMLToolTableDataSoruce(
          [
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1,
            ...BANG_1
          ],
          lists.ss_table_1
        )}></TanStackComponent> */}
      {/* <TestOnCellComponent></TestOnCellComponent> */}
      {/* <TestEditableTable></TestEditableTable> */}
      {/* <DynamicTableComponentTest
        tableColumns={generateXMLToolTableColumns(BANG_1, lists.ss_table_1)}
        tableDataSource={generateXMLToolTableDataSoruce(
          BANG_1,
          lists.ss_table_1
        )}></DynamicTableComponentTest> */}
      <DynamicTableComponent
        tableColumns={generateXMLToolTableColumns(ssTable)}
        tableDataSource={generateXMLToolTableDataSoruce(ssTable)}></DynamicTableComponent>
      <SPPagination
        getItems={handleGetSSTableData}
        setItems={setSSTable}
        items={ssTable}
        loading={loadingSSTable}
        setLoading={setLoadingSSTable}></SPPagination>
      {/* <Bu onClick={handleGetData}>Get Data</Bu
      {/* <DynamicFormExample></DynamicFormExample> */}
    </Form>
  )
}

export default ToolTest
