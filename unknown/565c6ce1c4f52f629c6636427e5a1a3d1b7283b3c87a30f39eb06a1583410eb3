import { useQuery } from '@tanstack/react-query'
import lists from '../../../common/lists'
import { getItemsService } from '../../../common/services'

const SS_ITEM_TECHNICAL_SERVICE_MAPPING_QUERY_KEYS = {
  SS_ITEM_TECHNICAL_SERVICE_MAPPING: 'ssItemTechnicalServiceMapping',
}

export const useSSItemTechnicalServiceMapping = (medicalSuppliesId) => {
  const ssItemTechnicalServiceMappingQuery = useQuery({
    queryKey: [
      SS_ITEM_TECHNICAL_SERVICE_MAPPING_QUERY_KEYS.SS_ITEM_TECHNICAL_SERVICE_MAPPING,
      medicalSuppliesId,
    ],
    queryFn: async () => {
      const data = await getItemsService(lists.ss_item_technical_services_mapping, {
        filter: `medical_supplies_id eq ${medicalSuppliesId}`,
        top: 50,
      })

      return data.value
    },
    enabled: !!medicalSuppliesId && medicalSuppliesId !== 'N/A',
  })

  const isLoading = ssItemTechnicalServiceMappingQuery.isLoading

  return {
    ssItemTechnicalServiceMappingQuery,
    ssItemTechnicalServiceMapping: ssItemTechnicalServiceMappingQuery.data || [],
    isLoading: isLoading,
    isError: ssItemTechnicalServiceMappingQuery.isError,
  }
}
