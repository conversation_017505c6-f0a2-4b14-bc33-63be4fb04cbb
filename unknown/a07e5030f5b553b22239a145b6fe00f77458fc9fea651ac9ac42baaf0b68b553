import React from 'react'
import { Tag } from 'antd'
import PropTypes from '../PropTypes'
import { CopyOutlined } from '@ant-design/icons'
import { useUI } from '../UIProvider'

const CopyableTag = ({ text, maxWidth = 450 }) => {
  const ui = useUI()

  const handleCopy = () => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        ui.message.success('Copied to clipboard!')
      })
      .catch(() => {
        ui.notiError('Failed to copy!')
      })
  }

  if (!text) {
    return ''
  }

  return (
    <Tag
      className="w-100 d-flex justify-content-between"
      color="blue"
      onClick={handleCopy}
      style={{ cursor: 'pointer' }}>
      <span style={{ cursor: 'pointer', maxWidth: maxWidth }} className="text-truncate">
        {text}
      </span>
      <CopyOutlined className="ms-2" />
    </Tag>
  )
}

CopyableTag.propTypes = {
  text: PropTypes.string,
  maxWidth: PropTypes.number,
}

export default CopyableTag
