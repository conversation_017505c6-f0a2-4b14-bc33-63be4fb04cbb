import React from 'react'
import { Image } from 'antd'
import PropTypes from 'prop-types'
import imgAvatar from '../assets/avatar.png'

const propTypes = {
  dataHome: PropTypes.object,
  handleChangeMenu: PropTypes.func,
}

const HomeItem = ({ dataHome, handleChangeMenu }) => {
  const handleNavigate = () => {
    handleChangeMenu(dataHome.NavigateTo)
  }
  return (
    <div
      className="grow d-flex flex-col py-2 relative gap-3 rounded-3xl bg-white shadow-md cursor-pointer hover:shadow-lg duration-500"
      onClick={handleNavigate}>
      <div className="d-flex items-center justify-center gap-2">
        <Image width={50} height={50} preview={false} src={imgAvatar} alt="Logo" />
      </div>
      <div className="flex items-center justify-center">
        <span className="font-semibold text-sm w-full text-center text-[#165c6d] p-1">
          {dataHome.Title}
        </span>
      </div>
    </div>
  )
}

HomeItem.propTypes = propTypes
export default HomeItem
