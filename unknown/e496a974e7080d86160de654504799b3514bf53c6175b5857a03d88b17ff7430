// Helper function to format ID for OData endpoints
const formatId = (id) => {
  // Check if id is a UUID or number
  const isUuid =
    typeof id === 'string' &&
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)
  const isNumber = !isNaN(id) && !isNaN(parseFloat(id))

  // If UUID or string, wrap in quotes, otherwise use as is
  return isUuid || isNumber ? `(${id})` : `('${id}')`
}

export default {
  getItems: (listName) => `/odata/${listName}/`,
  getItem: (listName, id) => `/odata/${listName}${formatId(id)}`,
  addItem: (listName) => `/odata/${listName}`,
  updateItem: (listName, id) => `/odata/${listName}${formatId(id)}`,
  deleteItem: (listName, id) => `/odata/${listName}${formatId(id)}`,
  // File
  getFile: (serverRelativeUrl) =>
    `api/store/get-file-content?serverRelativeUrl=${serverRelativeUrl}`,
  getFileInfo: (site, serverRelativeUrl) =>
    `${site}/_api/web/GetFileByServerRelativeUrl('${serverRelativeUrl}')`,
  addFile: (serverRelativeUrl, fileName, refID, dataSource) =>
    `/api/Store/create-file?serverRelativeUrl=${serverRelativeUrl}&fileName=${fileName}&refID=${refID}&dataSource=${dataSource}`,
  updateFile: (site, serverRelativeUrl) =>
    `${site}/_api/web/GetFileByServerRelativeUrl('${serverRelativeUrl}')/$value`,
  deleteFile: (serverRelativeUrl) => `api/store/delete-file?serverRelativeUrl=${serverRelativeUrl}`,
  patientPhoto: (path) => `/api/api/photo?path=${path}`,
  // #region CustomAPI
  getLogbooks: (logbookTypeId) => `/api/apprecords?app_rcd=${logbookTypeId}`,
  patchMultiRecords: (listName) => `/odata/${listName}/multi`,
  addFolder: (serverRelativeUrl) =>
    `api/store/create-folder?serverRelativeUrl=${serverRelativeUrl}`,
  deleteFolder: (serverRelativeUrl) =>
    `api/store/delete-folder?serverRelativeUrl=${serverRelativeUrl}`,
  getAppFieldRoleIndicatorMappings: (app_rcd) =>
    `/api/jointable/app_field_role_indicator_mapping?app_rcd=${app_rcd}`,
  getAppRecordDetailChangeLogs: (record_id) =>
    `/api/jointable/app_record_detail_change_log?record_id=${record_id}`,
  transInvoiceViewToTableXML: (listname, filterQuery) =>
    `/api/transdata/add/${listname}?${filterQuery}`,
  sendToPortal: (invoice_no, main_gate) =>
    `/api/parsexml/xml1to7?invoice_no=${invoice_no}&main_gate=${main_gate}`,
  syncVisitDetailCharge: (patient_visit_id, lu_user_id, charge_detail_lu_updated) =>
    `/api/syncdata/visit_charge_detail?patient_visit_id=${patient_visit_id}&lu_user_id=${lu_user_id}&charge_detail_lu_updated=${charge_detail_lu_updated || ''}`,
  // #endregion CustomAPI
}
