import React, { useEffect, useMemo, useState } from 'react'
import {
  <PERSON><PERSON>,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Popconfirm,
  Select,
  Table,
  Typography,
} from 'antd'
import {
  convertBase64ToXml,
  downloadXMLFile,
  findEntityPropertyByName,
  generateXMLToolTableColumns,
  generateXMLToolTableDataSoruce,
  parseMetadata,
} from '../../SI/helper'
import lists from '../../common/lists'
// import TestEditableTable from './components/Learning/TestEditableTable'
// import DynamicTableComponentTest from './components/DynamicTableComponentTest'
// import DynamicFormExample from './components/Learning/DynamicFormExample'
// import TanStackComponent from './components/Learning/TanStackComponent'
// import DynamicTableComponentTest3 from './components/Learning/DynamicTableComponentTest3'
import axios from 'axios'
import SPPagination from '../../common/components/SPPagination'

import { FORMAT_DATE } from '../../common/constant'
import { useWatch } from 'antd/es/form/Form'
import { useDarkMode, useDebounceValue } from 'usehooks-ts'
import { useUI } from '../../common/UIProvider'
import {
  getItemsService,
  getMetadataService,
  sendToPortalService,
  transInvoiceViewToTableXMLService,
} from '../../common/services'
import { useCommon } from '../../store/common'
import { filter } from 'lodash'
import SSTableComponent from './XmlToolTable'
import { siXmlToolActions } from '../../store/SI/xml_tool'
import { useDispatch } from 'react-redux'
import {
  generateDynamicTableColumns,
  generateDynamicTableDataSoruce,
  handleError,
} from '../../common/helpers'
import { getSystemSettingTableColumnList } from '../../SI/service'
import { XML_LIST } from '../../SI/constant'
import DynamicTableComponent from '../../common/components/DynamicTable/DynamicTableComponent'
import XmlToolTable from './XmlToolTable'
import ExcelHandler from '../../common/components/ExcelHandler'
import { generateXMLTable, sendXMLTableToThePortal } from './XmlToolService'

export const XmlToolPage = () => {
  //hooks
  const [toolXMLForm] = Form.useForm()
  const ui = useUI()
  const { MetadataXml } = useCommon()
  const dispatch = useDispatch()
  // Filter
  const filterSSTable = useWatch('filterSSTable', toolXMLForm)
  const filterHN = useDebounceValue(useWatch('filterHN', toolXMLForm), 500)[0]
  const filterInvoiceNo = useDebounceValue(useWatch('filterInvoiceNo', toolXMLForm), 500)[0]
  const filterVisitDate = useWatch('filterVisitDate', toolXMLForm)
  // state
  const [ssTable, setSSTable] = useState([])
  const [loadingSSTable, setLoadingSSTable] = useState(false)
  const [tableMetadataEntity, setTableMetadataEntity] = useState(null)
  // others
  const { RangePicker } = DatePicker
  const handleGetSSTableData = async (skip = '', top = 10, withLoading = true) => {
    withLoading && setLoadingSSTable(true)
    ui.setLoading(true)
    // console.log('StartGetData')
    try {
      let filter = []
      if (filterInvoiceNo) {
        filter.push(`(invoice_no_ eq '${filterInvoiceNo}')`)
      }
      if (filterHN) {
        filter.push(`(MA_BN eq '${filterHN}')`)
      }
      if (filterVisitDate) {
        filter.push(
          `NGAY_VAO ge ${filterVisitDate[0].format(
            FORMAT_DATE,
          )} and NGAY_VAO le ${filterVisitDate[1].format(FORMAT_DATE)}`,
        )
      }

      const data = await getItemsService(lists[filterSSTable], {
        filter: filter.join('and '),
        top: top,
        skip: skip,
        //  orderBy: 'lu_updated desc'
      })
      setSSTable(data.value)
      ui.setLoading(false)
      return data
    } catch (error) {
      handleError(error)
      ui.notiError('Không tìm thấy nội dung')
    }
    setLoadingSSTable(false)
    ui.setLoading(false)
  }

  useEffect(() => {
    if (filterSSTable) {
      handleGetSSTableData()
    }
  }, [filterSSTable, filterHN, filterInvoiceNo, filterVisitDate])

  const handleFetchTableMetadataEntity = async () => {
    let metadata_xml = MetadataXml
    if (!metadata_xml) {
      const response = await getMetadataService()
      metadata_xml = response.data
    }

    const parsedMetadata = await parseMetadata(metadata_xml)
    const entityType = findEntityPropertyByName(parsedMetadata, filterSSTable)
    setTableMetadataEntity(entityType)
    // console.log(entityType)
    return entityType
  }

  const handleGenerateXMLTable = async () => {
    await generateXMLTable({ filterSSTable, filterInvoiceNo, filterHN, filterVisitDate }, ui)
  }
  const handleSendXMLTableToThePortal = async () => {
    await sendXMLTableToThePortal(filterInvoiceNo, ui)
  }

  useEffect(() => {
    handleFetchTableMetadataEntity()
    //handleFetchTableMetadataEntity()
  }, [filterSSTable])

  const handleGetSystemSettingTableColumnList = async () => {
    const response = await getSystemSettingTableColumnList()
    dispatch(siXmlToolActions.setSystemSettingTableColumnList(response))
  }
  const handleImport = (importedData) => {
    // console.log('Imported Data:', importedData)
  }

  const handleConfirmImportExcel = (data) => {
    // console.log('Saving Data:', data)
    handleGetSSTableData()
    // Implement your save logic here
  }
  const handleOnSaveXmlToolTable = async (response) => {
    // // console.log('dynamic response', response)
    handleGetSSTableData()
    // return null
  }
  useEffect(() => {
    handleGetSystemSettingTableColumnList()
  })

  if (!ssTable) {
    return <div>Loading...</div>
  }

  return (
    <div className="container-fluid">
      <Form form={toolXMLForm}>
        <h1 className="fw-bold"> XmlToolPage </h1>
        <Button
          onClick={() => {
            // console.log(toolXMLForm.getFieldsValue())
          }}>
          {' '}
          GET FORM VALUE{' '}
        </Button>
        <hr className="row mt-2"></hr>
        <div className="row mt-2">
          <span className="fw-bold">FILTER: </span>{' '}
          <div className="d-flex justify-content-md-start gap-5">
            <Form.Item
              // initialValue={lists.ss_table_1.listName}
              style={{ width: 200 }}
              label="Ss table:"
              name={'filterSSTable'}>
              <Select
                allowClear
                options={XML_LIST.map((item, index) => ({
                  index: index,
                  label: item.list_name,
                  value: item.list_name,
                }))}></Select>
            </Form.Item>

            <Form.Item label="HN:" name={'filterHN'}>
              <Input allowClear placeholder="Search hn"></Input>
            </Form.Item>
            <Form.Item label="Invoice no:" name={'filterInvoiceNo'}>
              <Input placeholder="Search invoice no"></Input>
            </Form.Item>
            <Form.Item label="Visit date:" name={'filterVisitDate'}>
              <RangePicker></RangePicker>
            </Form.Item>
          </div>
        </div>

        <hr className="row mt-2"></hr>
        <div className="row mt-2">
          <span className="fw-bold">ACTION:</span>
          <div className="d-flex justify-content-md-start gap-3">
            {filterSSTable == lists.ss_table_1.listName && (
              <>
                <Button
                  onClick={async () => {
                    handleGenerateXMLTable()
                  }}>
                  GENERATE DATA
                </Button>
                <Button
                  disabled={filterInvoiceNo && ssTable.length > 0 ? false : true}
                  onClick={() => {
                    if (!filterInvoiceNo || ssTable.length == 0) {
                      ui.notiWarning(
                        'Bạn chưa nhập invoice no hoặc bảng dữ liệu không được để trống',
                      )
                      return
                    }
                    handleSendXMLTableToThePortal()
                  }}>
                  EXPORT XML
                </Button>
              </>
            )}
            <Button
              onClick={() => {
                handleGetSSTableData()
              }}>
              REFRESH DATA
            </Button>
          </div>
        </div>
        <hr className="row mt-2"></hr>
        <div className="row mt-2"></div>
        {/* {ssTable.length > 0 && (
          <DynamicTableComponent
            tableColumns={generateXMLToolTableColumns(ssTable)}
            tableDataSource={generateXMLToolTableDataSoruce(ssTable)}
            tableMetadataEntity={tableMetadataEntity}
            tablePrimaryKeyName={tableMetadataEntity?.Key?.PropertyRef['@_Name']}
            tableName={tableMetadataEntity?.['@_Name']}
            onSave={handleGetSSTableData}></DynamicTableComponent>
        )} */}
        {ssTable.length > 0 && (
          <>
            {/* <XmlToolTable
              isNavigateToDetail={true}
              tableColumns={generateXMLToolTableColumns(ssTable)}
              tableDataSource={generateXMLToolTableDataSoruce(ssTable)}
              tableMetadataEntity={tableMetadataEntity}
              tablePrimaryKeyName={tableMetadataEntity?.Key?.PropertyRef['@_Name']}
              tableName={tableMetadataEntity?.['@_Name']}
              onSave={handleGetSSTableData}></XmlToolTable> */}

            <DynamicTableComponent
              canNavigateToDetailPage={true}
              isEditableTable={true}
              tableColumns={generateDynamicTableColumns(ssTable, [])}
              tableDataSource={generateDynamicTableDataSoruce(ssTable)}
              tableMetadataEntity={tableMetadataEntity}
              tablePrimaryKeyName={tableMetadataEntity?.Key?.PropertyRef['@_Name']}
              tableName={tableMetadataEntity?.['@_Name']}
              onSave={handleOnSaveXmlToolTable}></DynamicTableComponent>

            {/* <DynamicTableComponent
              tableColumns={generateXMLToolTableColumns(ssTable)}
              tableDataSource={generateXMLToolTableDataSoruce(ssTable)}
              tableMetadataEntity={tableMetadataEntity}
              tablePrimaryKeyName={tableMetadataEntity?.Key?.PropertyRef['@_Name']}
              tableName={tableMetadataEntity?.['@_Name']}
              onSave={handleGetSSTableData}></DynamicTableComponent> */}
          </>
        )}
        {filterSSTable && (
          <SPPagination
            getItems={handleGetSSTableData}
            setItems={setSSTable}
            items={ssTable}
            loading={loadingSSTable}
            setLoading={setLoadingSSTable}></SPPagination>
        )}
        {ssTable.length > 0 && (
          <ExcelHandler
            dataToExport={ssTable}
            dataTableName={tableMetadataEntity?.['@_Name'] || null}
            onImport={handleImport}
            onSave={handleConfirmImportExcel}></ExcelHandler>
        )}
      </Form>
    </div>
  )
}

export default XmlToolPage
