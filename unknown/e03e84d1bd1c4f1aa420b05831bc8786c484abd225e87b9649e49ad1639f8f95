import React from 'react'
import { Form, Select } from 'antd'
import PropTypes from '../../common/PropTypes'
import { XML_GATE_STATUS } from '../Tool/XmlConstant'

const propsTypes = {
  form: PropTypes.object,
}

function PortalSubmissionDrawerFilter() {
  return (
    <>
      <Form.Item
        label="Trạng thái cổng"
        name="gate_status"
        initialValue={XML_GATE_STATUS.WAITING_SUBMIT.key}>
        <Select allowClear placeholder="Chọn trạng thái">
          {Object.values(XML_GATE_STATUS).map((status) => (
            <Select.Option key={status.key} value={status.key}>
              {status.nameL}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
    </>
  )
}

PortalSubmissionDrawerFilter.propTypes = propsTypes

export default PortalSubmissionDrawerFilter
