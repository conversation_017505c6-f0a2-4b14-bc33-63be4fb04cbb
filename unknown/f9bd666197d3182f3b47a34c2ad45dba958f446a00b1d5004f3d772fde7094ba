// This is a custom service worker that will be imported by the main service worker

// Disable all console logs
const log = () => {}
const logError = () => {}

// Handle push notifications
self.addEventListener('push', (event) => {
  log('[Service Worker] Push Received')

  let payload = {}

  try {
    // Parse the JSON payload
    payload = event.data.json()
    log('[Service Worker] Notification payload:', payload)
  } catch (e) {
    logError('[Service Worker] Error parsing notification payload:', e)
    // If the data is not JSON, use it as the notification body
    payload = {
      notification: {
        title: 'FVH-SI Notification',
        body: event.data ? event.data.text() : 'No payload',
        icon: '/pwa-192x192.png',
        badge: '/pwa-192x192.png',
      },
    }
  }

  // Extract notification data from the payload structure sent by the server
  // The server sends: { notification: { title, body, icon, data, actions, ... } }
  const notificationData = payload.notification || payload

  // Make sure we have the correct base URL for icons
  let iconUrl = notificationData.icon || '/pwa-192x192.png'
  if (iconUrl.startsWith('/') && !iconUrl.startsWith('/si/')) {
    // Add the /si/ prefix if it's missing and the path starts with /
    iconUrl = '/si' + iconUrl
  }

  const title = notificationData.title || 'FVH-SI Notification'
  const options = {
    body: notificationData.body || 'Something has happened!',
    icon: iconUrl,
    badge: notificationData.badge || '/pwa-192x192.png',
    data: notificationData.data || {},
    vibrate: [100, 50, 100],
    requireInteraction: notificationData.requireInteraction || false,
    actions: notificationData.actions || [],
  }

  // Log the notification options for debugging
  log('[Service Worker] Notification options:', options)

  event.waitUntil(self.registration.showNotification(title, options))
})

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  log('[Service Worker] Notification click received', event)

  // Close the notification
  event.notification.close()

  // Handle action clicks
  const action = event.action
  log('[Service Worker] Action clicked:', action)

  // Get the notification data
  const notificationData = event.notification.data
  log('[Service Worker] Notification data:', notificationData)

  // Determine the URL to open
  let url = '/'

  // Check different possible locations for the URL based on the server's payload structure
  if (notificationData) {
    // First priority: direct url property
    if (notificationData.url) {
      url = notificationData.url
    }
    // Second priority: url in data object
    else if (notificationData.data && notificationData.data.url) {
      url = notificationData.data.url
    }
  }

  // If this is an 'open' action, make sure we use the URL
  if (action === 'open') {
    // For 'open' action, we prioritize the URL in the data object
    // This matches the server's structure: { notification: { data: { url: '...' } } }
    if (notificationData && notificationData.data && notificationData.data.url) {
      url = notificationData.data.url
    } else if (notificationData && notificationData.url) {
      url = notificationData.url
    }
  }

  // Special handling for default action (when notification is clicked directly)
  if (!action && notificationData) {
    // When notification is clicked directly (not an action button),
    // prioritize the URL in the data object
    if (notificationData.data && notificationData.data.url) {
      url = notificationData.data.url
    } else if (notificationData.url) {
      url = notificationData.url
    }
  }

  // Make sure the URL is absolute
  if (url && !url.startsWith('http') && !url.startsWith('/')) {
    url = '/' + url
  }

  // If the URL is relative, make it absolute by adding the origin
  if (url && url.startsWith('/')) {
    // Get the origin from the registration scope or use a default
    const origin = self.registration.scope.replace(/\/$/g, '') || 'https://portal.fvhospital.com'
    url = origin + url
  }

  log('[Service Worker] Opening URL:', url)

  event.waitUntil(
    clients
      .matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        // Check if there's already a window/tab open with the target URL
        for (const client of clientList) {
          if (client.url === url && 'focus' in client) {
            log(client.url, '===', url)
            return client.focus()
          }
        }

        // If no window/tab is open with the URL, open a new one
        if (clients.openWindow) {
          log('[Service Worker] Opening new window with URL:', url)
          return clients.openWindow(url)
        }
      })
      .catch((error) => {
        logError('[Service Worker] Error opening URL:', error)
        // Fallback: try to open the URL directly
        return clients.openWindow(url)
      }),
  )
})

// Handle notification close
self.addEventListener('notificationclose', (event) => {
  log('[Service Worker] Notification closed', event)
})

// Handle messages from the client
self.addEventListener('message', (event) => {
  log('[Service Worker] Message received from client:', event.data)

  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
})
