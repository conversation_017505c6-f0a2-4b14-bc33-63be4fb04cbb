// import { getMetadataService } from '../../common/services'

import { handleError } from '../common/helpers'
import lists from '../common/lists'
import { getItemsService } from '../common/services'

// export const handleMetadataService = async () => {
//   const response = await getMetadataService()
//   return response.data
// }

export const getSystemSettingTableColumnList = async () => {
  try {
    let filter = `(active_flag eq true)`
    let data = await getItemsService(lists.system_setting_table_columns, {
      filter: filter,
      orderBy: 'col_seq_display asc',
    })
    //  console.log('Data getDatabankFieldAppMapping fetched: ', data) // Check if data is returned correctly

    return data.value
    // dispatch(commonActions.setFieldList(data.value))
  } catch (error) {
    handleError(error)
  }
}
