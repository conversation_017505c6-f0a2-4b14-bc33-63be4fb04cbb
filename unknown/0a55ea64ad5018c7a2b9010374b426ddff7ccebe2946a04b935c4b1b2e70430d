import { useEffect, useRef } from 'react'

export function useRunOnNewValues(callback, values) {
  const seenKeys = useRef(new Set())

  // Convert mảng giá trị thành key dạng chuỗi (đ<PERSON><PERSON> giản, d<PERSON> so sánh)
  const key = JSON.stringify(values)

  useEffect(() => {
    if (values.some((v) => v === undefined || v === null)) return
    if (seenKeys.current.has(key)) return

    seenKeys.current.add(key)
    callback(...values)
  }, [key])
}
