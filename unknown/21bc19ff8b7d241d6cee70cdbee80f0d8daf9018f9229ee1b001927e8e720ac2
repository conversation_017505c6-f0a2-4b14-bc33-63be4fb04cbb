import React from 'react'
import { Select } from 'antd'
import { useMappingState } from './useMappingState'
import BaseMappingComponent from './BaseMappingComponent'
import { renderTechnicalService, renderItem, renderStatus, renderDate } from './MappingConstant'
import {
  getTechnicalServicesMappings,
  deleteTechnicalServicesMapping,
  getTechnicalServices,
  createTechnicalServicesMapping,
  updateTechnicalServicesMapping,
  searchApiItems,
  checkDuplicateTechnicalServicesMapping,
} from './mappingService'
import lists from '../../common/lists'

const { Option } = Select

const TechnicalServicesMapping = () => {
  // Use the shared mapping state hook
  const mappingState = useMappingState({
    entityType: lists.technical_services.listName,
    getItems: getTechnicalServices,
    getMappings: getTechnicalServicesMappings,
    createMapping: createTechnicalServicesMapping,
    updateMapping: updateTechnicalServicesMapping,
    deleteMapping: deleteTechnicalServicesMapping,
    checkDuplicateMapping: checkDuplicateTechnicalServicesMapping,
    idField: 'technical_services_his_mapping_id',
    defaultFormValues: {
      active_flag: true,
    },
  })

  // Define table columns
  const columns = [
    {
      title: 'Technical Service ID',
      dataIndex: 'technical_services_id',
      key: 'technical_services_id',
      render: renderTechnicalService,
    },
    {
      title: 'Item ID',
      dataIndex: 'item_id',
      key: 'item_id',
      render: renderItem,
    },
    {
      title: 'Status',
      dataIndex: 'active_flag',
      key: 'active_flag',
      render: renderStatus,
    },
    {
      title: 'Last Updated',
      dataIndex: 'lu_updated',
      key: 'lu_updated',
      render: renderDate,
    },
  ]

  // Render entity option for select
  const renderTechnicalServiceOption = (service) => (
    <Option
      key={service.technical_services_id}
      value={service.technical_services_id}
      label={`${service.MA_TUONG_DUONG} - ${service.TEN_DVKT_PHEDUYET}`}>
      {service.MA_TUONG_DUONG} - {service.TEN_DVKT_PHEDUYET}
    </Option>
  )

  // Render item option for select
  const renderItemOption = (item) => (
    <Option key={item.item_id} value={item.item_id} label={`${item.item_code} - ${item.name_e}`}>
      {item.item_code} - {item.name_e}
    </Option>
  )

  return (
    <BaseMappingComponent
      title="Technical Services Mapping"
      entityType={lists.technical_services.listName}
      entityLabel="Technical Service"
      entityField="technical_services_id"
      idField="technical_services_his_mapping_id"
      mappingState={mappingState}
      columns={columns}
      renderEntityOption={renderTechnicalServiceOption}
      renderItemOption={renderItemOption}
      entitySearchHandler={getTechnicalServices}
      itemSearchHandler={searchApiItems}
      showRefreshButton={true}
    />
  )
}

export default TechnicalServicesMapping
