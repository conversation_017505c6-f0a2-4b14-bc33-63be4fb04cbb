import { defineConfig } from 'eslint/config'
import js from '@eslint/js'
import pluginReact from 'eslint-plugin-react'
import pluginPrettier from 'eslint-plugin-prettier'
import pluginPrettierRecommended from 'eslint-plugin-prettier/recommended'
import babelParser from '@babel/eslint-parser'
import babelPluginSyntaxImportAssertions from '@babel/plugin-syntax-import-assertions'
import globals from 'globals'

export default defineConfig([
  {
    ignores: ['node_modules/', 'dist/', '**/*.html', 'public/', 'src/tests/'],
  },
  {
    files: ['**/*.{js,jsx}'],
    plugins: {
      js,
      react: pluginReact,
      prettier: pluginPrettier,
    },
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      parser: babelParser,
      parserOptions: {
        requireConfigFile: false,
        babelOptions: {
          plugins: [babelPluginSyntaxImportAssertions, '@babel/plugin-syntax-jsx'],
        },
      },
      globals: globals.browser,
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
    extends: [pluginPrettierRecommended],
    rules: {
      ...js.configs.recommended.rules,
      ...pluginReact.configs.recommended.rules,
      'react/react-in-jsx-scope': 'off',
      'react/display-name': 'off',
      'no-console': 'warn',
      'no-unused-vars': 'warn',
      'react/prop-types': 0,
    },
  },
])
