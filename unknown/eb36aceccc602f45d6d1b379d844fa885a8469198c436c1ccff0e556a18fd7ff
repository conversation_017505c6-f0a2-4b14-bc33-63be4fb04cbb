import { useState, useMemo } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import debounce from 'lodash/debounce'
import { v4 as uuidv4 } from 'uuid'
import { handleError } from '../../common/helpers'
import { useUI } from '../../common/UIProvider'

/**
 * Custom hook for managing mapping component state and operations
 *
 * @param {Object} options - Configuration options
 * @param {string} options.entityType - The type of entity being mapped (e.g., 'medicalSupplies', 'medicine', 'technicalServices')
 * @param {Function} options.getItems - Function to fetch items
 * @param {Function} options.getMappings - Function to fetch mappings
 * @param {Function} options.createMapping - Function to create a mapping
 * @param {Function} options.updateMapping - Function to update a mapping
 * @param {Function} options.deleteMapping - Function to delete a mapping
 * @param {Function} options.checkDuplicateMapping - Function to check for duplicate mappings
 * @param {string} options.idField - The ID field name for the mapping (e.g., 'medical_supplies_his_mapping_id')
 * @param {Object} options.defaultFormValues - Default values for the form
 * @returns {Object} - State and functions for the mapping component
 */
export const useMappingState = ({
  entityType,
  getItems,
  getMappings,
  createMapping,
  updateMapping,
  deleteMapping,
  checkDuplicateMapping,
  idField,
  defaultFormValues = {},
}) => {
  const ui = useUI()
  const queryClient = useQueryClient()

  // UI state
  const [searchFilter, setSearchFilter] = useState(null)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [currentMapping, setCurrentMapping] = useState(null)
  const [modalMode, setModalMode] = useState('create')
  const [filterType, setFilterType] = useState('none')

  // Search state
  const [searchText, setSearchText] = useState('')
  const [selectOptions, setSelectOptions] = useState([])
  const [isSearching, setIsSearching] = useState(false)

  // Debounced search function
  const debouncedSearch = useMemo(() => debounce((value) => setSearchText(value), 500), [])

  // Debounced mapping ID search
  const debouncedMappingIdSearch = useMemo(
    () =>
      debounce((value) => {
        if (value.trim()) {
          setSearchFilter({ mapping_id: value.trim() })
        } else {
          setSearchFilter(null)
        }
      }, 500),
    [],
  )

  // Query to fetch mappings with search filter
  const { data: mappingsData, isLoading } = useQuery({
    queryKey: [`${entityType}Mappings`, searchFilter],
    queryFn: () => {
      let filter = []

      if (searchFilter?.active_flag !== undefined) {
        filter.push(`active_flag eq ${searchFilter.active_flag}`)
      }

      // If searching by mapping ID
      if (searchFilter?.mapping_id) {
        filter.push(`${idField} eq ${searchFilter.mapping_id}`)
      }
      // If searching by entity ID (e.g., medicine_id, technical_services_id)
      else if (searchFilter?.entity_id) {
        filter.push(`${entityType}_id eq ${searchFilter.entity_id}`)
      }
      // If searching by item ID
      else if (searchFilter?.item_id) {
        filter.push(`item_id eq ${searchFilter.item_id}`)
      }

      if (filter.length > 0) {
        return getMappings({
          filter: filter.join(' and '),
        })
      }

      // Default - no filter
      return getMappings()
    },
  })

  // Query to fetch items for dropdown with search
  const { data: itemsData, isLoading: isLoadingItems } = useQuery({
    queryKey: [entityType, searchText],
    queryFn: () => getItems({ searchText }),
  })

  // Mutation for deleting a mapping
  const deleteMutation = useMutation({
    mutationFn: (mappingId) => deleteMapping(mappingId),
    onSuccess: () => {
      ui.notiSuccess('Mapping deleted successfully')
      queryClient.invalidateQueries({ queryKey: [`${entityType}Mappings`] })
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to delete mapping')
    },
  })

  // Mutation for creating a mapping
  const createMutation = useMutation({
    mutationFn: (mapping) => createMapping(mapping),
    onSuccess: () => {
      ui.notiSuccess('Mapping created successfully')
      queryClient.invalidateQueries({ queryKey: [`${entityType}Mappings`] })
      setIsModalVisible(false)
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to create mapping')
    },
  })

  // Mutation for updating a mapping
  const updateMutation = useMutation({
    mutationFn: ({ mappingId, mapping }) => updateMapping(mappingId, mapping),
    onSuccess: () => {
      ui.notiSuccess('Mapping updated successfully')
      queryClient.invalidateQueries({ queryKey: [`${entityType}Mappings`] })
      setIsModalVisible(false)
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to update mapping')
    },
  })

  // Use the data directly from the API response
  const mappings = mappingsData?.value || []

  // Handle adding or editing a mapping
  const handleAddEdit = (record) => {
    setModalMode(record ? 'edit' : 'create')
    setCurrentMapping(record)

    // Reset form and set values if editing
    if (record) {
      return {
        ...defaultFormValues,
        ...record,
      }
    }

    return defaultFormValues
  }

  // Handle deleting a mapping
  const handleDelete = (mappingId) => {
    deleteMutation.mutate(mappingId)
  }

  // Check for duplicate mappings using API
  const checkForDuplicates = async (values) => {
    try {
      // Get the current mapping ID if in edit mode
      const currentMappingId = modalMode === 'edit' ? currentMapping[idField] : null

      // Check for duplicates using the API
      const result = await checkDuplicateMapping(values, currentMappingId)

      if (result.isDuplicate && result.duplicateMapping) {
        return {
          isDuplicate: true,
          duplicateMapping: result.duplicateMapping,
        }
      }

      return { isDuplicate: false }
    } catch (error) {
      handleError(error)
      ui.notiError('Error checking for duplicate mappings')
      return { isDuplicate: true, error } // Prevent form submission on error
    }
  }

  // Handle form submission with duplicate checking
  const handleSubmit = async (values) => {
    try {
      // Check for duplicate mappings
      const { isDuplicate } = await checkForDuplicates(values)
      if (isDuplicate) {
        ui.notiError('A mapping with this combination already exists')
        return false
      }

      if (modalMode === 'create') {
        createMutation.mutate({
          [idField]: uuidv4(),
          ...values,
          active_flag: values.active_flag ?? true,
          lu_updated: new Date().toISOString(),
        })
      } else {
        updateMutation.mutate({
          mappingId: currentMapping[idField],
          mapping: {
            ...values,
            lu_updated: new Date().toISOString(),
          },
        })
      }

      return true
    } catch (error) {
      handleError(error, 'Form submission')
      return false
    }
  }

  return {
    // State
    searchFilter,
    setSearchFilter,
    isModalVisible,
    setIsModalVisible,
    currentMapping,
    setCurrentMapping,
    modalMode,
    setModalMode,
    filterType,
    setFilterType,
    searchText,
    setSearchText,
    selectOptions,
    setSelectOptions,
    isSearching,
    setIsSearching,

    // Data
    mappings,
    itemsData,
    isLoading,
    isLoadingItems,

    // Functions
    debouncedSearch,
    debouncedMappingIdSearch,
    handleAddEdit,
    handleDelete,
    handleSubmit,
    checkForDuplicates,

    // Mutations
    createMutation,
    updateMutation,
    deleteMutation,
  }
}
