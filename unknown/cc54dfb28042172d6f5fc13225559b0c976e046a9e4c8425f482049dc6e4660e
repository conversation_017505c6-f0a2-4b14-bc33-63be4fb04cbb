import { defineConfig, loadEnv } from 'vite'
import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react-swc'
import { VitePWA } from 'vite-plugin-pwa'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load the environment variables based on the current mode (e.g., production)
  const env = loadEnv(mode, '.')

  return {
    plugins: [
      react(),
      tailwindcss(),
      VitePWA({
        registerType: 'autoUpdate',
        includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'masked-icon.svg'],
        // Use generateSW strategy instead of injectManifest
        strategies: 'generateSW',
        manifest: {
          name: 'FVH - SI',
          short_name: 'FVH-SI',
          description: 'FVH SI Application',
          theme_color: '#5ac2dc',
          background_color: '#ffffff',
          icons: [
            {
              src: 'pwa-192x192.png',
              sizes: '192x192',
              type: 'image/png',
            },
            {
              src: 'pwa-512x512.png',
              sizes: '512x512',
              type: 'image/png',
            },
            {
              src: 'pwa-512x512.png',
              sizes: '512x512',
              type: 'image/png',
              purpose: 'any maskable',
            },
          ],
          display: 'standalone',
          start_url: env.VITE_HOME_PAGE || '/',
        },
        workbox: {
          // Increase the maximum file size that can be precached (default is 2MB)
          maximumFileSizeToCacheInBytes: 10 * 1024 * 1024, // 10MB
          // Add custom service worker code
          additionalManifestEntries: [{ url: 'sw-custom.js', revision: '1' }],
          swDest: 'sw.js',
          // Add event listeners for push notifications
          skipWaiting: true,
          clientsClaim: true,
          cleanupOutdatedCaches: true,
          // Disable Workbox logs
          disableDevLogs: true,
          // Add custom service worker code
          importScripts: ['sw-custom.js'],
          runtimeCaching: [
            {
              urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
              handler: 'CacheFirst',
              options: {
                cacheName: 'google-fonts-cache',
                expiration: {
                  maxEntries: 10,
                  maxAgeSeconds: 60 * 60 * 24 * 365, // <== 365 days
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
            {
              urlPattern: /^https:\/\/fonts\.gstatic\.com\/.*/i,
              handler: 'CacheFirst',
              options: {
                cacheName: 'gstatic-fonts-cache',
                expiration: {
                  maxEntries: 10,
                  maxAgeSeconds: 60 * 60 * 24 * 365, // <== 365 days
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
            // Add caching for API requests
            {
              urlPattern: new RegExp('^' + env.VITE_BASE_URL),
              handler: 'NetworkFirst',
              options: {
                cacheName: 'api-cache',
                expiration: {
                  maxEntries: 100,
                  maxAgeSeconds: 60 * 60, // 1 hour
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
            // Cache other static assets
            {
              urlPattern: /\.(js|css|png|jpg|jpeg|svg|gif)$/,
              handler: 'StaleWhileRevalidate',
              options: {
                cacheName: 'static-resources',
                expiration: {
                  maxEntries: 100,
                  maxAgeSeconds: 60 * 60 * 24 * 7, // 7 days
                },
              },
            },
          ],
        },
        devOptions: {
          enabled: true,
          type: 'module',
        },
      }),
    ],
    base: env.VITE_HOME_PAGE, // This sets the base URL of the application
  }
})
