import React from 'react'
import { Form, Input, Button, message } from 'antd'

const DynamicFormExample = () => {
  const [form] = Form.useForm()

  const handleGetFieldsValue = () => {
    // Get the current values of the form fields
    const values = form.getFieldsValue()

    // Optionally display the values
    message.success(`Form Values: ${JSON.stringify(values)}`)
  }

  return (
    <Form form={form} name="dynamic_form" initialValues={{ users: [] }}>
      <Form.List name="users">
        {(fields, { add, remove }) => (
          <>
            {fields.map((field) => (
              <div key={field.key} style={{ marginBottom: '10px', display: 'flex', gap: '10px' }}>
                <Form.Item
                  name={[field.name, 'firstName']}
                  fieldKey={[field.fieldKey, 'firstName']}
                  rules={[{ required: true, message: 'First name is required' }]}
                  style={{ flex: 1 }}>
                  <Input placeholder="First Name" />
                </Form.Item>
                <Form.Item
                  name={[field.name, 'lastName']}
                  fieldKey={[field.fieldKey, 'lastName']}
                  rules={[{ required: true, message: 'Last name is required' }]}
                  style={{ flex: 1 }}>
                  <Input placeholder="Last Name" />
                </Form.Item>
                <Button onClick={() => remove(field.name)} type="link" danger>
                  Remove
                </Button>
              </div>
            ))}
            <Button onClick={() => add()} type="dashed" style={{ marginBottom: '10px' }}>
              Add User
            </Button>
          </>
        )}
      </Form.List>
      <Button type="primary" onClick={handleGetFieldsValue} style={{ marginTop: '10px' }}>
        Get Fields Value
      </Button>
    </Form>
  )
}

export default DynamicFormExample
