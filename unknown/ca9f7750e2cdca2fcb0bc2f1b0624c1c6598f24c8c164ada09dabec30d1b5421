import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import {
  getUserGroupMembershipsByUserId,
  getGroupPermissionMappingsByGroupId,
} from '../../../services/permissionService'
import { useQuery } from '@tanstack/react-query'
import { MODULE_AUTH } from '../../../store/auth'

// Custom hook to check if the current user has a specific permission
export const usePermission = (permissionCode) => {
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])
  const userId = currentUser?.User_id

  // Query to get user's group memberships
  const membershipQuery = useQuery({
    queryKey: ['userGroupMemberships', userId],
    queryFn: async () => {
      if (!userId) return { value: [] }
      return await getUserGroupMembershipsByUserId(userId)
    },
    enabled: !!userId,
  })

  // Extract user group IDs from memberships
  const userGroupIds = membershipQuery.data?.value?.map((m) => m.user_group_id) || []

  // Query to get permissions for each group
  const permissionsQueries = useQuery({
    queryKey: ['groupPermissions', userGroupIds, permissionCode],
    queryFn: async () => {
      if (!userGroupIds.length) return []

      // Get permissions for each group
      const permissionPromises = userGroupIds.map((groupId) =>
        getGroupPermissionMappingsByGroupId(groupId),
      )

      const results = await Promise.all(permissionPromises)
      return results.flatMap((result) => result.value || [])
    },
    enabled: userGroupIds.length > 0,
  })

  // Check if the user has the specific permission
  const hasPermission =
    permissionsQueries.data?.some(
      (mapping) => mapping.permission_rcd === permissionCode && mapping.active_flag,
    ) || false

  return {
    hasPermission,
    isLoading: membershipQuery.isLoading || permissionsQueries.isLoading,
    error: membershipQuery.error || permissionsQueries.error,
  }
}

// Hook to get all permissions for the current user
export const useUserPermissions = () => {
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])
  const userId = currentUser?.User_id
  const [permissions, setPermissions] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchPermissions = async () => {
      if (!userId) {
        setPermissions([])
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        // Get user's group memberships
        const memberships = await getUserGroupMembershipsByUserId(userId)
        const userGroupIds = memberships.value?.map((m) => m.user_group_id) || []

        if (!userGroupIds.length) {
          setPermissions([])
          setIsLoading(false)
          return
        }

        // Get permissions for each group
        const permissionPromises = userGroupIds.map((groupId) =>
          getGroupPermissionMappingsByGroupId(groupId),
        )

        const results = await Promise.all(permissionPromises)
        const allPermissions = results.flatMap((result) => result.value || [])

        // Filter active permissions and remove duplicates
        const uniquePermissions = [
          ...new Map(
            allPermissions.filter((p) => p.active_flag).map((p) => [p.permission_rcd, p]),
          ).values(),
        ]

        setPermissions(uniquePermissions)
      } catch (err) {
        setError(err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchPermissions()
  }, [userId])

  return { permissions, isLoading, error }
}

// Simple permission check component
export const PermissionCheck = ({ permissionCode, children, fallback = null }) => {
  const { hasPermission, isLoading } = usePermission(permissionCode)

  if (isLoading) return null

  return hasPermission ? children : fallback
}
