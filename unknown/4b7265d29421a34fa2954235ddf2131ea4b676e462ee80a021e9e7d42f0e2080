import React, { useEffect, useState } from 'react'
import { Button, Form, Input, InputNumber, Popconfirm, Table, Typography } from 'antd'
import PropTypes from '../../../common/PropTypes'
import EditableCell from './EditableCellComponent'

const propTypes = {
  tableColumns: PropTypes.array.isRequired,
  tableDataSource: PropTypes.array.isRequired,
}

const DynamicTableComponentTest = ({ tableColumns, tableDataSource }) => {
  const [form] = Form.useForm()
  const [data, setData] = useState(tableDataSource)
  const handleGetFieldsValue = () => {
    // Get the current values of the form fields
    const values = form.getFieldsValue()

    // Optionally display the values
    // message.success(`Form Values: ${JSON.stringify(values)}`)
  }
  // Initialize the form with all data when the component mounts
  useEffect(() => {
    const initialFormValues = {}

    data.forEach((row) => {
      Object.keys(row).forEach((column) => {
        if (!initialFormValues[row.key]) {
          initialFormValues[row.key] = {}
        }
        initialFormValues[row.key][column] = row[column]
      })
    })
    form.setFieldsValue(initialFormValues)
  }, [data, form])

  // Prepare columns with editable cells
  const mergedColumns = tableColumns.map((col) => ({
    ...col,
    onCell: (record) => ({
      record,
      inputType: col.dataIndex === 'age' ? 'number' : 'text',
      dataIndex: col.dataIndex,
      title: col.title,
      editing: true, // All cells are editable
    }),
  }))

  // Save logic (updates the data state)
  const save = async (key) => {
    try {
      const updatedFields = await form.validateFields()
      const updatedData = data.map((row) => {
        if (row.key === key) {
          return { ...row, ...updatedFields[key] }
        }
        return row
      })
      setData(updatedData)
    } catch (errInfo) {
      //  console.log('Validate Failed:', errInfo)
    }
  }

  return (
    <Form form={form} component={false}>
      <Button onClick={console.log('first', form.getFieldsValue())}></Button>
      <Table
        size="small"
        components={{
          body: {
            cell: EditableCell,
          },
        }}
        bordered
        dataSource={data}
        columns={mergedColumns}
        rowClassName="editable-row"
        pagination={{
          onChange: () => {}, // Handle pagination if needed
        }}
      />
      <Button onClick={handleGetFieldsValue}></Button>
    </Form>
  )
}

DynamicTableComponentTest.propTypes = propTypes

export default DynamicTableComponentTest
