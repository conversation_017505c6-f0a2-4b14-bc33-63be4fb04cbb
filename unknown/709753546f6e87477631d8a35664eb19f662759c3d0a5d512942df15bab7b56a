import React from 'react'
import { Table } from 'antd'

const data = [
  {
    key: '1',
    name: '<PERSON>',
    age: 32,
    address: 'New York No. 1 Lake Park',
  },
  {
    key: '2',
    name: '<PERSON>',
    age: 42,
    address: 'London No. 1 Lake Park',
  },
  {
    key: '3',
    name: '<PERSON>',
    age: 29,
    address: 'Sidney No. 1 Lake Park',
  },
]

const App = () => {
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
      onCell: (record) => ({
        style: {
          backgroundColor: record.age > 30 ? '#f6ffed' : '#fff2f0', // Highlight based on age
        },
      }),
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
    },
  ]

  return <Table size="small" dataSource={data} columns={columns} />
}

export default App
