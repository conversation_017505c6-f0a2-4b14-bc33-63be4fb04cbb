import React, { useState } from 'react'
import { Tabs, Card, Typography } from 'antd'
import UserGroupList from './UserGroupList'
import PermissionList from './PermissionList'
import UserGroupMembership from './UserGroupMembership'
import GroupPermissionMapping from './GroupPermissionMapping'
import { usePermission } from './hooks/usePermission'

const { Title } = Typography

const PermissionManagement = () => {
  const [activeTab, setActiveTab] = useState('1')

  // Check if user has admin permissions
  const { hasPermission: isAdmin } = usePermission('ADMIN')

  const items = [
    {
      key: '1',
      label: 'User Groups',
      children: <UserGroupList />,
    },
    {
      key: '2',
      label: 'Permissions',
      children: <PermissionList />,
    },
    {
      key: '3',
      label: 'User Group Membership',
      children: <UserGroupMembership />,
    },
    {
      key: '4',
      label: 'Group Permission Mapping',
      children: <GroupPermissionMapping />,
    },
  ]

  return (
    <div style={{ padding: '20px' }}>
      <Card>
        <Title level={2}>Permission Management</Title>
        <Tabs
          slot="small"
          activeKey={activeTab}
          onChange={setActiveTab}
          items={items}
          type="card"
        />
      </Card>
    </div>
  )
}

export default PermissionManagement
