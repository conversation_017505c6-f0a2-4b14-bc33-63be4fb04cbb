import { autoRequestAndSubscribe } from '../services/notificationService'

// Mock the dependencies
jest.mock('../services/notificationService', () => {
  const originalModule = jest.requireActual('../services/notificationService')

  return {
    ...originalModule,
    checkNotificationSupport: jest.fn(),
    getNotificationPermissionStatus: jest.fn(),
    requestNotificationPermission: jest.fn(),
    subscribeToPushNotifications: jest.fn(),
    registerSubscriptionWithServer: jest.fn(),
    autoRequestAndSubscribe: originalModule.autoRequestAndSubscribe,
  }
})

// Import the mocked functions
import {
  checkNotificationSupport,
  getNotificationPermissionStatus,
  requestNotificationPermission,
  subscribeToPushNotifications,
} from '../services/notificationService'

describe('Notification Service', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks()

    // Setup global window object
    global.window = {
      swRegistration: {
        pushManager: {
          getSubscription: jest.fn(),
          subscribe: jest.fn(),
        },
      },
      isSubscribed: false,
    }
  })

  describe('autoRequestAndSubscribe', () => {
    it('should return early if notifications are not supported', async () => {
      // Setup
      checkNotificationSupport.mockReturnValue(false)

      // Execute
      const result = await autoRequestAndSubscribe('user123')

      // Verify
      expect(result).toEqual({ permissionGranted: false, subscribed: false })
      expect(checkNotificationSupport).toHaveBeenCalledTimes(1)
      expect(getNotificationPermissionStatus).not.toHaveBeenCalled()
    })

    it('should handle already granted permission and existing subscription', async () => {
      // Setup
      checkNotificationSupport.mockReturnValue(true)
      getNotificationPermissionStatus.mockReturnValue('granted')

      const mockSubscription = { endpoint: 'https://example.com/push' }
      global.window.swRegistration.pushManager.getSubscription.mockResolvedValue(mockSubscription)

      // Execute
      const result = await autoRequestAndSubscribe('user123')

      // Verify
      expect(result).toEqual({ permissionGranted: true, subscribed: true })
      expect(checkNotificationSupport).toHaveBeenCalledTimes(1)
      expect(getNotificationPermissionStatus).toHaveBeenCalledTimes(1)
      expect(global.window.isSubscribed).toBe(true)
    })

    it('should handle already granted permission but no subscription', async () => {
      // Setup
      checkNotificationSupport.mockReturnValue(true)
      getNotificationPermissionStatus.mockReturnValue('granted')

      // No existing subscription
      global.window.swRegistration.pushManager.getSubscription.mockResolvedValue(null)

      // Mock successful subscription
      const mockNewSubscription = { endpoint: 'https://example.com/push/new' }
      subscribeToPushNotifications.mockResolvedValue(mockNewSubscription)

      // Execute
      const result = await autoRequestAndSubscribe('user123')

      // Verify
      expect(result).toEqual({ permissionGranted: true, subscribed: true })
      expect(subscribeToPushNotifications).toHaveBeenCalledWith('user123')
    })

    it('should handle denied permission', async () => {
      // Setup
      checkNotificationSupport.mockReturnValue(true)
      getNotificationPermissionStatus.mockReturnValue('denied')

      // Execute
      const result = await autoRequestAndSubscribe('user123')

      // Verify
      expect(result).toEqual({ permissionGranted: false, subscribed: false })
      expect(requestNotificationPermission).not.toHaveBeenCalled()
    })

    it('should request permission if status is default', async () => {
      // Setup
      checkNotificationSupport.mockReturnValue(true)
      getNotificationPermissionStatus.mockReturnValue('default')
      requestNotificationPermission.mockResolvedValue(true)

      const mockNewSubscription = { endpoint: 'https://example.com/push/new' }
      subscribeToPushNotifications.mockResolvedValue(mockNewSubscription)

      // Execute
      const result = await autoRequestAndSubscribe('user123')

      // Verify
      expect(result).toEqual({ permissionGranted: true, subscribed: true })
      expect(requestNotificationPermission).toHaveBeenCalledTimes(1)
      expect(subscribeToPushNotifications).toHaveBeenCalledWith('user123')
    })

    it('should handle permission request rejection', async () => {
      // Setup
      checkNotificationSupport.mockReturnValue(true)
      getNotificationPermissionStatus.mockReturnValue('default')
      requestNotificationPermission.mockResolvedValue(false)

      // Execute
      const result = await autoRequestAndSubscribe('user123')

      // Verify
      expect(result).toEqual({ permissionGranted: false, subscribed: false })
      expect(requestNotificationPermission).toHaveBeenCalledTimes(1)
      expect(subscribeToPushNotifications).not.toHaveBeenCalled()
    })

    it('should handle errors gracefully', async () => {
      // Setup
      checkNotificationSupport.mockReturnValue(true)
      getNotificationPermissionStatus.mockImplementation(() => {
        throw new Error('Test error')
      })

      // Execute
      const result = await autoRequestAndSubscribe('user123')

      // Verify
      expect(result).toEqual({ permissionGranted: false, subscribed: false })
    })
  })
})
