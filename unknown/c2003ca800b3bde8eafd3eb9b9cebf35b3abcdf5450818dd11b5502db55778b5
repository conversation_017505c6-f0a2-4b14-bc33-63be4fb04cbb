import React from 'react'
import PropTypes from 'prop-types'
import { Form, Select, Spin } from 'antd'

const { Option } = Select

/**
 * Reusable form component for mapping modals
 */
const MappingForm = ({
  form,
  entityField,
  entityLabel,
  entityOptions,
  isLoadingEntity,
  entitySearchHandler,
  entityOptionRenderer,
  itemOptions,
  isSearchingItems,
  itemSearchHandler,
  itemOptionRenderer,
  additionalFields,
}) => {
  return (
    <Form form={form} layout="vertical">
      <Form.Item
        name={entityField}
        label={entityLabel}
        rules={[{ required: true, message: `Please select a ${entityLabel.toLowerCase()}` }]}>
        <Select
          placeholder={`Select a ${entityLabel.toLowerCase()}`}
          loading={isLoadingEntity}
          showSearch
          defaultActiveFirstOption={false}
          optionLabelProp="label"
          filterOption={false}
          onSearch={entitySearchHandler}
          notFoundContent={isLoadingEntity ? <Spin size="small" /> : 'Type to search'}>
          {entityOptions.map(entityOptionRenderer)}
        </Select>
      </Form.Item>

      <Form.Item
        name="item_id"
        label="Item ID"
        rules={[{ required: true, message: 'Please select an item' }]}>
        <Select
          placeholder="Select an item"
          loading={isSearchingItems}
          optionLabelProp="label"
          showSearch
          defaultActiveFirstOption={false}
          filterOption={false}
          onSearch={itemSearchHandler}
          notFoundContent={
            isSearchingItems ? <Spin size="small" /> : 'Type at least 2 characters to search'
          }>
          {itemOptions.map(itemOptionRenderer)}
        </Select>
      </Form.Item>

      {additionalFields}

      <Form.Item initialValue={true} name="active_flag" label="Status">
        <Select>
          <Option value={true}>Active</Option>
          <Option value={false}>Inactive</Option>
        </Select>
      </Form.Item>
    </Form>
  )
}

MappingForm.propTypes = {
  form: PropTypes.object.isRequired,
  entityField: PropTypes.string.isRequired,
  entityLabel: PropTypes.string.isRequired,
  entityOptions: PropTypes.array.isRequired,
  isLoadingEntity: PropTypes.bool.isRequired,
  entitySearchHandler: PropTypes.func.isRequired,
  entityOptionRenderer: PropTypes.func.isRequired,
  itemOptions: PropTypes.array.isRequired,
  isSearchingItems: PropTypes.bool.isRequired,
  itemSearchHandler: PropTypes.func.isRequired,
  itemOptionRenderer: PropTypes.func.isRequired,
  additionalFields: PropTypes.node,
}

export default MappingForm
