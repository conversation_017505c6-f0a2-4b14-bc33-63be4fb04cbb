// filepath: /c:/Users/<USER>/OneDrive - CMC TSSG/GitHub/FVH-SI/src/store/visitSlice.js
import { createSlice } from '@reduxjs/toolkit'
import { FORM_MODE } from '../../common/constant'

const initialState = {
  selectedInsuranceInvoiceDataList: [],
  selectedVisitDataList: [],
  systemReferralDispositionRefList: [],
  currentPatientVisitMappingViewDetail: {},
  visitDetailMode: FORM_MODE.edit,
  currentApiPatientVisitMedicalCodingViewList: [],
}
export const MODULE_VISIT = 'MODULE_VISIT'

export const visitSlice = createSlice({
  name: MODULE_VISIT,
  initialState,
  reducers: {
    setSelectedInsuranceData: (state, action) => {
      state.selectedInsuranceInvoiceDataList = action.payload
    },
    setSelectedVisitDataList: (state, action) => {
      state.selectedVisitDataList = action.payload
    },
    setSystemReferralDispositionRefList: (state, action) => {
      state.systemReferralDispositionRefList = action.payload
    },
    setCurrentPatientVisitMappingViewDetail: (state, action) => {
      state.currentPatientVisitMappingViewDetail = action.payload
    },
    setVisitDetailMode: (state, action) => {
      state.visitDetailMode = action.payload
    },
    setCurrentApiPatientVisitMedicalCodingViewList: (state, action) => {
      state.currentApiPatientVisitMedicalCodingViewList = action.payload
    },
  },
})

export const visitActions = { ...visitSlice.actions }
export const visitReducer = visitSlice.reducer
