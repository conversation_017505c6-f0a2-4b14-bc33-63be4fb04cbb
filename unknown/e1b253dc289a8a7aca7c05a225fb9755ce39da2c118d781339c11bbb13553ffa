import { Workbook } from 'exceljs'

const setCellValueByName = (workbook, name, value) => {
  const namedRanges = workbook.definedNames.getRanges(name)
  if (!namedRanges || namedRanges.ranges?.length === 0) {
    throw new Error(`Defined name "${name}" not found`)
  }

  const [sheetName, cellAddress] = namedRanges.ranges[0].split('!')
  const worksheet = workbook.getWorksheet(sheetName)

  worksheet.getCell(cellAddress).value = value
}

export const print = async () => {
  const workbook = new Workbook()
  // const worksheet = workbook.addWorksheet('Sheet1')
  const file = await fetch('/FormN learning.xlsx') // Đảm bảo file được đặt ở public hoặc truy cập được
  const arrayBuffer = await file.arrayBuffer()
  await workbook.xlsx.load(arrayBuffer)
  const worksheet = workbook.getWorksheet('Sheet1') // hoặc getWorksheet(1)

  // top values
  setCellValueByName(workbook, 'ss_mkv', 'ninh')
  setCellValueByName(workbook, 'ss_n01', 'ninh')

  // use 5 row for table
  const startTableRow = 25
  const needToUseRowNumber = 5
  for (let i = startTableRow; i < startTableRow + needToUseRowNumber; i++) {
    worksheet.getRow(i).hidden = false
  }

  // bottom values
  setCellValueByName(workbook, 'all_tt_bh_fund', 'ninh20000')

  const buffer2 = await workbook.xlsx.writeBuffer()
  const blob2 = new Blob([buffer2], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  })

  const link2 = document.createElement('a')
  link2.href = URL.createObjectURL(blob2)
  link2.download = 'FormN.xlsx'
  document.body.appendChild(link2)
  link2.click()
  document.body.removeChild(link2)
}
