import React, { useState, useMemo, useEffect } from 'react'
import { Form, Table } from 'antd'
import PropTypes from 'prop-types'
import EditableCell from '../EditableCellComponent'

const propTypes = {
  tableColumns: PropTypes.array.isRequired,
  tableDataSource: PropTypes.array.isRequired,
}

const DynamicTableComponentTest3 = ({ tableColumns, tableDataSource }) => {
  const [form] = Form.useForm()
  const [editingKey, setEditingKey] = useState(null) // Track the editing row

  const isEditing = (record) => record.key === editingKey

  const edit = (key) => {
    setEditingKey(key)
    form.setFieldsValue({ ...tableDataSource.find((item) => item.key === key) })
  }

  const cancel = () => {
    setEditingKey(null)
  }

  const save = async (key) => {
    try {
      const row = await form.validateFields()
      const newData = [...tableDataSource]
      const index = newData.findIndex((item) => item.key === key)
      if (index > -1) {
        const item = newData[index]
        newData.splice(index, 1, { ...item, ...row })
        setEditingKey(null)
      }
    } catch (errInfo) {
      console.error('Save failed:', errInfo)
    }
  }
  useEffect(() => {
    const fetchData = () => {
      const initialFormValues = tableDataSource.reduce((acc, row) => {
        acc[row.key] = { ...row }
        return acc
      }, {})
      form.setFieldsValue(initialFormValues)
    }
    fetchData()
  }, [])
  const mergedColumns = useMemo(
    () =>
      tableColumns.map((col) => {
        if (!col.editable) {
          return col
        }
        return {
          ...col,
          onCell: (record) => ({
            record,
            inputType: col.dataIndex === 'age' ? 'number' : 'text',
            dataIndex: col.dataIndex,
            title: col.title,
            editing: true,
          }),
        }
      }),
    [tableColumns, editingKey],
  )

  return (
    <Form form={form} component={false}>
      <Table
        size="small"
        components={{
          body: {
            cell: EditableCell,
          },
        }}
        bordered
        dataSource={tableDataSource}
        columns={mergedColumns}
        rowClassName="editable-row"
        pagination={{
          onChange: cancel,
        }}
      />
    </Form>
  )
}

DynamicTableComponentTest3.propTypes = propTypes

export default React.memo(DynamicTableComponentTest3)
