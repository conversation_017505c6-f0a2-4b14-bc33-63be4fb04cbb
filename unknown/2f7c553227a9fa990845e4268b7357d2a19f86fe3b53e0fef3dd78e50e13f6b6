/* Tùy chỉnh thanh cuộn cho trình duyệt WebKit (Chrome, Safari) */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px; /* <PERSON><PERSON> rộng của thanh cuộn */
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1; /* <PERSON><PERSON><PERSON> nền của track */
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #888; /* Màu của thanh cuộn */
  border-radius: 10px; /* Bo góc của thanh cuộn */
  border: 2px solid #f1f1f1; /* Viền của thanh cuộn */
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #555; /* Màu của thanh cuộn khi hover */
}

/* Tùy chỉnh thanh cuộn cho Firefox */
// .custom-scrollbar {
//   scrollbar-width: thin; /* Độ rộng của thanh cuộn */
//   scrollbar-color: #888 #f1f1f1; /* Màu của thanh cuộn và track */
// }

/* Tùy chỉnh thanh cuộn cho IE và Edge */
// .custom-scrollbar::-ms-scrollbar {
//   width: 8px;
// }
