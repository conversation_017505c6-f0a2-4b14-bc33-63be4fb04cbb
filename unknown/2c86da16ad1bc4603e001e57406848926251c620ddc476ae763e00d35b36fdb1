import { registerSW } from 'virtual:pwa-register'

// This is the service worker registration function
export const registerServiceWorker = () => {
  if ('serviceWorker' in navigator) {
    // Register the service worker
    const updateSW = registerSW({
      immediate: true, // Register the service worker immediately
      onNeedRefresh() {
        // This function is called when a new service worker is available
        // Silent operation - no console logs
        // You can show a notification to the user here
        if (confirm('New content available. Reload?')) {
          updateSW(true)
        }
      },
      onOfflineReady() {
        // This function is called when the app is ready to work offline
        // Silent operation - no console logs
      },
      onRegistered(registration) {
        // This function is called when the service worker is registered
        // Silent operation - no console logs

        // Store the registration globally for later use
        window.swRegistration = registration

        // Check for existing subscription
        checkSubscription(registration)

        // Listen for messages from the service worker
        navigator.serviceWorker.addEventListener('message', (_event) => {
          // Silent operation - no console logs
          // Handle messages from the service worker here
        })
      },
      onRegisterError(_error) {
        // Silent operation - no console logs
      },
    })

    return updateSW
  }
}

// Function to check if the user is already subscribed to push notifications
const checkSubscription = async (registration) => {
  try {
    const subscription = await registration.pushManager.getSubscription()
    window.isSubscribed = !(subscription === null)

    // Silent operation - no console logs

    // You can update UI based on subscription status here

    // If the user is already subscribed, store the subscription for later use
    if (subscription) {
      // Here you would typically send the subscription to your server
      // to store it for sending push notifications later
      // Silent operation - no console logs
    }
  } catch (_error) {
    // Silent operation - no console logs
  }
}

// Function to send a message to the service worker
export const sendMessageToSW = (message) => {
  if (navigator.serviceWorker.controller) {
    navigator.serviceWorker.controller.postMessage(message)
  }
}
