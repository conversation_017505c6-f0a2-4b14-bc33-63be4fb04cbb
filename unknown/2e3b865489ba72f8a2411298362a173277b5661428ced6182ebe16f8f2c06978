import { useQuery, useQueryClient } from '@tanstack/react-query'
import { getVisitTypeRef } from '../VisitService'
import { useEffect } from 'react'
import nProgress from 'nprogress'

export const VISIT_TYPE_REF_KEYS = {
  VISIT_TYPE_REF: 'visitTypeRef',
}

export const useVisitTypeRef = ({ enabled = true } = {}) => {
  const queryClient = useQueryClient()

  // keys
  const visitTypeRefKey = [VISIT_TYPE_REF_KEYS.VISIT_TYPE_REF]

  const visitTypeRefQuery = useQuery({
    queryKey: visitTypeRefKey,
    queryFn: () => getVisitTypeRef(),
    enabled: enabled,
  })

  const refetchVisitTypeRef = () => {
    queryClient.invalidateQueries({ queryKey: visitTypeRefKey })
  }

  const visitTypeRef = visitTypeRefQuery.data || []

  const isLoading = visitTypeRefQuery.isLoading
  const isError = visitTypeRefQuery.isError
  const isSuccess = visitTypeRefQuery.isSuccess
  const isFetching = visitTypeRefQuery.isFetching

  useEffect(() => {
    if (isFetching) {
      nProgress.start()
    } else {
      nProgress.done()
    }
  }, [isFetching])

  return {
    visitTypeRefQuery,
    visitTypeRef,
    refetchVisitTypeRef,
    isLoading,
    isError,
    isSuccess,
    isFetching,
  }
}
