import React, { createContext, useContext, useState, useRef, useEffect } from 'react'
import { createPortal } from 'react-dom'

const ResizeContext = createContext()

export const ResizeProvider = ({ children }) => {
  const [resizing, setResizing] = useState(false)
  const [guidePosition, setGuidePosition] = useState(0)
  const [portalContainer, setPortalContainer] = useState(null)

  // Create a ref for the portal container
  const portalRef = useRef(null)

  // Set up the portal container on mount
  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof document !== 'undefined') {
      // Create a div element for the portal if it doesn't exist
      if (!portalRef.current) {
        portalRef.current = document.createElement('div')
        portalRef.current.className = 'resize-portal'
        document.body.appendChild(portalRef.current)
      }

      setPortalContainer(portalRef.current)

      // Clean up on unmount
      return () => {
        if (portalRef.current && document.body.contains(portalRef.current)) {
          document.body.removeChild(portalRef.current)
        }
      }
    }
  }, [])

  // Render the resize elements in a portal
  const renderPortalContent = () => {
    if (!portalContainer) return null

    return createPortal(
      <>
        {/* Add overlay layer */}
        {resizing && <div className="resize-overlay" />}
        <div
          className="resize-guide"
          style={{
            left: guidePosition,
            opacity: resizing ? 1 : 0,
            pointerEvents: resizing ? 'none' : 'auto',
          }}
        />
      </>,
      portalContainer,
    )
  }

  return (
    <ResizeContext.Provider value={{ resizing, setResizing, guidePosition, setGuidePosition }}>
      {children}
      {renderPortalContent()}
    </ResizeContext.Provider>
  )
}

export const useResize = () => useContext(ResizeContext)
