import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from 'antd'
import { BellOutlined, BellFilled } from '@ant-design/icons'
import {
  requestNotificationPermission,
  subscribeToPushNotifications,
  unsubscribeFromPushNotifications,
  simulatePushNotification,
  checkNotificationSupport,
  getNotificationPermissionStatus,
  sendNotificationWithActions,
  autoRequestAndSubscribe,
} from '../../services/notificationService'
import { useUI } from '../UIProvider'
import config from '../config'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import { getItemsService } from '../services'
import lists from '../lists'
import CopyableTag from './CopyableTag'

const NotificationButton = () => {
  const [permissionGranted, setPermissionGranted] = useState(false)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [loading, setLoading] = useState(false)
  const [isSupported, setIsSupported] = useState(false)
  const [subscription, setSubscription] = useState(null)
  const ui = useUI()
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  // Check notification permission and auto-subscribe on component mount
  useEffect(() => {
    const checkPermissionAndSubscribe = async () => {
      const supported = checkNotificationSupport()
      setIsSupported(supported)

      if (supported) {
        const permissionStatus = getNotificationPermissionStatus()
        setPermissionGranted(permissionStatus === 'granted')
        setIsSubscribed(window.isSubscribed || false)

        // Auto-request permission and subscribe if user is logged in
        const userId = currentUser?.User_id
        if (userId) {
          try {
            setLoading(true)
            const result = await autoRequestAndSubscribe(userId)
            if (result.permissionGranted) {
              setPermissionGranted(true)
            }
            if (result.subscribed) {
              setIsSubscribed(true)
            }
          } catch {
            // Silent error handling for automatic subscription
          } finally {
            setLoading(false)
          }
        }
      }
    }

    checkPermissionAndSubscribe()
  }, [currentUser])

  const handleNotificationToggle = async () => {
    setLoading(true)

    try {
      if (!permissionGranted) {
        // Request permission
        const granted = await requestNotificationPermission()
        setPermissionGranted(granted)

        if (granted) {
          // Subscribe to push notifications
          const userId = currentUser?.User_id
          if (!userId) {
            ui.notiWarning('User ID not found', 'Please log in to enable notifications')
            setLoading(false)
            return
          }

          const subscription = await subscribeToPushNotifications(userId)
          setIsSubscribed(!!subscription)

          if (subscription) {
            ui.notiSuccess('Notifications Enabled', 'You will now receive notifications')

            // Send a test notification
            setTimeout(() => {
              simulatePushNotification('Notifications Enabled', {
                body: 'You will now receive notifications from FVH-SI',
                icon: config.HOME_PAGE + 'pwa-192x192.png',
              })
            }, 1000)
          }
        } else {
          ui.notiWarning(
            'Permission Denied',
            'Please enable notifications in your browser settings',
          )
        }
      } else if (isSubscribed) {
        // Unsubscribe
        const userId = currentUser?.User_id
        if (!userId) {
          ui.notiWarning('User ID not found', 'Please log in to manage notifications')
          setLoading(false)
          return
        }

        const success = await unsubscribeFromPushNotifications(userId)
        if (success) {
          setIsSubscribed(false)
          ui.notiInfo('Notifications Disabled', 'You will no longer receive notifications')
        }
      } else {
        // Subscribe
        const userId = currentUser?.User_id
        if (!userId) {
          ui.notiWarning('User ID not found', 'Please log in to enable notifications')
          setLoading(false)
          return
        }

        const subscription = await subscribeToPushNotifications(userId)
        setIsSubscribed(!!subscription)

        if (subscription) {
          ui.notiSuccess('Notifications Enabled', 'You will now receive notifications')

          // Send a test notification
          setTimeout(() => {
            simulatePushNotification('Notifications Enabled', {
              body: 'You will now receive notifications from FVH-SI',
              icon: config.HOME_PAGE + 'pwa-192x192.png',
            })
          }, 1000)
        }
      }
    } catch {
      // Show error notification to user
      ui.notiError('Error', 'Failed to manage notification settings')
    } finally {
      setLoading(false)
    }
  }

  // Function to test sending a notification
  const sendTestNotification = async () => {
    let firstVisit = await getItemsService(lists.patient_visit_mapping_view, {
      top: 1,
      orderBy: 'actual_visit_datetime desc',
    })
    firstVisit = firstVisit.value[0]

    const title = 'Có lượt khám cần xử lý'
    const options = {
      body: `Lượt khám của bệnh nhân ${firstVisit?.fullname} chờ được nhân viên BHYT xử lý`,
      icon: config.HOME_PAGE + 'pwa-192x192.png',
      data: {
        url:
          window.location.origin + `${config.HOME_PAGE}his/visit/${firstVisit?.patient_visit_id}`,
      },
    }

    if (window.swRegistration) {
      sendNotificationWithActions(title, options)
    } else {
      simulatePushNotification(title, options)
    }
  }

  // Don't render the button if notifications are not supported
  if (!isSupported) {
    return null
  }

  return (
    <div className="flex items-center gap-2 px-2">
      <Popover
        content={isSubscribed ? 'Click to disable notification' : 'Click to enable notification'}>
        <Button
          className="text-secondary"
          type="text"
          size="small"
          icon={isSubscribed ? <BellFilled /> : <BellOutlined />}
          onClick={handleNotificationToggle}
          loading={loading}>
          {isSubscribed ? 'Notification is ready' : 'Notification is disabled'}
        </Button>
      </Popover>

      {/* Popover to view subscription details */}
      {isSubscribed && (
        <Popover content="Notification subscription details">
          <Button
            onClick={async () => {
              const subscription = await getItemsService(lists.notification_subscription, {
                filter: `user_id eq ${currentUser?.User_id}`,
                orderBy: 'lu_updated desc',
              })

              setSubscription(subscription.value[0])
            }}
            size="small"
            className="text-secondary"
            type="text"
            icon={<i className="fas fa-info-circle ms-1"></i>}></Button>
        </Popover>
      )}

      <Modal
        title="Subscription Details"
        open={!!subscription}
        onCancel={() => setSubscription(null)}
        footer={null}>
        <div className="d-flex flex-column gap-2 pb-3">
          <div>
            Auth: <CopyableTag text={subscription?.auth} />
          </div>
          <div>
            P256DH: <CopyableTag text={subscription?.p256dh} />
          </div>
          <div>
            Endpoint: <CopyableTag text={subscription?.endpoint} />
          </div>
          <div>
            User ID: <CopyableTag text={subscription?.user_id} />
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default NotificationButton
