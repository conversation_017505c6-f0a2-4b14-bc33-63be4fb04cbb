// import { getMetadataService } from '../../common/services'

import { FORMAT_DATE } from '../../common/constant'
import { handleError } from '../../common/helpers'
import lists from '../../common/lists'
import {
  getItemsService,
  sendToPortalService,
  transInvoicesToTableXMLService,
  transInvoiceViewToTableXMLService,
} from '../../common/services'
import { convertBase64ToXml, downloadXMLFile } from '../../SI/helper'

// export const handleMetadataService = async () => {
//   const response = await getMetadataService()
//   return response.data
// }

export const getSystemSettingTableColumnListByXmlType = async (XmlType) => {
  try {
    let filter = `(active_flag eq true and xml_type eq '${XmlType}')`
    let data = await getItemsService(lists.system_setting_table_columns, {
      filter: filter,
      orderBy: 'col_seq_display asc',
    })
    //  // console.log('Data getDatabankFieldAppMapping fetched: ', data) // Check if data is returned correctly

    return data.value
    // dispatch(commonActions.setFieldList(data.value))
  } catch (error) {
    handleError(error)
  }
}

export const generateXMLTable = async (params, ui) => {
  ui.setLoading(true)
  // console.log('params', params)
  const {
    filterSSTable = 'ss_table_1',
    filterInvoiceNo,
    filterHN,
    filterVisitDate,
    patientVisitId,
  } = params
  try {
    let filterQuery = []
    if (filterInvoiceNo) {
      filterQuery.push(`invoice_no=${filterInvoiceNo}`)
    }
    if (filterHN) {
      filterQuery.push(`hn=${filterHN}`)
    }
    if (filterVisitDate?.[0]) {
      filterQuery.push(`visit_start=${filterVisitDate[0].format(FORMAT_DATE)}`)
    }
    if (filterVisitDate?.[1]) {
      filterQuery.push(`visit_end=${filterVisitDate[1].format(FORMAT_DATE)}`)
    }
    if (patientVisitId) {
      filterQuery.push(`patient_visit_id=${patientVisitId}`)
    }
    const response = await transInvoiceViewToTableXMLService(filterSSTable, filterQuery.join('&'))
    ui.notiSuccess(response?.message, 'Đang xử lí, vui lòng đợi')
  } catch (error) {
    handleError(error)
  }
  // setTimeout(() => {
  //   ui.setLoading(false)
  // }, 20000) // 30 seconds
}

export const generateXMLTableByInvoices = async (params, ui) => {
  ui.setLoading(true)
  // console.log('params', params)
  const { filterInvoiceNo, filterHN, patientVisitId } = params
  try {
    const body = { hn: filterHN, invoiceNos: filterInvoiceNo, patient_visit_id: patientVisitId }
    // console.log(body)
    const response = await transInvoicesToTableXMLService(body)
    ui.notiSuccess(response?.message, 'Đang xử lí, vui lòng đợi')
  } catch (error) {
    handleError(error)
  }
  // setTimeout(() => {
  //   ui.setLoading(false)
  // }, 20000) // 30 seconds
  // // ui.setLoading(false)
}

// add return response
export const sendXMLTableToThePortal = async (
  invoice_no,
  ui,
  main_gate = 0,
  downloadFile = true,
) => {
  ui.setLoading(true)
  // console.log(invoice_no)
  let response = {}
  let isSuccess = false
  let message = ''

  try {
    response = await sendToPortalService(invoice_no, main_gate)
    // console.log(response)
    if (downloadFile) {
      const data = await getItemsService(lists.tbl_file_tracking, {
        filter: `maGiaoDich eq '${response?.maGiaoDich}'`,
        top: 1,
      })
      const file_tracking = data.value[0]
      const xml_content = convertBase64ToXml(file_tracking?.file_content)
      downloadXMLFile(xml_content, file_tracking?.file_name)
    }

    !downloadFile && ui.notiSuccess(response?.thongDiep)

    isSuccess = true
    message = response?.thongDiep
  } catch (error) {
    handleError(error)
    !downloadFile && ui.notiError(response?.thongDiep)

    isSuccess = false
    message = response?.thongDiep || error.message
  }

  ui.setLoading(false)

  return {
    ...response,
    isSuccess,
    message,
  }
}

export const checkXML = (xml1List, ui) => {
  const invalidCHAN_DOAN_VAO = xml1List.find((row) => !row.CHAN_DOAN_VAO)
  const invalidMA_BENH_CHINH = xml1List.find((row) => !row.MA_BENH_CHINH)
  const invalidCHAN_DOAN_RV = xml1List.find((row) => !row.CHAN_DOAN_RV)
  const invalidMA_LOAI_RV = xml1List.find((row) => !row.MA_LOAI_RV)
  const invalidKET_QUA_DTRI = xml1List.find((row) => !row.KET_QUA_DTRI)

  if (invalidCHAN_DOAN_VAO) {
    ui.notiError('CHAN_DOAN_VAO không được để trống')
    return false
  }
  if (invalidMA_BENH_CHINH) {
    ui.notiError('MA_BENH_CHINH không được để trống')
    return false
  }
  if (invalidCHAN_DOAN_RV) {
    ui.notiError('CHAN_DOAN_RV không được để trống')
    return false
  }
  if (invalidMA_LOAI_RV) {
    ui.notiError('MA_LOAI_RV không được để trống')
    return false
  }
  if (invalidKET_QUA_DTRI) {
    ui.notiError('KET_QUA_DTRI không được để trống')
    return false
  }

  return true
}
