import { handleError } from '../../common/helpers'
import lists from '../../common/lists'
import {
  getItemsService,
  getItemService,
  addListItemService,
  updateListItemService,
  deleteListItemService,
} from '../../common/services'
import { v4 as uuidv4 } from 'uuid'
import { isUUID } from '../../SI/helper'

// Medical Supplies Mapping
export const getMedicalSuppliesMappings = async (options = {}) => {
  try {
    const filter = options.filter || '(active_flag eq true)'
    const orderBy = options.orderBy || 'lu_updated desc'
    const data = await getItemsService(lists.medical_supplies_his_mapping, {
      filter,
      orderBy,
      top: options.top || 1000,
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getMedicalSuppliesMappings')
    throw error
  }
}

export const getMedicalSuppliesMappingById = async (mappingId) => {
  try {
    const data = await getItemService(lists.medical_supplies_his_mapping, mappingId)
    return data
  } catch (error) {
    handleError(error, 'getMedicalSuppliesMappingById')
    throw error
  }
}

export const createMedicalSuppliesMapping = async (mapping) => {
  try {
    // Generate UUID if not provided
    if (!mapping.medical_supplies_his_mapping_id) {
      mapping.medical_supplies_his_mapping_id = uuidv4()
    }

    // Set active flag if not provided
    if (mapping.active_flag === undefined) {
      mapping.active_flag = true
    }

    // Set update timestamp if not provided
    if (!mapping.lu_updated) {
      mapping.lu_updated = new Date().toISOString()
    }

    const data = await addListItemService(lists.medical_supplies_his_mapping, mapping)
    return data
  } catch (error) {
    handleError(error, 'createMedicalSuppliesMapping')
    throw error
  }
}

export const updateMedicalSuppliesMapping = async (mappingId, mapping) => {
  try {
    // Set update timestamp
    mapping.lu_updated = new Date().toISOString()

    const data = await updateListItemService(lists.medical_supplies_his_mapping, mappingId, mapping)
    return data
  } catch (error) {
    handleError(error, 'updateMedicalSuppliesMapping')
    throw error
  }
}

export const deleteMedicalSuppliesMapping = async (mappingId) => {
  try {
    const data = await deleteListItemService(lists.medical_supplies_his_mapping, mappingId)
    return data
  } catch (error) {
    handleError(error, 'deleteMedicalSuppliesMapping')
    throw error
  }
}

// Check if a medical supplies mapping with the same combination of IDs already exists
export const checkDuplicateMedicalSuppliesMapping = async (values, currentMappingId = null) => {
  try {
    // Build filter to check for existing mappings with the same combination
    let filters = []

    // Add medical supplies filter (required)
    if (values.medical_supplies_id) {
      filters.push(`medical_supplies_id eq ${values.medical_supplies_id}`)
    } else {
      // If no medical supplies ID, there can't be a duplicate
      return { isDuplicate: false }
    }

    // Add item filter (required)
    if (values.item_id) {
      filters.push(`item_id eq ${values.item_id}`)
    } else {
      // If no item ID, there can't be a duplicate
      return { isDuplicate: false }
    }

    // Exclude current mapping when editing
    if (currentMappingId) {
      filters.push(`medical_supplies_his_mapping_id ne ${currentMappingId}`)
    }

    // Combine all filters
    const filter = filters.join(' and ')

    // Query for existing mappings with the same combination
    const data = await getItemsService(lists.medical_supplies_his_mapping, {
      filter,
      top: 1, // We only need to know if at least one exists
    })

    // Check if any duplicate was found
    const isDuplicate = data.value && data.value.length > 0

    return {
      isDuplicate,
      duplicateMapping: isDuplicate ? data.value[0] : null,
    }
  } catch (error) {
    handleError(error, 'checkDuplicateMedicalSuppliesMapping')
    throw error
  }
}

// Medicine Mapping
export const getMedicineMappings = async (options = {}) => {
  try {
    const filter = options.filter || '(active_flag eq true)'
    const orderBy = options.orderBy || 'lu_updated desc'
    const data = await getItemsService(lists.medicine_his_mapping, {
      filter,
      orderBy,
      top: options.top || 1000,
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getMedicineMappings')
    throw error
  }
}

export const getMedicineMappingById = async (mappingId) => {
  try {
    const data = await getItemService(lists.medicine_his_mapping, mappingId)
    return data
  } catch (error) {
    handleError(error, 'getMedicineMappingById')
    throw error
  }
}

export const createMedicineMapping = async (mapping) => {
  try {
    // Generate UUID if not provided
    if (!mapping.medicine_his_mapping_id) {
      mapping.medicine_his_mapping_id = uuidv4()
    }

    // Set active flag if not provided
    if (mapping.active_flag === undefined) {
      mapping.active_flag = true
    }

    // Set update timestamp if not provided
    if (!mapping.lu_updated) {
      mapping.lu_updated = new Date().toISOString()
    }

    const data = await addListItemService(lists.medicine_his_mapping, mapping)
    return data
  } catch (error) {
    handleError(error, 'createMedicineMapping')
    throw error
  }
}

export const updateMedicineMapping = async (mappingId, mapping) => {
  try {
    // Set update timestamp
    mapping.lu_updated = new Date().toISOString()

    const data = await updateListItemService(lists.medicine_his_mapping, mappingId, mapping)
    return data
  } catch (error) {
    handleError(error, 'updateMedicineMapping')
    throw error
  }
}

export const deleteMedicineMapping = async (mappingId) => {
  try {
    const data = await deleteListItemService(lists.medicine_his_mapping, mappingId)
    return data
  } catch (error) {
    handleError(error, 'deleteMedicineMapping')
    throw error
  }
}

// Check if a medicine mapping with the same combination of IDs already exists
export const checkDuplicateMedicineMapping = async (values, currentMappingId = null) => {
  try {
    // Build filter to check for existing mappings with the same combination
    let filters = []

    // Add medicine filter (required)
    if (values.medicine_id) {
      filters.push(`medicine_id eq ${values.medicine_id}`)
    } else {
      // If no medicine ID, there can't be a duplicate
      return { isDuplicate: false }
    }

    // Add item filter (required)
    if (values.item_id) {
      filters.push(`item_id eq ${values.item_id}`)
    } else {
      // If no item ID, there can't be a duplicate
      return { isDuplicate: false }
    }

    // Exclude current mapping when editing
    if (currentMappingId) {
      filters.push(`medicine_his_mapping_id ne ${currentMappingId}`)
    }

    // Combine all filters
    const filter = filters.join(' and ')

    // Query for existing mappings with the same combination
    const data = await getItemsService(lists.medicine_his_mapping, {
      filter,
      top: 1, // We only need to know if at least one exists
    })

    // Check if any duplicate was found
    const isDuplicate = data.value && data.value.length > 0

    return {
      isDuplicate,
      duplicateMapping: isDuplicate ? data.value[0] : null,
    }
  } catch (error) {
    handleError(error, 'checkDuplicateMedicineMapping')
    throw error
  }
}

// Technical Services Mapping
export const getTechnicalServicesMappings = async (options = {}) => {
  try {
    const filter = options.filter || '(active_flag eq true)'
    const orderBy = options.orderBy || 'lu_updated desc'
    const data = await getItemsService(lists.technical_services_his_mapping, {
      filter,
      orderBy,
      top: options.top || 1000,
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getTechnicalServicesMappings')
    throw error
  }
}

export const getTechnicalServicesMappingById = async (mappingId) => {
  try {
    const data = await getItemService(lists.technical_services_his_mapping, mappingId)
    return data
  } catch (error) {
    handleError(error, 'getTechnicalServicesMappingById')
    throw error
  }
}

export const createTechnicalServicesMapping = async (mapping) => {
  try {
    // Generate UUID if not provided
    if (!mapping.technical_services_his_mapping_id) {
      mapping.technical_services_his_mapping_id = uuidv4()
    }

    // Set active flag if not provided
    if (mapping.active_flag === undefined) {
      mapping.active_flag = true
    }

    // Set update timestamp if not provided
    if (!mapping.lu_updated) {
      mapping.lu_updated = new Date().toISOString()
    }

    const data = await addListItemService(lists.technical_services_his_mapping, mapping)
    return data
  } catch (error) {
    handleError(error, 'createTechnicalServicesMapping')
    throw error
  }
}

export const updateTechnicalServicesMapping = async (mappingId, mapping) => {
  try {
    // Set update timestamp
    mapping.lu_updated = new Date().toISOString()

    const data = await updateListItemService(
      lists.technical_services_his_mapping,
      mappingId,
      mapping,
    )
    return data
  } catch (error) {
    handleError(error, 'updateTechnicalServicesMapping')
    throw error
  }
}

export const deleteTechnicalServicesMapping = async (mappingId) => {
  try {
    const data = await deleteListItemService(lists.technical_services_his_mapping, mappingId)
    return data
  } catch (error) {
    handleError(error, 'deleteTechnicalServicesMapping')
    throw error
  }
}

// Check if a technical services mapping with the same combination of IDs already exists
export const checkDuplicateTechnicalServicesMapping = async (values, currentMappingId = null) => {
  try {
    // Build filter to check for existing mappings with the same combination
    let filters = []

    // Add technical services filter (required)
    if (values.technical_services_id) {
      filters.push(`technical_services_id eq ${values.technical_services_id}`)
    } else {
      // If no technical services ID, there can't be a duplicate
      return { isDuplicate: false }
    }

    // Add item filter (required)
    if (values.item_id) {
      filters.push(`item_id eq ${values.item_id}`)
    } else {
      // If no item ID, there can't be a duplicate
      return { isDuplicate: false }
    }

    // Exclude current mapping when editing
    if (currentMappingId) {
      filters.push(`technical_services_his_mapping_id ne ${currentMappingId}`)
    }

    // Combine all filters
    const filter = filters.join(' and ')

    // Query for existing mappings with the same combination
    const data = await getItemsService(lists.technical_services_his_mapping, {
      filter,
      top: 1, // We only need to know if at least one exists
    })

    // Check if any duplicate was found
    const isDuplicate = data.value && data.value.length > 0

    return {
      isDuplicate,
      duplicateMapping: isDuplicate ? data.value[0] : null,
    }
  } catch (error) {
    handleError(error, 'checkDuplicateTechnicalServicesMapping')
    throw error
  }
}

// Get Medical Supplies
export const getMedicalSupplies = async (options = {}) => {
  try {
    let filter = options.filter || '(active_flag eq true)'
    const searchText = options.searchText || ''

    // Add search by ID if provided
    if ((searchText && typeof searchText === 'object' && searchText.id) || isUUID(searchText)) {
      // For UUID fields, don't use quotes
      const searchFilter = `medical_supplies_id eq ${searchText.id || searchText}`
      filter = filter ? `(${filter}) and (${searchFilter})` : searchFilter
    } else if (searchText && typeof searchText === 'string' && searchText.trim()) {
      // For text search, use contains with quotes
      const searchFilter = `contains(MA_VAT_TU, '${searchText}') or contains(TEN_VAT_TU, '${searchText}')`
      filter = filter ? `(${filter}) and (${searchFilter})` : searchFilter
    }

    const orderBy = options.orderBy || 'MA_VAT_TU asc'
    const data = await getItemsService(lists.medical_supplies, {
      filter,
      orderBy,
      top: options.top || 20, // Reduced for better performance with search
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getMedicalSupplies')
    throw error
  }
}

// Get Medicines
export const getMedicines = async (options = {}) => {
  try {
    let filter = options.filter || '(active_flag eq true)'
    const searchText = options.searchText || ''

    // Add search by ID if provided
    if ((searchText && typeof searchText === 'object' && searchText.id) || isUUID(searchText)) {
      // For UUID fields, don't use quotes
      const searchFilter = `medicine_id eq ${searchText.id || searchText}`
      filter = filter ? `(${filter}) and (${searchFilter})` : searchFilter
    } else if (searchText && typeof searchText === 'string' && searchText.trim()) {
      // For text search, use contains with quotes
      const searchFilter = `contains(MA_THUOC, '${searchText}') or contains(TEN_THUOC, '${searchText}')`
      filter = filter ? `(${filter}) and (${searchFilter})` : searchFilter
    }

    const orderBy = options.orderBy || 'MA_THUOC asc'
    const data = await getItemsService(lists.medicine, {
      filter,
      orderBy,
      top: options.top || 20, // Reduced for better performance with search
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getMedicines')
    throw error
  }
}

// Get Technical Services
export const getTechnicalServices = async (options = {}) => {
  try {
    let filter = options.filter || '(active_flag eq true)'
    const searchText = options.searchText || ''

    // Add search by ID if provided
    if ((searchText && typeof searchText === 'object' && searchText.id) || isUUID(searchText)) {
      // For UUID fields, don't use quotes
      const searchFilter = `technical_services_id eq ${searchText.id || searchText}`
      filter = filter ? `(${filter}) and (${searchFilter})` : searchFilter
    } else if (searchText && typeof searchText === 'string' && searchText.trim()) {
      // For text search, use contains with quotes
      const searchFilter = `contains(MA_TUONG_DUONG, '${searchText}') or contains(TEN_DVKT_PHEDUYET, '${searchText}')`
      filter = filter ? `(${filter}) and (${searchFilter})` : searchFilter
    }

    const orderBy = options.orderBy || 'MA_TUONG_DUONG asc'
    const data = await getItemsService(lists.technical_services, {
      filter,
      orderBy,
      top: options.top || 20, // Reduced for better performance with search
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getTechnicalServices')
    throw error
  }
}

// Get Items from api_item_view with search capability
export const getApiItems = async (options = {}) => {
  try {
    const filter = options.filter || ''
    const orderBy = options.orderBy || 'item_code asc'
    const data = await getItemsService(lists.api_item_view, {
      filter,
      orderBy,
      top: options.top || 20, // Limit to 20 records by default
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getApiItems')
    throw error
  }
}

// Search Items from api_item_view by item_code or name_e
export const searchApiItems = async (searchText = '') => {
  try {
    if (!searchText || searchText.length < 2) {
      return { value: [] }
    }

    // Create filter for searching by item_code or name_e
    const filter = `contains(item_code, '${searchText}') or contains(name_e, '${searchText}')`

    const data = await getItemsService(lists.api_item_view, {
      filter,
      orderBy: 'item_code asc',
      top: 20, // Limit to 20 records for performance
      skip: 0,
      count: false,
    })
    return data
  } catch (error) {
    handleError(error, 'searchApiItems')
    throw error
  }
}

// SS Item Technical Services Mapping
export const getSSItemTechnicalServicesMappings = async (options = {}) => {
  try {
    let filter = options.filter || ''
    const searchText = options.searchText || ''

    // Add search by ID if provided
    if (searchText) {
      let searchFilter = ''

      // Check if searchText is a UUID for any of the ID fields
      if (searchText.mapping_id) {
        // Direct search by mapping ID
        searchFilter = `ss_item_technical_services_mapping_id eq ${searchText.mapping_id}`
      } else if (searchText.medical_supplies_id) {
        searchFilter = `medical_supplies_id eq ${searchText.medical_supplies_id}`
      } else if (searchText.medicine_id) {
        searchFilter = `medicine_id eq ${searchText.medicine_id}`
      } else if (searchText.technical_services_id) {
        searchFilter = `technical_services_id eq ${searchText.technical_services_id}`
      }

      // Combine with existing filter if any
      if (searchFilter) {
        filter = filter ? `(${filter}) and (${searchFilter})` : searchFilter
      }
    }

    const orderBy = options.orderBy || 'lu_updated desc'
    const data = await getItemsService(lists.ss_item_technical_services_mapping, {
      filter,
      orderBy,
      top: options.top || 20, // Reduced for better performance with search
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getSSItemTechnicalServicesMappings')
    throw error
  }
}

export const getSSItemTechnicalServicesMappingById = async (mappingId) => {
  try {
    const data = await getItemService(lists.ss_item_technical_services_mapping, mappingId)
    return data
  } catch (error) {
    handleError(error, 'getSSItemTechnicalServicesMappingById')
    throw error
  }
}

export const createSSItemTechnicalServicesMapping = async (mapping) => {
  try {
    // Generate UUID if not provided
    if (!mapping.ss_item_technical_services_mapping_id) {
      mapping.ss_item_technical_services_mapping_id = uuidv4()
    }

    // Set update timestamp if not provided
    if (!mapping.lu_updated) {
      mapping.lu_updated = new Date().toISOString()
    }

    const data = await addListItemService(lists.ss_item_technical_services_mapping, mapping)
    return data
  } catch (error) {
    handleError(error, 'createSSItemTechnicalServicesMapping')
    throw error
  }
}

export const updateSSItemTechnicalServicesMapping = async (mappingId, mapping) => {
  try {
    // Set update timestamp
    mapping.lu_updated = new Date().toISOString()

    const data = await updateListItemService(
      lists.ss_item_technical_services_mapping,
      mappingId,
      mapping,
    )
    return data
  } catch (error) {
    handleError(error, 'updateSSItemTechnicalServicesMapping')
    throw error
  }
}

export const deleteSSItemTechnicalServicesMapping = async (mappingId) => {
  try {
    const data = await deleteListItemService(lists.ss_item_technical_services_mapping, mappingId)
    return data
  } catch (error) {
    handleError(error, 'deleteSSItemTechnicalServicesMapping')
    throw error
  }
}

// Check if a mapping with the same combination of IDs already exists
export const checkDuplicateSSItemTechnicalServicesMapping = async (
  values,
  currentMappingId = null,
) => {
  try {
    // Build filter to check for existing mappings with the same combination
    let filters = []

    // Add technical service filter (required)
    if (values.technical_services_id) {
      filters.push(`technical_services_id eq ${values.technical_services_id}`)
    } else {
      // If no technical service ID, there can't be a duplicate
      return { isDuplicate: false }
    }

    // Add medical supplies filter
    if (values.medical_supplies_id) {
      filters.push(`medical_supplies_id eq ${values.medical_supplies_id}`)
    } else {
      filters.push(`medical_supplies_id eq null`)
    }

    // Add medicine filter
    if (values.medicine_id) {
      filters.push(`medicine_id eq ${values.medicine_id}`)
    } else {
      filters.push(`medicine_id eq null`)
    }

    // Exclude current mapping when editing
    if (currentMappingId) {
      filters.push(`ss_item_technical_services_mapping_id ne ${currentMappingId}`)
    }

    // Combine all filters
    const filter = filters.join(' and ')

    // Query for existing mappings with the same combination
    const data = await getItemsService(lists.ss_item_technical_services_mapping, {
      filter,
      top: 1, // We only need to know if at least one exists
    })

    // Check if any duplicate was found
    const isDuplicate = data.value && data.value.length > 0

    return {
      isDuplicate,
      duplicateMapping: isDuplicate ? data.value[0] : null,
    }
  } catch (error) {
    handleError(error, 'checkDuplicateSSItemTechnicalServicesMapping')
    throw error
  }
}
