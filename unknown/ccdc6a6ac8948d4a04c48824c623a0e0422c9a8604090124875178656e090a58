import React, { useEffect } from 'react'
import { Modal, Form, Input, Switch, Button } from 'antd'
import { useMutation } from '@tanstack/react-query'
import { createUserGroup, updateUserGroup } from '../../services/permissionService'
import { handleError } from '../../common/helpers'
import { useUI } from '../../common/UIProvider'
import PropTypes from 'prop-types'

const UserGroupForm = ({ visible, onCancel, group, mode, onSuccess }) => {
  const [form] = Form.useForm()
  const ui = useUI()

  // Reset form when modal opens or group changes
  useEffect(() => {
    if (visible) {
      form.resetFields()
      if (group && mode === 'edit') {
        form.setFieldsValue({
          name_e: group.name_e,
          name_l: group.name_l,
          active_flag: group.active_flag,
        })
      }
    }
  }, [visible, group, form, mode])

  // Mutation for creating a user group
  const createMutation = useMutation({
    mutationFn: (values) => createUserGroup(values),
    onSuccess: () => {
      ui.notiSuccess('User group created successfully')
      onSuccess()
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to create user group')
    },
  })

  // Mutation for updating a user group
  const updateMutation = useMutation({
    mutationFn: ({ id, values }) => updateUserGroup(id, values),
    onSuccess: () => {
      ui.notiSuccess('User group updated successfully')
      onSuccess()
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to update user group')
    },
  })

  // Handle form submission
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      
      if (mode === 'create') {
        createMutation.mutate(values)
      } else {
        updateMutation.mutate({ id: group.user_group_id, values })
      }
    } catch (error) {
      console.error('Validation failed:', error)
    }
  }

  return (
    <Modal
      title={mode === 'create' ? 'Create User Group' : 'Edit User Group'}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={createMutation.isPending || updateMutation.isPending}
          onClick={handleSubmit}
        >
          {mode === 'create' ? 'Create' : 'Update'}
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          active_flag: true,
        }}
      >
        <Form.Item
          name="name_e"
          label="Name (English)"
          rules={[{ required: true, message: 'Please enter the English name' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="name_l"
          label="Name (Local)"
          rules={[{ required: true, message: 'Please enter the local name' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="active_flag"
          label="Active"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </Form>
    </Modal>
  )
}

UserGroupForm.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  group: PropTypes.object,
  mode: PropTypes.oneOf(['create', 'edit']).isRequired,
  onSuccess: PropTypes.func.isRequired,
}

export default UserGroupForm
