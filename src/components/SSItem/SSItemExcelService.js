import lists from '../../common/lists'
import { exportData } from './SSItemExcelUtils'
import { patchMultiRecordDetails } from '../../common/services'
import { v4 as uuidv4 } from 'uuid'
import dayjs from '../../common/dayjs'

// Default column format configurations
const DEFAULT_COLUMN_FORMATS = {
  TU_NGAY: 'YYYYMMDD',
  DEN_NGAY: 'YYYYMMDD',
}

/**
 * Convert YYYYMMDD string format to SQL Server datetime format
 * @param {string} dateString - Date string in YYYYMMDD format
 * @returns {string|null} - ISO date string or null if invalid
 */
const convertYYYYMMDDToSQLDateTime = (dateString) => {
  if (!dateString) return null

  // Check if the string is in YYYYMMDD format
  if (typeof dateString === 'string' && dateString.length === 8) {
    const year = dateString.substring(0, 4)
    const month = dateString.substring(4, 6)
    const day = dateString.substring(6, 8)

    // Create a date object and convert to ISO string for SQL Server
    const date = dayjs(`${year}-${month}-${day}`)
    return date.isValid() ? date.format('YYYY-MM-DDTHH:mm:ss') : null
  }

  // If it's already a date object or in another format, try to parse it
  const date = dayjs(dateString)
  return date.isValid() ? date.format('YYYY-MM-DDTHH:mm:ss') : null
}

/**
 * Export Medical Supplies Details
 * @param {string} medicalSupplyId - ID of the medical supply
 * @param {Object} columnFormats - Optional column format configuration
 * @returns {Promise<Array>} - Exported data
 */
export const exportMedicalSuppliesDetails = async (
  medicalSupplyId = null,
  columnFormats = DEFAULT_COLUMN_FORMATS,
) => {
  const filter = medicalSupplyId ? `(medical_supplies_id eq ${medicalSupplyId})` : ''
  return exportData(lists.medical_supplies_detail, 'exportMedicalSuppliesDetails', {
    filter,
    includeParentInfo: true,
    parentListConfig: {
      ...lists.medical_supplies,
      primaryKeyName: 'medical_supplies_id',
    },
    parentIdField: 'medical_supplies_id',
    columnFormats,
  })
}

/**
 * Export Medicine Details
 * @param {string} medicineId - ID of the medicine
 * @param {Object} columnFormats - Optional column format configuration
 * @returns {Promise<Array>} - Exported data
 */
export const exportMedicineDetails = async (
  medicineId = null,
  columnFormats = DEFAULT_COLUMN_FORMATS,
) => {
  const filter = medicineId ? `(medicine_id eq ${medicineId})` : ''
  return exportData(lists.medicine_detail, 'exportMedicineDetails', {
    filter,
    includeParentInfo: true,
    parentListConfig: {
      ...lists.medicine,
      primaryKeyName: 'medicine_id',
    },
    parentIdField: 'medicine_id',
    columnFormats,
  })
}

/**
 * Export Technical Service Details
 * @param {string} technicalServiceId - ID of the technical service
 * @param {Object} columnFormats - Optional column format configuration
 * @returns {Promise<Array>} - Exported data
 */
export const exportTechnicalServiceDetails = async (
  technicalServiceId = null,
  columnFormats = DEFAULT_COLUMN_FORMATS,
) => {
  const filter = technicalServiceId ? `(technical_services_id eq ${technicalServiceId})` : ''
  return exportData(lists.technical_services_detail, 'exportTechnicalServiceDetails', {
    filter,
    includeParentInfo: true,
    parentListConfig: {
      ...lists.technical_services,
      primaryKeyName: 'technical_services_id',
    },
    parentIdField: 'technical_services_id',
    columnFormats,
  })
}

// Import Medical Supplies Details
export const importMedicalSuppliesDetails = async ({ data, userId }) => {
  try {
    // Validate input parameters
    if (!data) {
      console.error('No data provided to importMedicalSuppliesDetails')
      return {
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [{ error: 'No data provided', item: {} }],
      }
    }

    // Validate data is an array
    if (!Array.isArray(data)) {
      console.error('Invalid data format provided to importMedicalSuppliesDetails - expected array')
      return {
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [{ error: 'Invalid data format - expected array', item: data || {} }],
      }
    }

    // Validate array is not empty
    if (data.length === 0) {
      console.error('Empty data array provided to importMedicalSuppliesDetails')
      return {
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [{ error: 'Empty data array', item: {} }],
      }
    }

    // Validate userId
    if (!userId) {
      console.error('No userId provided to importMedicalSuppliesDetails')
      return {
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [{ error: 'No userId provided', item: {} }],
      }
    }

    // Ensure userId is a string (some functions might expect it as a string)
    const userIdStr = String(userId)

    // Process data to prepare for batch operation
    const parentRecords = []
    const detailRecords = []
    const results = {
      created: 0,
      skipped: 0,
      updated: 0,
      errors: [],
    }

    // Extract parent and detail records
    data.forEach((item) => {
      if (!item) return

      // Process parent info if it exists
      if (item.parentInfo) {
        const parentInfo = { ...item.parentInfo }

        // Convert date fields to SQL Server datetime format
        if (parentInfo.TU_NGAY) {
          parentInfo.TU_NGAY = convertYYYYMMDDToSQLDateTime(parentInfo.TU_NGAY)
        }
        if (parentInfo.DEN_NGAY) {
          parentInfo.DEN_NGAY = convertYYYYMMDDToSQLDateTime(parentInfo.DEN_NGAY)
        }

        if (parentInfo.medical_supplies_id) {
          parentRecords.push({
            ...parentInfo,
            lu_updated: new Date().toISOString(),
            lu_user_id: userIdStr,
          })
        }
      }

      // Process detail record
      const { parentInfo, ...detailInfo } = item

      // Convert date fields to SQL Server datetime format
      if (detailInfo.TU_NGAY) {
        detailInfo.TU_NGAY = convertYYYYMMDDToSQLDateTime(detailInfo.TU_NGAY)
      }
      if (detailInfo.DEN_NGAY) {
        detailInfo.DEN_NGAY = convertYYYYMMDDToSQLDateTime(detailInfo.DEN_NGAY)
      }

      if (detailInfo.medical_supplies_detail_id || detailInfo.medical_supplies_id) {
        // Ensure detail record has an ID
        if (!detailInfo.medical_supplies_detail_id) {
          detailInfo.medical_supplies_detail_id = uuidv4()
        }

        detailRecords.push({
          ...detailInfo,
          lu_updated: new Date().toISOString(),
          lu_user_id: userIdStr,
        })
      }
    })

    // Process all records in a single batch operation
    if (parentRecords.length > 0) {
      try {
        // Make sure we're passing the listName property
        await patchMultiRecordDetails(lists.medical_supplies.listName, parentRecords)
        results.updated += parentRecords.length
      } catch (error) {
        console.error('Error updating parent records:', error)
        results.errors.push({ error: error.message || 'Error updating parent records', item: {} })
      }
    }

    if (detailRecords.length > 0) {
      try {
        // Make sure we're passing the listName property
        await patchMultiRecordDetails(lists.medical_supplies_detail.listName, detailRecords)
        results.created += detailRecords.length
      } catch (error) {
        console.error('Error creating detail records:', error)
        results.errors.push({ error: error.message || 'Error creating detail records', item: {} })
      }
    }

    return results
  } catch (error) {
    console.error('Error in importMedicalSuppliesDetails:', error)
    return {
      created: 0,
      skipped: 0,
      updated: 0,
      errors: [{ error: error.message || 'Unknown error', item: {} }],
    }
  }
}

// Import Medicine Details
export const importMedicineDetails = async ({ data, userId }) => {
  try {
    // Validate input parameters
    if (!data) {
      console.error('No data provided to importMedicineDetails')
      return {
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [{ error: 'No data provided', item: {} }],
      }
    }

    // Validate data is an array
    if (!Array.isArray(data)) {
      console.error('Invalid data format provided to importMedicineDetails - expected array')
      return {
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [{ error: 'Invalid data format - expected array', item: data || {} }],
      }
    }

    // Validate array is not empty
    if (data.length === 0) {
      console.error('Empty data array provided to importMedicineDetails')
      return {
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [{ error: 'Empty data array', item: {} }],
      }
    }

    // Validate userId
    if (!userId) {
      console.error('No userId provided to importMedicineDetails')
      return {
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [{ error: 'No userId provided', item: {} }],
      }
    }

    // Ensure userId is a string (some functions might expect it as a string)
    const userIdStr = String(userId)

    // Process data to prepare for batch operation
    const parentRecords = []
    const detailRecords = []
    const results = {
      created: 0,
      skipped: 0,
      updated: 0,
      errors: [],
    }

    // Extract parent and detail records
    data.forEach((item) => {
      if (!item) return

      // Process parent info if it exists
      if (item.parentInfo) {
        const parentInfo = { ...item.parentInfo }

        // Convert date fields to SQL Server datetime format
        if (parentInfo.TU_NGAY) {
          parentInfo.TU_NGAY = convertYYYYMMDDToSQLDateTime(parentInfo.TU_NGAY)
        }
        if (parentInfo.DEN_NGAY) {
          parentInfo.DEN_NGAY = convertYYYYMMDDToSQLDateTime(parentInfo.DEN_NGAY)
        }

        if (parentInfo.medicine_id) {
          parentRecords.push({
            ...parentInfo,
            lu_updated: new Date().toISOString(),
            lu_user_id: userIdStr,
          })
        }
      }

      // Process detail record
      const { parentInfo, ...detailInfo } = item

      // Convert date fields to SQL Server datetime format
      if (detailInfo.TU_NGAY) {
        detailInfo.TU_NGAY = convertYYYYMMDDToSQLDateTime(detailInfo.TU_NGAY)
      }
      if (detailInfo.DEN_NGAY) {
        detailInfo.DEN_NGAY = convertYYYYMMDDToSQLDateTime(detailInfo.DEN_NGAY)
      }

      if (detailInfo.medicine_detail_id || detailInfo.medicine_id) {
        // Ensure detail record has an ID
        if (!detailInfo.medicine_detail_id) {
          detailInfo.medicine_detail_id = uuidv4()
        }

        detailRecords.push({
          ...detailInfo,
          lu_updated: new Date().toISOString(),
          lu_user_id: userIdStr,
        })
      }
    })

    // Process all records in a single batch operation
    if (parentRecords.length > 0) {
      try {
        // Make sure we're passing the listName property
        await patchMultiRecordDetails(lists.medicine.listName, parentRecords)
        results.updated += parentRecords.length
      } catch (error) {
        console.error('Error updating parent records:', error)
        results.errors.push({ error: error.message || 'Error updating parent records', item: {} })
      }
    }

    if (detailRecords.length > 0) {
      try {
        // Make sure we're passing the listName property
        await patchMultiRecordDetails(lists.medicine_detail.listName, detailRecords)
        results.created += detailRecords.length
      } catch (error) {
        console.error('Error creating detail records:', error)
        results.errors.push({ error: error.message || 'Error creating detail records', item: {} })
      }
    }

    return results
  } catch (error) {
    console.error('Error in importMedicineDetails:', error)
    return {
      created: 0,
      skipped: 0,
      updated: 0,
      errors: [{ error: error.message || 'Unknown error', item: {} }],
    }
  }
}

// Import Technical Service Details
export const importTechnicalServiceDetails = async ({ data, userId }) => {
  try {
    // Validate input parameters
    if (!data) {
      console.error('No data provided to importTechnicalServiceDetails')
      return {
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [{ error: 'No data provided', item: {} }],
      }
    }

    // Validate data is an array
    if (!Array.isArray(data)) {
      console.error(
        'Invalid data format provided to importTechnicalServiceDetails - expected array',
      )
      return {
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [{ error: 'Invalid data format - expected array', item: data || {} }],
      }
    }

    // Validate array is not empty
    if (data.length === 0) {
      console.error('Empty data array provided to importTechnicalServiceDetails')
      return {
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [{ error: 'Empty data array', item: {} }],
      }
    }

    // Validate userId
    if (!userId) {
      console.error('No userId provided to importTechnicalServiceDetails')
      return {
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [{ error: 'No userId provided', item: {} }],
      }
    }

    // Ensure userId is a string (some functions might expect it as a string)
    const userIdStr = String(userId)

    // Process data to prepare for batch operation
    const parentRecords = []
    const detailRecords = []
    const results = {
      created: 0,
      skipped: 0,
      updated: 0,
      errors: [],
    }

    // Extract parent and detail records
    data.forEach((item) => {
      if (!item) return

      // Process parent info if it exists
      if (item.parentInfo) {
        const parentInfo = { ...item.parentInfo }

        // Convert date fields to SQL Server datetime format
        if (parentInfo.TU_NGAY) {
          parentInfo.TU_NGAY = convertYYYYMMDDToSQLDateTime(parentInfo.TU_NGAY)
        }
        if (parentInfo.DEN_NGAY) {
          parentInfo.DEN_NGAY = convertYYYYMMDDToSQLDateTime(parentInfo.DEN_NGAY)
        }

        if (parentInfo.technical_services_id) {
          parentRecords.push({
            ...parentInfo,
            lu_updated: new Date().toISOString(),
            lu_user_id: userIdStr,
          })
        }
      }

      // Process detail record
      const { parentInfo, ...detailInfo } = item

      // Convert date fields to SQL Server datetime format
      if (detailInfo.TU_NGAY) {
        detailInfo.TU_NGAY = convertYYYYMMDDToSQLDateTime(detailInfo.TU_NGAY)
      }
      if (detailInfo.DEN_NGAY) {
        detailInfo.DEN_NGAY = convertYYYYMMDDToSQLDateTime(detailInfo.DEN_NGAY)
      }

      if (detailInfo.technical_services_detail_id || detailInfo.technical_services_id) {
        // Ensure detail record has an ID
        if (!detailInfo.technical_services_detail_id) {
          detailInfo.technical_services_detail_id = uuidv4()
        }

        detailRecords.push({
          ...detailInfo,
          lu_updated: new Date().toISOString(),
          lu_user_id: userIdStr,
        })
      }
    })

    // Process all records in a single batch operation
    if (parentRecords.length > 0) {
      try {
        // Make sure we're passing the listName property
        await patchMultiRecordDetails(lists.technical_services.listName, parentRecords)
        results.updated += parentRecords.length
      } catch (error) {
        console.error('Error updating parent records:', error)
        results.errors.push({ error: error.message || 'Error updating parent records', item: {} })
      }
    }

    if (detailRecords.length > 0) {
      try {
        // Make sure we're passing the listName property
        await patchMultiRecordDetails(lists.technical_services_detail.listName, detailRecords)
        results.created += detailRecords.length
      } catch (error) {
        console.error('Error creating detail records:', error)
        results.errors.push({ error: error.message || 'Error creating detail records', item: {} })
      }
    }

    return results
  } catch (error) {
    console.error('Error in importTechnicalServiceDetails:', error)
    return {
      created: 0,
      skipped: 0,
      updated: 0,
      errors: [{ error: error.message || 'Unknown error', item: {} }],
    }
  }
}
