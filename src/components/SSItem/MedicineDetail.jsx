import React from 'react'
import { Form, Input, DatePicker, InputNumber, Select, Checkbox, Col } from 'antd'
import SSItemDetail from './SSItemDetail'
import { useMedicineDetails, useAddMedicineDetail, useDeleteMedicineItem } from './hooks/useSSItem'
import { ITEM_TYPES } from './SSItemConstant'
import dayjs from '../../common/dayjs'
import { FORMAT_DATE } from '../../common/constant'

// Detail columns for the history table
const detailColumns = [
  {
    title: 'Mã thuốc',
    dataIndex: 'MA_THUOC',
    key: 'MA_THUOC',
    width: 150,
  },
  {
    title: 'Tên thuốc',
    dataIndex: 'TEN_THUOC',
    key: 'TEN_THUOC',
    width: 300,
  },
  {
    title: 'Tên hoạt chất',
    dataIndex: 'TEN_HOAT_CHAT',
    key: 'TEN_HOAT_CHAT',
    width: 200,
  },
  {
    title: '<PERSON><PERSON><PERSON> lượng',
    dataIndex: 'HAM_LUONG',
    key: 'HAM_LUONG',
    width: 150,
  },
  {
    title: 'Đơn vị tính',
    dataIndex: 'DON_VI_TINH',
    key: 'DON_VI_TINH',
    width: 100,
  },
  {
    title: 'Đơn giá',
    dataIndex: 'DON_GIA',
    key: 'DON_GIA',
    width: 120,
    render: (value) => (value ? value.toLocaleString('vi-VN') : 'N/A'),
  },
  {
    title: 'Đơn giá BH',
    dataIndex: 'DON_GIA_BH',
    key: 'DON_GIA_BH',
    width: 120,
    render: (value) => (value ? value.toLocaleString('vi-VN') : 'N/A'),
  },
  {
    title: 'TT Thầu',
    dataIndex: 'TT_THAU',
    key: 'TT_THAU',
    width: 120,
  },
  {
    title: 'Từ ngày',
    dataIndex: 'TU_NGAY',
    key: 'TU_NGAY',
    width: 120,
    render: (date) => (date ? dayjs(date).format(FORMAT_DATE) : 'N/A'),
  },
  {
    title: 'Đến ngày',
    dataIndex: 'DEN_NGAY',
    key: 'DEN_NGAY',
    width: 120,
    render: (date) => (date ? dayjs(date).format(FORMAT_DATE) : 'N/A'),
  },
  {
    title: 'Cập nhật lúc',
    dataIndex: 'lu_updated',
    key: 'lu_updated',
    width: 150,
    render: (date) => (date ? dayjs(date).format('DD/MM/YYYY HH:mm:ss') : 'N/A'),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'active_flag',
    key: 'active_flag',
    width: 100,
    render: (active) => (active ? 'Có' : 'Không'),
  },
]

// Render form fields for the add/edit modal
const renderFields = () => (
  <>
    {/* Row 1 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item
        name="MA_THUOC"
        label="Mã thuốc"
        rules={[{ required: true, message: 'Vui lòng nhập mã thuốc' }]}>
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item
        name="TEN_THUOC"
        label="Tên thuốc"
        rules={[{ required: true, message: 'Vui lòng nhập tên thuốc' }]}>
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="TEN_HOAT_CHAT" label="Tên hoạt chất">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="HAM_LUONG" label="Hàm lượng">
        <Input />
      </Form.Item>
    </Col>

    {/* Row 2 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="DON_VI_TINH" label="Đơn vị tính">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="DUONG_DUNG" label="Đường dùng">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="MA_DUONG_DUNG" label="Mã đường dùng">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="DANG_BAO_CHE" label="Dạng bào chế">
        <Input />
      </Form.Item>
    </Col>

    {/* Row 3 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="SO_DANG_KY" label="Số đăng ký">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="SO_LUONG" label="Số lượng">
        <InputNumber style={{ width: '100%' }} min={0} />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="DON_GIA" label="Đơn giá">
        <InputNumber
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
        />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="DON_GIA_BH" label="Đơn giá BH">
        <InputNumber
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
        />
      </Form.Item>
    </Col>

    {/* Row 4 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="QUY_CACH" label="Quy cách">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="NHA_SX" label="Nhà sản xuất">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="NUOC_SX" label="Nước sản xuất">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="NHA_THAU" label="Nhà thầu">
        <Input />
      </Form.Item>
    </Col>

    {/* Row 5 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="TU_NGAY" label="Từ ngày">
        <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="DEN_NGAY" label="Đến ngày">
        <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="MA_CSKCB" label="Mã CSKCB">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="LOAI_THUOC" label="Loại thuốc">
        <Select
          options={[
            { value: 1, label: 'Loại 1' },
            { value: 2, label: 'Loại 2' },
            { value: 3, label: 'Loại 3' },
          ]}
        />
      </Form.Item>
    </Col>

    {/* Row 6 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="LOAI_THAU" label="Loại thầu">
        <Select
          options={[
            { value: 1, label: 'Loại 1' },
            { value: 2, label: 'Loại 2' },
            { value: 3, label: 'Loại 3' },
          ]}
        />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="HT_THAU" label="Hình thức thầu">
        <Select
          options={[
            { value: 1, label: 'Hình thức 1' },
            { value: 2, label: 'Hình thức 2' },
            { value: 3, label: 'Hình thức 3' },
          ]}
        />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="MA_DVKT" label="Mã DVKT">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="TCCL" label="TCCL">
        <Input />
      </Form.Item>
    </Col>

    {/* Row 7 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="DON_GIA_AP_THAU" label="Đơn giá áp thầu">
        <InputNumber
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
        />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="TYLE_TT_DV" label="Tỷ lệ thanh toán DV">
        <InputNumber style={{ width: '100%' }} min={0} max={100} />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="TYLE_TT_BH" label="Tỷ lệ thanh toán BH">
        <InputNumber style={{ width: '100%' }} min={0} max={100} />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="TYLE_TAI_SD" label="Tỷ lệ tái sử dụng">
        <InputNumber style={{ width: '100%' }} min={0} max={100} />
      </Form.Item>
    </Col>

    {/* Row 8 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="MA_NHOM" label="Mã nhóm">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="TT_THAU" label="TT Thầu">
        <Input />
      </Form.Item>
    </Col>

    {/* TextArea takes full width */}
    <Col span={24}>
      <Form.Item name="DIEU_KIEN_THANH_TOAN_BHYT" label="Điều kiện thanh toán BHYT">
        <Input.TextArea rows={4} />
      </Form.Item>
    </Col>

    <Col span={24}>
      <Form.Item name="active_flag" valuePropName="checked">
        <Checkbox>Kích hoạt</Checkbox>
      </Form.Item>
    </Col>
  </>
)

const MedicineDetail = () => {
  return (
    <SSItemDetail
      itemType={ITEM_TYPES.MEDICINE}
      useItemDetails={useMedicineDetails}
      useAddItemDetail={useAddMedicineDetail}
      useDeleteItem={useDeleteMedicineItem}
      renderFields={renderFields}
      detailColumns={detailColumns}
    />
  )
}

export default MedicineDetail
