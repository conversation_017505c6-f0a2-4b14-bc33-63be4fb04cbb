import React, { useState } from 'react'
import { Card, Tabs, Typography } from 'antd'
import { usePermission } from '../Permission/hooks/usePermission'
import SSItemList from './SSItemList'
import { useMedicalSupplies, useMedicines, useTechnicalServices } from './hooks/useSSItem'
import {
  TAB_KEYS,
  ITEM_TYPES,
  MEDICAL_SUPPLIES_COLUMNS,
  MEDICINE_COLUMNS,
  TECHNICAL_SERVICES_COLUMNS,
} from './SSItemConstant'

const { Title } = Typography

const SSItemManagement = () => {
  const [activeTab, setActiveTab] = useState(TAB_KEYS.MEDICAL_SUPPLIES)
  const [searchText, setSearchText] = useState('')

  // Check if user has admin permissions
  usePermission('ADMIN')

  // Custom hooks for data fetching
  const {
    medicalSupplies,
    isLoading: isLoadingMedicalSupplies,
    pagination: medicalSuppliesPagination,
    setPagination: setMedicalSuppliesPagination,
    refetch: refetchMedicalSupplies,
  } = useMedicalSupplies(searchText)

  const {
    medicines,
    isLoading: isLoadingMedicines,
    pagination: medicinesPagination,
    setPagination: setMedicinesPagination,
    refetch: refetchMedicines,
  } = useMedicines(searchText)

  const {
    technicalServices,
    isLoading: isLoadingTechnicalServices,
    pagination: technicalServicesPagination,
    setPagination: setTechnicalServicesPagination,
    refetch: refetchTechnicalServices,
  } = useTechnicalServices(searchText)

  // Handle tab change
  const handleTabChange = (key) => {
    setActiveTab(key)
    setSearchText('')
  }

  // Handle search for Medical Supplies
  const handleSearchMedicalSupplies = (text) => {
    setSearchText(text)
    setMedicalSuppliesPagination((prev) => ({ ...prev, current: 1 }))
    refetchMedicalSupplies()
  }

  // Handle search for Medicines
  const handleSearchMedicines = (text) => {
    setSearchText(text)
    setMedicinesPagination((prev) => ({ ...prev, current: 1 }))
    refetchMedicines()
  }

  // Handle search for Technical Services
  const handleSearchTechnicalServices = (text) => {
    setSearchText(text)
    setTechnicalServicesPagination((prev) => ({ ...prev, current: 1 }))
    refetchTechnicalServices()
  }

  // Handle pagination change for Medical Supplies
  const handleMedicalSuppliesPaginationChange = (pagination) => {
    setMedicalSuppliesPagination(pagination)
  }

  // Handle pagination change for Medicines
  const handleMedicinesPaginationChange = (pagination) => {
    setMedicinesPagination(pagination)
  }

  // Handle pagination change for Technical Services
  const handleTechnicalServicesPaginationChange = (pagination) => {
    setTechnicalServicesPagination(pagination)
  }

  // Tab items
  const items = [
    {
      key: TAB_KEYS.MEDICAL_SUPPLIES,
      label: 'Vật tư y tế',
      children: (
        <SSItemList
          title="Danh sách vật tư y tế"
          dataSource={medicalSupplies}
          columns={MEDICAL_SUPPLIES_COLUMNS}
          loading={isLoadingMedicalSupplies}
          pagination={medicalSuppliesPagination}
          onPaginationChange={handleMedicalSuppliesPaginationChange}
          onSearch={handleSearchMedicalSupplies}
          itemType={ITEM_TYPES.MEDICAL_SUPPLIES}
        />
      ),
    },
    {
      key: TAB_KEYS.MEDICINE,
      label: 'Thuốc',
      children: (
        <SSItemList
          title="Danh sách thuốc"
          dataSource={medicines}
          columns={MEDICINE_COLUMNS}
          loading={isLoadingMedicines}
          pagination={medicinesPagination}
          onPaginationChange={handleMedicinesPaginationChange}
          onSearch={handleSearchMedicines}
          itemType={ITEM_TYPES.MEDICINE}
        />
      ),
    },
    {
      key: TAB_KEYS.TECHNICAL_SERVICES,
      label: 'Dịch vụ kỹ thuật',
      children: (
        <SSItemList
          title="Danh sách dịch vụ kỹ thuật"
          dataSource={technicalServices}
          columns={TECHNICAL_SERVICES_COLUMNS}
          loading={isLoadingTechnicalServices}
          pagination={technicalServicesPagination}
          onPaginationChange={handleTechnicalServicesPaginationChange}
          onSearch={handleSearchTechnicalServices}
          itemType={ITEM_TYPES.TECHNICAL_SERVICES}
        />
      ),
    },
  ]

  return (
    <div style={{ padding: '20px' }}>
      <Card>
        <Title level={2}>Quản lý danh mục</Title>
        <Tabs activeKey={activeTab} onChange={handleTabChange} items={items} type="card" />
      </Card>
    </div>
  )
}

export default SSItemManagement
