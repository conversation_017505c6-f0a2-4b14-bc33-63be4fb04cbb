import { handleError } from '../../common/helpers'
import lists from '../../common/lists'
import { getItemsService, patchMultiRecordDetails } from '../../common/services'
import { v4 as uuidv4 } from 'uuid'
import { isEqual } from 'lodash'
import dayjs from '../../common/dayjs'

/**
 * Fetch all records from a list in batches
 * @param {Object} listConfig - List configuration from lists.js
 * @param {Object} options - Additional options for the query
 * @returns {Promise<Array>} - All records from the list
 */
export const fetchAllRecords = async (listConfig, options = {}) => {
  const batchSize = options.batchSize || 1000
  let allRecords = []
  let skip = 0
  let hasMoreRecords = true

  try {
    while (hasMoreRecords) {
      const response = await getItemsService(listConfig, {
        filter: options.filter || '',
        orderBy: options.orderBy || 'lu_updated desc',
        top: batchSize,
        skip: skip,
        expand: options.expand || '',
        select: options.select || '',
      })

      const records = response.value || []
      allRecords = [...allRecords, ...records]

      // Check if we need to fetch more records
      if (records.length < batchSize) {
        hasMoreRecords = false
      } else {
        skip += batchSize
      }
    }

    return allRecords
  } catch (error) {
    handleError(error, options.errorContext || 'fetchAllRecords')
    throw error
  }
}

/**
 * Helper function to compare objects excluding specified fields
 * @param {Object} obj1 - First object to compare
 * @param {Object} obj2 - Second object to compare
 * @param {Array} excludeFields - Fields to exclude from comparison
 * @returns {boolean} - Whether objects are equal
 */
export const areObjectsEqual = (obj1, obj2, excludeFields = ['lu_updated', 'lu_user_id']) => {
  const filteredObj1 = { ...obj1 }
  const filteredObj2 = { ...obj2 }

  // Remove excluded fields
  excludeFields.forEach((field) => {
    delete filteredObj1[field]
    delete filteredObj2[field]
  })

  return isEqual(filteredObj1, filteredObj2)
}

/**
 * Format data based on column format configuration
 * @param {Array} data - Data to format
 * @param {Object} columnFormats - Column format configuration
 * @returns {Array} - Formatted data
 */
export const formatDataForExport = (data, columnFormats = {}) => {
  if (!data || !Array.isArray(data) || data.length === 0 || !columnFormats) {
    return data
  }

  return data.map((item) => {
    const formattedItem = { ...item }

    // Apply format to each configured column
    Object.entries(columnFormats).forEach(([column, format]) => {
      if (formattedItem[column] !== undefined && formattedItem[column] !== null) {
        // Handle date formats
        if (format === 'YYYYMMDD') {
          // Convert to dayjs object if it's not already
          const dateValue = dayjs(formattedItem[column])
          if (dateValue.isValid()) {
            formattedItem[column] = dateValue.format('YYYYMMDD')
          }
        }
        // Add more format handlers here as needed
      }
    })

    return formattedItem
  })
}

/**
 * Generic function to export data from a list
 * @param {Object} listConfig - List configuration from lists.js
 * @param {string} errorContext - Context for error handling
 * @param {Object} options - Additional options for the query
 * @returns {Promise<Array>} - Exported data
 */
export const exportData = async (listConfig, errorContext, options = {}) => {
  try {
    // Use fetchAllRecords to get all data in batches
    const processedData = await fetchAllRecords(listConfig, {
      filter: options.filter || '',
      orderBy: options.orderBy || 'lu_updated desc',
      expand: options.expand || '',
      select: options.select || '',
      errorContext: errorContext,
    })

    // If this is a detail table and we need to include parent info
    if (options.includeParentInfo && options.parentListConfig && options.parentIdField) {
      // Extract unique parent IDs
      const parentIds = [
        ...new Set(processedData.map((item) => item[options.parentIdField]).filter(Boolean)),
      ]

      // Fetch all parent records without filtering by IDs
      const groupedByParent = {}
      if (parentIds.length > 0) {
        try {
          // Fetch all parent records using our batch function
          const parentRecords = await fetchAllRecords(options.parentListConfig, {
            errorContext: `${errorContext}_fetchParents`,
          })

          // Group parent records by ID for easy lookup
          if (parentRecords && parentRecords.length > 0) {
            parentRecords.forEach((record) => {
              const recordId =
                record[options.parentListConfig.primaryKeyName || options.parentIdField]
              if (recordId) {
                groupedByParent[recordId] = record
              }
            })
          }
        } catch (error) {
          console.error(`Error fetching parent records: ${error.message}`)
        }
      }

      // Merge parent info into detail records
      const dataWithParentInfo = processedData.map((item) => {
        const parentId = item[options.parentIdField]
        const parentRecord = groupedByParent[parentId] || {}

        // Create a new object with parent info but exclude lu_updated and lu_user_id
        const { lu_updated, lu_user_id, ...parentInfo } = parentRecord

        return {
          ...item,
          parentInfo: parentInfo,
        }
      })

      // Apply column formats if provided
      if (options.columnFormats) {
        return formatDataForExport(dataWithParentInfo, options.columnFormats)
      }

      return dataWithParentInfo
    }

    // Apply column formats if provided
    if (options.columnFormats) {
      return formatDataForExport(processedData, options.columnFormats)
    }

    return processedData
  } catch (error) {
    handleError(error, errorContext)
    throw error
  }
}

/**
 * Generic function to import parent items (medical_supplies, medicine, technical_services)
 * @param {Array} data - Data to import
 * @param {string} userId - User ID
 * @param {Object} config - Configuration object with list, idField, and errorContext
 * @returns {Promise<Object>} - Import results
 */
export const importParentItems = async (data, userId, config) => {
  try {
    const results = {
      created: 0,
      updated: 0,
      skipped: 0,
      errors: [],
    }

    // Check if data is valid
    if (!data || !Array.isArray(data) || data.length === 0) {
      console.warn('No valid data provided for import')
      return results
    }

    // Process data - if data contains parentInfo, extract parent records
    const processedData = data
      .map((item) => {
        if (!item) return null
        // If this is a detail record with parentInfo, extract the parent info
        if (item.parentInfo) {
          return item.parentInfo
        }
        return item
      })
      .filter(Boolean)

    // Remove duplicates based on ID field
    const uniqueData = []
    const seenIds = new Set()

    for (const item of processedData) {
      if (item[config.idField] && !seenIds.has(item[config.idField])) {
        seenIds.add(item[config.idField])
        uniqueData.push(item)
      } else if (!item[config.idField]) {
        uniqueData.push(item)
      }
    }

    // Fetch all existing items without filtering by IDs
    const existingItemsMap = {}
    try {
      // Fetch all records using our batch function
      const existingItems = await fetchAllRecords(config.list, {
        errorContext: `${config.errorContext}_fetchAll`,
      })

      // Create a map for quick lookup
      if (existingItems && existingItems.length > 0) {
        existingItems.forEach((item) => {
          if (item[config.idField]) {
            existingItemsMap[item[config.idField]] = item
          }
        })
      }
    } catch (error) {
      console.error(`Error fetching existing items: ${error.message}`)
    }

    // Prepare items for batch update/create
    const itemsToProcess = []

    // Process each item
    for (const item of uniqueData) {
      try {
        // Check if the item has an ID
        if (item[config.idField]) {
          // Check if the item exists in our map
          const existingItem = existingItemsMap[item[config.idField]]

          if (existingItem) {
            // Check if all columns are the same (excluding lu_updated and lu_user_id)
            if (!areObjectsEqual(existingItem, item)) {
              // Update the item
              itemsToProcess.push({
                ...item,
                lu_updated: new Date().toISOString(),
                lu_user_id: userId,
              })
              results.updated++
            } else {
              // Skip if identical
              results.skipped++
            }
          } else {
            // Create new item with the provided ID
            itemsToProcess.push({
              ...item,
              lu_updated: new Date().toISOString(),
              lu_user_id: userId,
            })
            results.created++
          }
        } else {
          // Create new item with a new ID
          itemsToProcess.push({
            ...item,
            [config.idField]: uuidv4(),
            lu_updated: new Date().toISOString(),
            lu_user_id: userId,
            active_flag: item.active_flag !== undefined ? item.active_flag : true,
          })
          results.created++
        }
      } catch (error) {
        results.errors.push({
          item,
          error: error.message,
        })
      }
    }

    // Process all items in a single batch operation if there are items to process
    if (itemsToProcess.length > 0) {
      await patchMultiRecordDetails(config.list, itemsToProcess)
    }

    return results
  } catch (error) {
    handleError(error, config.errorContext)
    throw error
  }
}

/**
 * Generic function to import detail items (medical_supplies_detail, medicine_detail, technical_services_detail)
 * @param {Array} data - Data to import
 * @param {string} userId - User ID
 * @param {Object} config - Configuration object with list, idField, and errorContext
 * @returns {Promise<Object>} - Import results
 */
export const importDetailItems = async (data, userId, config) => {
  try {
    const results = {
      created: 0,
      skipped: 0,
      errors: [],
    }

    // Check if data is valid
    if (!data || !Array.isArray(data) || data.length === 0) {
      console.warn('No valid data provided for import')
      return results
    }

    // Process data - remove parentInfo from items
    const processedData = data.map((item) => {
      // Create a new object without parentInfo
      if (!item) return {}
      const { parentInfo, ...rest } = item
      return rest
    })

    // Fetch all existing items without filtering by IDs
    const existingItemsMap = {}
    try {
      // Fetch all records using our batch function
      const existingItems = await fetchAllRecords(config.list, {
        errorContext: `${config.errorContext}_fetchAll`,
      })

      // Create a map for quick lookup
      if (existingItems && existingItems.length > 0) {
        existingItems.forEach((item) => {
          if (item[config.idField]) {
            existingItemsMap[item[config.idField]] = item
          }
        })
      }
    } catch (error) {
      console.error(`Error fetching existing detail items: ${error.message}`)
    }

    // Prepare items for batch create
    const itemsToProcess = []

    // Process each item
    for (const item of processedData) {
      try {
        // Check if the item has an ID and if it exists
        if (item[config.idField]) {
          const existingItem = existingItemsMap[item[config.idField]]

          if (existingItem) {
            // Check if all columns are the same (excluding lu_updated and lu_user_id)
            if (!areObjectsEqual(existingItem, item)) {
              // Create a new detail record with a new ID
              itemsToProcess.push({
                ...item,
                [config.idField]: uuidv4(), // Always create a new ID for detail records
                lu_updated: new Date().toISOString(),
                lu_user_id: userId,
              })
              results.created++
            } else {
              // Skip if identical
              results.skipped++
            }
          } else {
            // Create with the provided ID
            itemsToProcess.push({
              ...item,
              lu_updated: new Date().toISOString(),
              lu_user_id: userId,
            })
            results.created++
          }
        } else {
          // Create new item with a new ID
          itemsToProcess.push({
            ...item,
            [config.idField]: uuidv4(),
            lu_updated: new Date().toISOString(),
            lu_user_id: userId,
          })
          results.created++
        }
      } catch (error) {
        results.errors.push({
          item,
          error: error.message,
        })
      }
    }

    // Process all items in a single batch operation if there are items to process
    if (itemsToProcess.length > 0) {
      await patchMultiRecordDetails(config.list, itemsToProcess)
    }

    return results
  } catch (error) {
    handleError(error, config.errorContext)
    throw error
  }
}
