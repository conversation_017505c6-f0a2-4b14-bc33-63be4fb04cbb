import React from 'react'
import { Form, Input, DatePicker, InputNumber, Select, Checkbox, Col } from 'antd'
import SSItemDetail from './SSItemDetail'
import {
  useTechnicalServiceDetails,
  useAddTechnicalServiceDetail,
  useDeleteTechnicalServiceItem,
} from './hooks/useSSItem'
import { ITEM_TYPES } from './SSItemConstant'
import dayjs from '../../common/dayjs'
import { FORMAT_DATE } from '../../common/constant'

// Detail columns for the history table
const detailColumns = [
  {
    title: 'Mã DVKT',
    dataIndex: 'MA_TUONG_DUONG',
    key: 'MA_TUONG_DUONG',
    width: 150,
  },
  {
    title: 'Tên DVKT phê duyệt',
    dataIndex: 'TEN_DVKT_PHEDUYET',
    key: 'TEN_DVKT_PHEDUYET',
    width: 300,
  },
  {
    title: 'Tên DVKT giá',
    dataIndex: 'TEN_DVKT_GIA',
    key: 'TEN_DVKT_GIA',
    width: 300,
  },
  {
    title: 'Đơn vị tính',
    dataIndex: 'DON_VI_TINH',
    key: 'DON_VI_TINH',
    width: 100,
  },
  {
    title: 'Đơn giá',
    dataIndex: 'DON_GIA',
    key: 'DON_GIA',
    width: 120,
    render: (value) => (value ? value.toLocaleString('vi-VN') : 'N/A'),
  },
  {
    title: 'Đơn giá BH',
    dataIndex: 'DON_GIA_BH',
    key: 'DON_GIA_BH',
    width: 120,
    render: (value) => (value ? value.toLocaleString('vi-VN') : 'N/A'),
  },
  {
    title: 'Từ ngày',
    dataIndex: 'TU_NGAY',
    key: 'TU_NGAY',
    width: 120,
    render: (date) => (date ? dayjs(date).format(FORMAT_DATE) : 'N/A'),
  },
  {
    title: 'Đến ngày',
    dataIndex: 'DEN_NGAY',
    key: 'DEN_NGAY',
    width: 120,
    render: (date) => (date ? dayjs(date).format(FORMAT_DATE) : 'N/A'),
  },
  {
    title: 'Cập nhật lúc',
    dataIndex: 'lu_updated',
    key: 'lu_updated',
    width: 150,
    render: (date) => (date ? dayjs(date).format('DD/MM/YYYY HH:mm:ss') : 'N/A'),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'active_flag',
    key: 'active_flag',
    width: 100,
    render: (active) => (active ? 'Có' : 'Không'),
  },
]

// Render form fields for the add/edit modal
const renderFields = () => (
  <>
    {/* Row 1 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item
        name="MA_TUONG_DUONG"
        label="Mã DVKT"
        rules={[{ required: true, message: 'Vui lòng nhập mã DVKT' }]}>
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item
        name="TEN_DVKT_PHEDUYET"
        label="Tên DVKT phê duyệt"
        rules={[{ required: true, message: 'Vui lòng nhập tên DVKT phê duyệt' }]}>
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="TEN_DVKT_GIA" label="Tên DVKT giá">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="PHAN_LOAI_PTTT" label="Phân loại PTTT">
        <Input />
      </Form.Item>
    </Col>

    {/* Row 2 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="DON_VI_TINH" label="Đơn vị tính">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="DON_GIA" label="Đơn giá">
        <InputNumber
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
        />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="DON_GIA_BH" label="Đơn giá BH">
        <InputNumber
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
        />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="QUYET_DINH" label="Quyết định">
        <Input />
      </Form.Item>
    </Col>

    {/* Row 3 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="TU_NGAY" label="Từ ngày">
        <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="DEN_NGAY" label="Đến ngày">
        <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="DON_GIA_AP_THAU" label="Đơn giá áp thầu">
        <InputNumber
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
        />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="TYLE_TT_DV" label="Tỷ lệ thanh toán DV">
        <InputNumber style={{ width: '100%' }} min={0} max={100} />
      </Form.Item>
    </Col>

    {/* Row 4 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="TYLE_TT_BH" label="Tỷ lệ thanh toán BH">
        <InputNumber style={{ width: '100%' }} min={0} max={100} />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="TYLE_TAI_SD" label="Tỷ lệ tái sử dụng">
        <InputNumber style={{ width: '100%' }} min={0} max={100} />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="MA_NHOM" label="Mã nhóm">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="MA_KHOA" label="Mã khoa">
        <Input />
      </Form.Item>
    </Col>

    {/* Row 5 */}
    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="SO_LAN_SU_DUNG" label="Số lần sử dụng">
        <Input />
      </Form.Item>
    </Col>

    <Col xs={24} sm={12} md={8} lg={6}>
      <Form.Item name="SO_LUONG_PHIM" label="Số lượng phim">
        <Input />
      </Form.Item>
    </Col>

    {/* TextArea fields take full width of their columns */}
    <Col xs={24} sm={24} md={12} lg={12}>
      <Form.Item name="GHI_CHU" label="Ghi chú">
        <Input.TextArea rows={2} />
      </Form.Item>
    </Col>

    <Col xs={24} sm={24} md={12} lg={12}>
      <Form.Item name="DIEU_KIEN_THANH_TOAN_BHYT" label="Điều kiện thanh toán BHYT">
        <Input.TextArea rows={4} />
      </Form.Item>
    </Col>

    <Col span={24}>
      <Form.Item name="active_flag" valuePropName="checked">
        <Checkbox>Kích hoạt</Checkbox>
      </Form.Item>
    </Col>
  </>
)

const TechnicalServiceDetail = () => {
  return (
    <SSItemDetail
      itemType={ITEM_TYPES.TECHNICAL_SERVICES}
      useItemDetails={useTechnicalServiceDetails}
      useAddItemDetail={useAddTechnicalServiceDetail}
      useDeleteItem={useDeleteTechnicalServiceItem}
      renderFields={renderFields}
      detailColumns={detailColumns}
    />
  )
}

export default TechnicalServiceDetail
