import React, { useState } from 'react'
import {
  Table,
  Input,
  Button,
  Card,
  Space,
  Modal,
  Form,
  DatePicker,
  Checkbox,
  Row,
  Col,
} from 'antd'
import { SearchOutlined, PlusOutlined } from '@ant-design/icons'
import PropTypes from '../../common/PropTypes'
import { useNavigate } from 'react-router-dom'
import {
  useAddMedicalSupplyItem,
  useAddMedicineItem,
  useAddTechnicalServiceItem,
  useMedicalSuppliesExcel,
  useMedicinesExcel,
  useTechnicalServicesExcel,
} from './hooks/useSSItem'
import SSItemExcel from './SSItemExcel'
import { ITEM_TYPES } from './SSItemConstant'
import { FORMAT_DATE } from '../../common/constant'

const propTypes = {
  title: PropTypes.string.isRequired,
  dataSource: PropTypes.array.isRequired,
  columns: PropTypes.array.isRequired,
  loading: PropTypes.bool,
  pagination: PropTypes.object,
  onPaginationChange: PropTypes.func,
  onSearch: PropTypes.func,
  itemType: PropTypes.string.isRequired,
}

const SSItemList = ({
  title,
  dataSource,
  columns,
  loading = false,
  pagination,
  onPaginationChange,
  onSearch,
  itemType,
}) => {
  const navigate = useNavigate()
  const [searchText, setSearchText] = useState('')
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [form] = Form.useForm()

  // Custom hooks for adding items
  const { addMedicalSupplyItem, isLoading: isAddingMedicalSupply } = useAddMedicalSupplyItem()
  const { addMedicineItem, isLoading: isAddingMedicine } = useAddMedicineItem()
  const { addTechnicalServiceItem, isLoading: isAddingTechnicalService } =
    useAddTechnicalServiceItem()

  // Excel hooks based on item type
  const medicalSuppliesExcel = useMedicalSuppliesExcel()
  const medicinesExcel = useMedicinesExcel()
  const technicalServicesExcel = useTechnicalServicesExcel()

  // Default column format configurations
  const DEFAULT_COLUMN_FORMATS = {
    TU_NGAY: 'YYYYMMDD',
    DEN_NGAY: 'YYYYMMDD',
  }

  // Get the appropriate Excel hook based on item type
  const getExcelHook = () => {
    const excelHooks = {
      [ITEM_TYPES.MEDICAL_SUPPLIES]: {
        exportDetailFunction: (id, columnFormats = DEFAULT_COLUMN_FORMATS) =>
          medicalSuppliesExcel.exportMedicalSuppliesDetails(id, columnFormats),
        importDetailFunction: medicalSuppliesExcel.importMedicalSuppliesDetails,
      },
      [ITEM_TYPES.MEDICINE]: {
        exportDetailFunction: (id, columnFormats = DEFAULT_COLUMN_FORMATS) =>
          medicinesExcel.exportMedicineDetails(id, columnFormats),
        importDetailFunction: medicinesExcel.importMedicineDetails,
      },
      [ITEM_TYPES.TECHNICAL_SERVICES]: {
        exportDetailFunction: (id, columnFormats = DEFAULT_COLUMN_FORMATS) =>
          technicalServicesExcel.exportTechnicalServiceDetails(id, columnFormats),
        importDetailFunction: technicalServicesExcel.importTechnicalServiceDetails,
      },
    }

    return excelHooks[itemType] || {}
  }

  // Determine if we're currently adding an item
  const isAdding = isAddingMedicalSupply || isAddingMedicine || isAddingTechnicalService

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchText(e.target.value)
  }

  // Handle search button click
  const handleSearch = () => {
    if (onSearch) {
      onSearch(searchText)
    }
  }

  // Handle key press in search input
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  // Handle row click to navigate to detail page
  const handleRowClick = (record) => {
    const idField = `${itemType}_id`
    navigate(`/ssitem/${itemType}/${record[idField]}`)
  }

  // Show modal for adding new item
  const showAddModal = () => {
    form.resetFields()
    setIsModalVisible(true)
  }

  // Handle modal cancel
  const handleCancel = () => {
    setIsModalVisible(false)
  }

  // Handle form submission
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()

      // Format dates if they exist
      if (values.TU_NGAY) {
        values.TU_NGAY = values.TU_NGAY.format(FORMAT_DATE)
      }
      if (values.DEN_NGAY) {
        values.DEN_NGAY = values.DEN_NGAY.format(FORMAT_DATE)
      }

      // Add user ID
      values.lu_user_id = localStorage.getItem('user_id') || null

      let newItemId = null

      // Call the appropriate add function based on itemType
      if (itemType === ITEM_TYPES.MEDICAL_SUPPLIES) {
        const result = await addMedicalSupplyItem(values)
        newItemId = result?.medical_supplies_id
      } else if (itemType === ITEM_TYPES.MEDICINE) {
        const result = await addMedicineItem(values)
        newItemId = result?.medicine_id
      } else if (itemType === ITEM_TYPES.TECHNICAL_SERVICES) {
        const result = await addTechnicalServiceItem(values)
        newItemId = result?.technical_services_id
      }

      setIsModalVisible(false)

      // Navigate to detail page if we have an ID
      if (newItemId) {
        navigate(`/ssitem/${itemType}/${newItemId}`)
      } else {
        // Otherwise just refresh the list
        if (onSearch) {
          onSearch(searchText)
        }
      }
    } catch (error) {
      console.error('Form validation failed:', error)
    }
  }

  // Render form fields based on itemType
  const renderFormFields = () => {
    if (itemType === ITEM_TYPES.MEDICAL_SUPPLIES) {
      return (
        <>
          <Row gutter={[16, 0]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="MA_VAT_TU"
                label="Mã VTYT"
                rules={[{ required: true, message: 'Vui lòng nhập mã VTYT' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="TEN_VAT_TU"
                label="Tên VTYT"
                rules={[{ required: true, message: 'Vui lòng nhập tên VTYT' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="NHOM_VAT_TU" label="Nhóm VTYT">
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="MA_NHOM_VAT_TU" label="Mã nhóm VTYT">
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="TU_NGAY" label="Từ ngày">
                <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="DEN_NGAY" label="Đến ngày">
                <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item name="active_flag" valuePropName="checked" initialValue={true}>
                <Checkbox>Kích hoạt</Checkbox>
              </Form.Item>
            </Col>
          </Row>
        </>
      )
    } else if (itemType === ITEM_TYPES.MEDICINE) {
      return (
        <>
          <Row gutter={[16, 0]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="MA_THUOC"
                label="Mã thuốc"
                rules={[{ required: true, message: 'Vui lòng nhập mã thuốc' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="TEN_THUOC"
                label="Tên thuốc"
                rules={[{ required: true, message: 'Vui lòng nhập tên thuốc' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="TEN_HOAT_CHAT" label="Tên hoạt chất">
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="HAM_LUONG" label="Hàm lượng">
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="DON_VI_TINH" label="Đơn vị tính">
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="TU_NGAY" label="Từ ngày">
                <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="DEN_NGAY" label="Đến ngày">
                <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item name="active_flag" valuePropName="checked" initialValue={true}>
                <Checkbox>Kích hoạt</Checkbox>
              </Form.Item>
            </Col>
          </Row>
        </>
      )
    } else if (itemType === ITEM_TYPES.TECHNICAL_SERVICES) {
      return (
        <>
          <Row gutter={[16, 0]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="MA_TUONG_DUONG"
                label="Mã DVKT"
                rules={[{ required: true, message: 'Vui lòng nhập mã DVKT' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="TEN_DVKT_PHEDUYET"
                label="Tên DVKT phê duyệt"
                rules={[{ required: true, message: 'Vui lòng nhập tên DVKT phê duyệt' }]}>
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="TEN_DVKT_GIA" label="Tên DVKT giá">
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="TU_NGAY" label="Từ ngày">
                <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="DEN_NGAY" label="Đến ngày">
                <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item name="active_flag" valuePropName="checked" initialValue={true}>
                <Checkbox>Kích hoạt</Checkbox>
              </Form.Item>
            </Col>
          </Row>
        </>
      )
    }

    return null
  }

  // Add action column to the columns
  const tableColumns = [
    ...columns,
    {
      title: 'Hành động',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button type="link" onClick={() => handleRowClick(record)}>
          Chi tiết
        </Button>
      ),
    },
  ]

  return (
    <Card title={title} style={{ marginBottom: 16 }}>
      <Row justify="space-between" style={{ marginBottom: 16 }}>
        <Col>
          <Space>
            <Input
              placeholder="Tìm kiếm..."
              value={searchText}
              onChange={handleSearchChange}
              onKeyPress={handleKeyPress}
              style={{ width: 300 }}
              suffix={<SearchOutlined style={{ cursor: 'pointer' }} onClick={handleSearch} />}
            />
            <Button type="primary" onClick={handleSearch}>
              Tìm kiếm
            </Button>
          </Space>
        </Col>
        <Col>
          <Space>
            {/* Excel Import/Export Component */}
            <SSItemExcel
              itemType={itemType}
              exportDetailFunction={getExcelHook().exportDetailFunction}
              importDetailFunction={getExcelHook().importDetailFunction}
              onImportComplete={() => onSearch(searchText)}
              columnFormats={DEFAULT_COLUMN_FORMATS}
            />
            <Button type="primary" icon={<PlusOutlined />} onClick={showAddModal}>
              Thêm mới
            </Button>
          </Space>
        </Col>
      </Row>

      <Table
        dataSource={dataSource}
        columns={tableColumns}
        rowKey={(record) => record[`${itemType}_id`]}
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showTotal: (total) => `Tổng số ${total} bản ghi`,
        }}
        onChange={onPaginationChange}
        onRow={(record) => ({
          onClick: () => handleRowClick(record),
          style: { cursor: 'pointer' },
        })}
        scroll={{ x: 'max-content' }}
      />

      {/* Modal for adding new item */}
      <Modal
        title={`Thêm mới ${
          itemType === ITEM_TYPES.MEDICAL_SUPPLIES
            ? 'vật tư y tế'
            : itemType === ITEM_TYPES.MEDICINE
              ? 'thuốc'
              : 'dịch vụ kỹ thuật'
        }`}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        confirmLoading={isAdding}
        width={800}>
        <Form form={form} layout="vertical">
          {renderFormFields()}
        </Form>
      </Modal>
    </Card>
  )
}

SSItemList.propTypes = propTypes

export default SSItemList
