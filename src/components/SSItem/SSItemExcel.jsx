import React, { useState, useMemo, useEffect } from 'react'
import { Button, Upload, Modal, Table, Space, Typography } from 'antd'
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons'
import { utils, writeFile, read } from 'xlsx'
import PropTypes from 'prop-types'
import { useUI } from '../../common/UIProvider'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import AsyncButton from '../../common/components/AsyncButton'
import COLOR from '../../common/color'
import { ITEM_TYPES } from './SSItemConstant'

const { Title, Text } = Typography

// Default column format configurations
const DEFAULT_COLUMN_FORMATS = {
  TU_NGAY: 'YYYYMMDD',
  DEN_NGAY: 'YYYYMMDD',
}

// Title mapping for different item types
const ITEM_TITLES = {
  [ITEM_TYPES.MEDICAL_SUPPLIES]: {
    parent: 'Vật tư y tế',
    detail: 'Chi tiết vật tư y tế',
  },
  [ITEM_TYPES.MEDICINE]: {
    parent: 'Thuốc',
    detail: 'Chi tiết thuốc',
  },
  [ITEM_TYPES.TECHNICAL_SERVICES]: {
    parent: 'Dịch vụ kỹ thuật',
    detail: 'Chi tiết dịch vụ kỹ thuật',
  },
}

/**
 * SSItemExcel component for importing and exporting Excel files
 *
 * This component handles:
 * 1. Export of detail records to Excel
 * 2. Import of detail records with automatic parent record extraction
 * 3. Only detail records are included in Excel files, parent info is extracted automatically
 * 4. Configurable column formats for export (e.g., date formats)
 */
const SSItemExcel = ({
  itemType,
  exportDetailFunction,
  importDetailFunction,
  onImportComplete,
  columnFormats = DEFAULT_COLUMN_FORMATS,
}) => {
  const ui = useUI()
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  // State
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [importedData, setImportedData] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [importResults, setImportResults] = useState(null)

  // Get title based on item type using memoization
  const titles = useMemo(() => {
    return (
      ITEM_TITLES[itemType] || {
        parent: 'Danh mục',
        detail: 'Chi tiết danh mục',
      }
    )
  }, [itemType])

  // Handle export
  const handleExport = async () => {
    try {
      setIsLoading(true)

      // Always use the detail export function with column formats
      const data = await exportDetailFunction(null, columnFormats)

      if (data && data.length > 0) {
        // Process data before export if needed
        let exportData = data

        // Clean up the data by removing unnecessary fields
        exportData = data.map((item) => {
          // If parentInfo exists, extract it but exclude some fields
          if (item.parentInfo) {
            // We don't need to use parentInfo in the exported file
            // eslint-disable-next-line no-unused-vars
            const { parentInfo, ...rest } = item
            return rest
          }
          return item
        })

        const ws = utils.json_to_sheet(exportData)
        const wb = utils.book_new()
        utils.book_append_sheet(wb, ws, 'Data')

        const fileName = `${titles.detail}_${new Date().toISOString().split('T')[0]}.xlsx`
        writeFile(wb, fileName)
        ui.notiSuccess(`Xuất file ${fileName} thành công`)
      } else {
        ui.notiWarning('Không có dữ liệu để xuất')
      }
    } catch (error) {
      ui.notiError('Xuất file thất bại: ' + error.message)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle export of error records
  const handleExportErrors = () => {
    try {
      if (!importResults || !importResults.errors || importResults.errors.length === 0) {
        ui.notiWarning('Không có dữ liệu lỗi để xuất')
        return
      }

      // Process error records to include error message as first column
      const exportData = importResults.errors.map((errorRecord) => {
        // Create a new object with error message as first property
        const { error, item = {} } = errorRecord

        // Handle case where item might be null or undefined
        const itemData = item || {}

        // Create a new object with error message as first property
        // and spread the item properties after
        return {
          'Thông tin lỗi': error || 'Lỗi không xác định',
          ...itemData,
        }
      })

      // Create worksheet and workbook
      const ws = utils.json_to_sheet(exportData)
      const wb = utils.book_new()
      utils.book_append_sheet(wb, ws, 'Lỗi')

      // Generate filename and write file
      const fileName = `${titles.detail}_Errors_${new Date().toISOString().split('T')[0]}.xlsx`
      writeFile(wb, fileName)
      ui.notiSuccess(`Xuất file lỗi ${fileName} thành công`)
    } catch (error) {
      ui.notiError('Xuất file lỗi thất bại: ' + error.message)
    }
  }

  // Handle import
  const handleImport = (file) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = utils.sheet_to_json(worksheet)

        if (jsonData && jsonData.length > 0) {
          setImportedData(jsonData)
          setIsModalVisible(true)
        } else {
          ui.notiWarning('File không có dữ liệu')
        }
      } catch (error) {
        ui.notiError('Đọc file thất bại: ' + error.message)
      }
    }
    reader.readAsArrayBuffer(file)
    return false // Prevent automatic upload
  }

  // Handle save imported data
  const handleSaveImport = async () => {
    try {
      setIsLoading(true)

      // Check if we have valid data to import
      if (!importedData || !Array.isArray(importedData) || importedData.length === 0) {
        ui.notiWarning('Không có dữ liệu hợp lệ để import')
        setIsLoading(false)
        return
      }

      // Ensure data is properly formatted before passing to import function
      // Filter out any null or undefined items
      const validData = importedData.filter((item) => item !== null && item !== undefined)

      if (validData.length === 0) {
        ui.notiWarning('Không có dữ liệu hợp lệ để import sau khi lọc')
        setIsLoading(false)
        return
      }

      // Get user ID from currentUser, handling different possible property names
      const userId =
        currentUser?.user_id || currentUser?.User_id || currentUser?.userId || currentUser?.UserId

      if (!userId) {
        console.error('No user ID found in currentUser:', currentUser)
        throw new Error('Không tìm thấy ID người dùng. Vui lòng đăng nhập lại.')
      }

      // Always use the detail import function
      // This will automatically extract parent records from detail records
      const results = await importDetailFunction({
        data: validData,
        userId: userId,
      })

      setImportResults(results)

      if (results) {
        ui.notiSuccess(
          `Import thành công: ${results.created} tạo mới, ${results.updated || 0} cập nhật, ${results.skipped} bỏ qua`,
        )

        if (results.errors && results.errors.length > 0) {
          ui.notiWarning(`Có ${results.errors.length} lỗi trong quá trình import`)
        }

        // Call the callback function if provided
        if (onImportComplete) {
          onImportComplete()
        }
      }
    } catch (error) {
      console.error('Import error:', error)
      ui.notiError('Import thất bại: ' + (error.message || 'Lỗi không xác định'))

      // Create an error result to display in the UI
      setImportResults({
        created: 0,
        skipped: 0,
        updated: 0,
        errors: [
          {
            error: error.message || 'Lỗi không xác định',
            item: importedData && importedData.length > 0 ? importedData[0] : {},
          },
        ],
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle cancel import
  const handleCancelImport = () => {
    setIsModalVisible(false)
    setImportedData(null)
    setImportResults(null)
  }

  // Generate columns for preview table - memoized to avoid recalculation
  const generatePreviewColumns = useMemo(() => {
    if (!importedData || importedData.length === 0) return []

    const firstRow = importedData[0]
    return Object.keys(firstRow)
      .filter((key) => key !== '_previewKey') // Exclude our internal key
      .map((key) => ({
        title: key,
        dataIndex: key,
        key: key,
        ellipsis: true,
      }))
  }, [importedData])

  // Generate results columns - memoized to avoid recalculation
  const resultsColumns = useMemo(
    () => [
      {
        title: 'Trạng thái',
        key: 'status',
        render: () => <Text type="danger">Lỗi</Text>,
      },
      {
        title: 'Thông tin lỗi',
        dataIndex: 'error',
        key: 'error',
      },
      {
        title: 'Dữ liệu',
        key: 'item',
        render: (_, record) => {
          // Filter out our internal key from display
          const { _errorKey, ...displayItem } = record.item || {}
          return (
            <pre style={{ maxHeight: '100px', overflow: 'auto' }}>
              {JSON.stringify(displayItem, null, 2)}
            </pre>
          )
        },
      },
    ],
    [],
  )

  return (
    <>
      <Space>
        <AsyncButton
          style={{
            backgroundColor: COLOR.lightgreen,
            color: COLOR.lime,
            borderColor: 'white',
          }}
          loading={isLoading}
          onClick={handleExport}
          icon={<DownloadOutlined />}>
          Xuất Excel
        </AsyncButton>

        <Upload
          accept=".xlsx, .xls"
          beforeUpload={handleImport}
          showUploadList={false}
          disabled={isLoading}>
          <Button
            style={{
              backgroundColor: COLOR.lime,
              color: 'white',
              borderColor: 'white',
            }}
            loading={isLoading}
            icon={<UploadOutlined />}>
            Nhập Excel
          </Button>
        </Upload>
      </Space>

      <Modal
        title={`Import ${titles.detail}`}
        open={isModalVisible}
        onCancel={handleCancelImport}
        width={1000}
        footer={[
          <Button key="cancel" onClick={handleCancelImport}>
            Hủy
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={isLoading}
            onClick={handleSaveImport}
            disabled={importResults !== null}>
            Import
          </Button>,
        ]}>
        {importedData && !importResults && (
          <>
            <Title level={5}>Xem trước dữ liệu ({importedData.length} bản ghi)</Title>
            <Table
              dataSource={importedData
                .slice(0, 10)
                .map((item, idx) => ({ ...item, _previewKey: `preview_${idx}` }))}
              columns={generatePreviewColumns}
              rowKey="_previewKey"
              scroll={{ x: 'max-content' }}
              pagination={false}
              size="small"
            />
            {importedData.length > 10 && (
              <Text type="secondary">Hiển thị 10/{importedData.length} bản ghi</Text>
            )}
          </>
        )}

        {importResults && importResults.errors && importResults.errors.length > 0 && (
          <>
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '10px',
              }}>
              <Title level={5} style={{ margin: 0 }}>
                Lỗi ({importResults.errors.length} bản ghi)
              </Title>
              <Button type="primary" onClick={handleExportErrors} icon={<DownloadOutlined />}>
                Xuất lỗi ra Excel
              </Button>
            </div>
            <Table
              dataSource={importResults.errors.map((item, idx) => ({
                ...item,
                _errorKey: `error_${idx}`,
              }))}
              columns={resultsColumns}
              rowKey="_errorKey"
              scroll={{ x: 'max-content' }}
              pagination={{ pageSize: 5 }}
              size="small"
            />
          </>
        )}
      </Modal>
    </>
  )
}

SSItemExcel.propTypes = {
  itemType: PropTypes.string.isRequired,
  exportDetailFunction: PropTypes.func.isRequired,
  importDetailFunction: PropTypes.func.isRequired,
  onImportComplete: PropTypes.func,
  columnFormats: PropTypes.object,
}

export default SSItemExcel
