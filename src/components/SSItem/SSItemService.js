import { handleError } from '../../common/helpers'
import lists from '../../common/lists'
import {
  getItemsService,
  getItemService,
  addListItemService,
  updateListItemService,
  deleteListItemService,
} from '../../common/services'
import { v4 as uuidv4 } from 'uuid'

// Add Medical Supply
export const addMedicalSupply = async (data) => {
  try {
    const newItem = {
      ...data,
      medical_supplies_id: uuidv4(),
      lu_updated: new Date().toISOString(),
      active_flag: true,
    }
    return await addListItemService(lists.medical_supplies, newItem)
  } catch (error) {
    handleError(error, 'addMedicalSupply')
    throw error
  }
}

// Add Medicine
export const addMedicine = async (data) => {
  try {
    const newItem = {
      ...data,
      medicine_id: uuidv4(),
      lu_updated: new Date().toISOString(),
      active_flag: true,
    }
    return await addListItemService(lists.medicine, newItem)
  } catch (error) {
    handleError(error, 'addMedicine')
    throw error
  }
}

// Add Technical Service
export const addTechnicalService = async (data) => {
  try {
    const newItem = {
      ...data,
      technical_services_id: uuidv4(),
      lu_updated: new Date().toISOString(),
      active_flag: true,
    }
    return await addListItemService(lists.technical_services, newItem)
  } catch (error) {
    handleError(error, 'addTechnicalService')
    throw error
  }
}

// Get Medical Supplies
export const getMedicalSupplies = async (options = {}) => {
  try {
    let filter = options.filter || '(active_flag eq true)'
    const searchText = options.searchText || ''

    if (searchText) {
      filter = `${filter} and (contains(MA_VAT_TU, '${searchText}') or contains(TEN_VAT_TU, '${searchText}'))`
    }

    const orderBy = options.orderBy || 'lu_updated desc'
    const data = await getItemsService(lists.medical_supplies, {
      filter,
      orderBy,
      top: options.top || 50,
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getMedicalSupplies')
    throw error
  }
}

// Get Medical Supply by ID
export const getMedicalSupplyById = async (id) => {
  try {
    const response = await getItemService(lists.medical_supplies, id)
    // Return the response directly, it already contains the item data
    return response
  } catch (error) {
    handleError(error, 'getMedicalSupplyById')
    throw error
  }
}

// Get Medical Supply Details
export const getMedicalSupplyDetails = async (medicalSupplyId) => {
  try {
    const filter = `(medical_supplies_id eq ${medicalSupplyId})`
    const data = await getItemsService(lists.medical_supplies_detail, {
      filter,
      orderBy: 'lu_updated desc',
    })
    return data
  } catch (error) {
    handleError(error, 'getMedicalSupplyDetails')
    throw error
  }
}

// Add Medical Supply Detail
export const addMedicalSupplyDetail = async (detail) => {
  try {
    const newDetail = {
      ...detail,
      medical_supplies_detail_id: uuidv4(),
      lu_updated: new Date().toISOString(),
    }
    return await addListItemService(lists.medical_supplies_detail, newDetail)
  } catch (error) {
    handleError(error, 'addMedicalSupplyDetail')
    throw error
  }
}

// Update Medical Supply Detail
export const updateMedicalSupplyDetail = async (detailId, detail) => {
  try {
    const updatedDetail = {
      ...detail,
      lu_updated: new Date().toISOString(),
    }
    return await updateListItemService(lists.medical_supplies_detail, detailId, updatedDetail)
  } catch (error) {
    handleError(error, 'updateMedicalSupplyDetail')
    throw error
  }
}

// Delete Medical Supply Detail
export const deleteMedicalSupplyDetail = async (detailId) => {
  try {
    return await deleteListItemService(lists.medical_supplies_detail, detailId)
  } catch (error) {
    handleError(error, 'deleteMedicalSupplyDetail')
    throw error
  }
}

// Delete Medical Supply with all its details
export const deleteMedicalSupply = async (id) => {
  try {
    // Get all details for this medical supply
    const detailsResponse = await getMedicalSupplyDetails(id)
    const details = detailsResponse.value || []

    // Delete all detail records first
    for (const detail of details) {
      await deleteListItemService(lists.medical_supplies_detail, detail.medical_supplies_detail_id)
    }

    // Then delete the main record
    return await deleteListItemService(lists.medical_supplies, id)
  } catch (error) {
    handleError(error, 'deleteMedicalSupply')
    throw error
  }
}

// Get Medicines
export const getMedicines = async (options = {}) => {
  try {
    let filter = options.filter || '(active_flag eq true)'
    const searchText = options.searchText || ''

    if (searchText) {
      filter = `${filter} and (contains(MA_THUOC, '${searchText}') or contains(TEN_THUOC, '${searchText}'))`
    }

    const orderBy = options.orderBy || 'lu_updated desc'
    const data = await getItemsService(lists.medicine, {
      filter,
      orderBy,
      top: options.top || 50,
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getMedicines')
    throw error
  }
}

// Get Medicine by ID
export const getMedicineById = async (id) => {
  try {
    const response = await getItemService(lists.medicine, id)
    // Return the response directly, it already contains the item data
    return response
  } catch (error) {
    handleError(error, 'getMedicineById')
    throw error
  }
}

// Get Medicine Details
export const getMedicineDetails = async (medicineId) => {
  try {
    const filter = `(medicine_id eq ${medicineId})`
    const data = await getItemsService(lists.medicine_detail, {
      filter,
      orderBy: 'lu_updated desc',
    })
    return data
  } catch (error) {
    handleError(error, 'getMedicineDetails')
    throw error
  }
}

// Add Medicine Detail
export const addMedicineDetail = async (detail) => {
  try {
    const newDetail = {
      ...detail,
      medicine_detail_id: uuidv4(),
      lu_updated: new Date().toISOString(),
    }
    return await addListItemService(lists.medicine_detail, newDetail)
  } catch (error) {
    handleError(error, 'addMedicineDetail')
    throw error
  }
}

// Update Medicine Detail
export const updateMedicineDetail = async (detailId, detail) => {
  try {
    const updatedDetail = {
      ...detail,
      lu_updated: new Date().toISOString(),
    }
    return await updateListItemService(lists.medicine_detail, detailId, updatedDetail)
  } catch (error) {
    handleError(error, 'updateMedicineDetail')
    throw error
  }
}

// Delete Medicine Detail
export const deleteMedicineDetail = async (detailId) => {
  try {
    return await deleteListItemService(lists.medicine_detail, detailId)
  } catch (error) {
    handleError(error, 'deleteMedicineDetail')
    throw error
  }
}

// Delete Medicine with all its details
export const deleteMedicine = async (id) => {
  try {
    // Get all details for this medicine
    const detailsResponse = await getMedicineDetails(id)
    const details = detailsResponse.value || []

    // Delete all detail records first
    for (const detail of details) {
      await deleteListItemService(lists.medicine_detail, detail.medicine_detail_id)
    }

    // Then delete the main record
    return await deleteListItemService(lists.medicine, id)
  } catch (error) {
    handleError(error, 'deleteMedicine')
    throw error
  }
}

// Get Technical Services
export const getTechnicalServices = async (options = {}) => {
  try {
    let filter = options.filter || '(active_flag eq true)'
    const searchText = options.searchText || ''

    if (searchText) {
      filter = `${filter} and (contains(MA_TUONG_DUONG, '${searchText}') or contains(TEN_DVKT_PHEDUYET, '${searchText}'))`
    }

    const orderBy = options.orderBy || 'lu_updated desc'
    const data = await getItemsService(lists.technical_services, {
      filter,
      orderBy,
      top: options.top || 50,
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getTechnicalServices')
    throw error
  }
}

// Get Technical Service by ID
export const getTechnicalServiceById = async (id) => {
  try {
    const response = await getItemService(lists.technical_services, id)
    // Return the response directly, it already contains the item data
    return response
  } catch (error) {
    handleError(error, 'getTechnicalServiceById')
    throw error
  }
}

// Get Technical Service Details
export const getTechnicalServiceDetails = async (technicalServiceId) => {
  try {
    const filter = `(technical_services_id eq ${technicalServiceId})`
    const data = await getItemsService(lists.technical_services_detail, {
      filter,
      orderBy: 'lu_updated desc',
    })
    return data
  } catch (error) {
    handleError(error, 'getTechnicalServiceDetails')
    throw error
  }
}

// Add Technical Service Detail
export const addTechnicalServiceDetail = async (detail) => {
  try {
    const newDetail = {
      ...detail,
      technical_services_detail_id: uuidv4(),
      lu_updated: new Date().toISOString(),
    }
    return await addListItemService(lists.technical_services_detail, newDetail)
  } catch (error) {
    handleError(error, 'addTechnicalServiceDetail')
    throw error
  }
}

// Update Technical Service Detail
export const updateTechnicalServiceDetail = async (detailId, detail) => {
  try {
    const updatedDetail = {
      ...detail,
      lu_updated: new Date().toISOString(),
    }
    return await updateListItemService(lists.technical_services_detail, detailId, updatedDetail)
  } catch (error) {
    handleError(error, 'updateTechnicalServiceDetail')
    throw error
  }
}

// Delete Technical Service Detail
export const deleteTechnicalServiceDetail = async (detailId) => {
  try {
    return await deleteListItemService(lists.technical_services_detail, detailId)
  } catch (error) {
    handleError(error, 'deleteTechnicalServiceDetail')
    throw error
  }
}

// Delete Technical Service with all its details
export const deleteTechnicalService = async (id) => {
  try {
    // Get all details for this technical service
    const detailsResponse = await getTechnicalServiceDetails(id)
    const details = detailsResponse.value || []

    // Delete all detail records first
    for (const detail of details) {
      await deleteListItemService(
        lists.technical_services_detail,
        detail.technical_services_detail_id,
      )
    }

    // Then delete the main record
    return await deleteListItemService(lists.technical_services, id)
  } catch (error) {
    handleError(error, 'deleteTechnicalService')
    throw error
  }
}
