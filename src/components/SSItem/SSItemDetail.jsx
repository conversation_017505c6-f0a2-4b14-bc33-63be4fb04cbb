import React, { useState, useEffect, useMemo } from 'react'
import {
  Card,
  Table,
  Button,
  Form,
  Modal,
  Spin,
  Space,
  Divider,
  Popconfirm,
  Row,
  Col,
  message,
} from 'antd'
import { useParams, useNavigate } from 'react-router-dom'
import PropTypes from '../../common/PropTypes'
import { useUI } from '../../common/UIProvider'
import {
  ArrowLeftOutlined,
  SaveOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons'
import dayjs from '../../common/dayjs'
import { FORMAT_DATE } from '../../common/constant'
import { v4 as uuidv4 } from 'uuid'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import { isEqual } from 'lodash'
import SSItemExcel from './SSItemExcel'
import { ITEM_TYPES } from './SSItemConstant'
import {
  useMedicalSuppliesExcel,
  useMedicinesExcel,
  useTechnicalServicesExcel,
} from './hooks/useSSItem'

const propTypes = {
  itemType: PropTypes.string.isRequired,
  useItemDetails: PropTypes.func.isRequired,
  useAddItemDetail: PropTypes.func.isRequired,
  useDeleteItem: PropTypes.func.isRequired,
  renderFields: PropTypes.func.isRequired,
  detailColumns: PropTypes.array.isRequired,
}

const SSItemDetail = ({
  itemType,
  useItemDetails,
  useAddItemDetail,
  useDeleteItem,
  renderFields,
  detailColumns,
}) => {
  const { id } = useParams()
  const navigate = useNavigate()
  const ui = useUI()
  const [form] = Form.useForm()
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  // State
  const [formChanged, setFormChanged] = useState(false)
  const [initialValues, setInitialValues] = useState({})

  // Custom hooks
  // Get the correct property names based on itemType
  let itemPropName = itemType
  let detailsPropName = `${itemType}Details`

  // Handle special cases for property names
  if (itemType === 'medical_supplies') {
    itemPropName = 'medical_supplies'
    detailsPropName = 'medical_suppliesDetails'
  } else if (itemType === 'technical_services') {
    itemPropName = 'technical_services'
    detailsPropName = 'technical_servicesDetails'
  }

  const {
    [itemPropName]: item,
    [detailsPropName]: details,
    isLoading,
    refetch,
  } = useItemDetails(id)

  // Get the correct add function name based on itemType
  let addDetailFunctionName = ''
  if (itemType === 'medical_supplies') {
    addDetailFunctionName = 'addMedicalSupplyDetail'
  } else if (itemType === 'medicine') {
    addDetailFunctionName = 'addMedicineDetail'
  } else if (itemType === 'technical_services') {
    addDetailFunctionName = 'addTechnicalServiceDetail'
  }

  const { [addDetailFunctionName]: addDetail, isLoading: isAdding } = useAddItemDetail()

  // Get the latest detail
  const latestDetail = details && details.length > 0 ? details[0] : null

  // Call hooks at the top level
  const medicalSuppliesExcelHook = useMedicalSuppliesExcel()
  const medicinesExcelHook = useMedicinesExcel()
  const technicalServicesExcelHook = useTechnicalServicesExcel()

  // Get Excel hook based on item type using memoization
  const excelHook = useMemo(() => {
    const hooks = {
      [ITEM_TYPES.MEDICAL_SUPPLIES]: {
        exportDetailFunction: (id, columnFormats) =>
          medicalSuppliesExcelHook.exportMedicalSuppliesDetails(id, columnFormats),
        importDetailFunction: medicalSuppliesExcelHook.importMedicalSuppliesDetails,
      },
      [ITEM_TYPES.MEDICINE]: {
        exportDetailFunction: (id, columnFormats) =>
          medicinesExcelHook.exportMedicineDetails(id, columnFormats),
        importDetailFunction: medicinesExcelHook.importMedicineDetails,
      },
      [ITEM_TYPES.TECHNICAL_SERVICES]: {
        exportDetailFunction: (id, columnFormats) =>
          technicalServicesExcelHook.exportTechnicalServiceDetails(id, columnFormats),
        importDetailFunction: technicalServicesExcelHook.importTechnicalServiceDetails,
      },
    }

    return hooks[itemType] || {}
  }, [itemType, medicalSuppliesExcelHook, medicinesExcelHook, technicalServicesExcelHook])

  // Initialize form with latest detail data
  useEffect(() => {
    if (latestDetail) {
      const formValues = { ...latestDetail }

      // Convert dates to dayjs objects
      if (formValues.TU_NGAY) {
        formValues.TU_NGAY = dayjs(formValues.TU_NGAY)
      }
      if (formValues.DEN_NGAY) {
        formValues.DEN_NGAY = dayjs(formValues.DEN_NGAY)
      }

      form.setFieldsValue(formValues)
      setInitialValues(formValues)
    } else if (item) {
      // If no detail records exist, use the main item data
      const formValues = { ...item }

      // Convert dates to dayjs objects
      if (formValues.TU_NGAY) {
        formValues.TU_NGAY = dayjs(formValues.TU_NGAY)
      }
      if (formValues.DEN_NGAY) {
        formValues.DEN_NGAY = dayjs(formValues.DEN_NGAY)
      }

      form.setFieldsValue(formValues)
      setInitialValues(formValues)
    }
  }, [latestDetail, item, form])

  // Handle back button click
  const handleBack = () => {
    navigate('/ssitem')
  }

  // Check if form values have changed from initial values
  const checkFormChanged = () => {
    const currentValues = form.getFieldsValue()

    // Compare current form values with initial values
    return !isEqual(
      // Normalize values for comparison
      Object.entries(currentValues).reduce((acc, [key, value]) => {
        // Skip internal fields
        if (key === 'lu_updated' || key === 'lu_user_id' || key === `${itemType}_detail_id`) {
          return acc
        }

        // Format dates for comparison
        if (key === 'TU_NGAY' || key === 'DEN_NGAY') {
          acc[key] = value ? value.format(FORMAT_DATE) : null
        } else {
          acc[key] = value
        }

        return acc
      }, {}),
      // Normalize initial values for comparison
      Object.entries(initialValues).reduce((acc, [key, value]) => {
        // Skip internal fields
        if (key === 'lu_updated' || key === 'lu_user_id' || key === `${itemType}_detail_id`) {
          return acc
        }

        // Format dates for comparison
        if (key === 'TU_NGAY' || key === 'DEN_NGAY') {
          acc[key] = value ? (typeof value === 'string' ? value : value.format(FORMAT_DATE)) : null
        } else {
          acc[key] = value
        }

        return acc
      }, {}),
    )
  }

  // Get the correct delete function name based on itemType
  let deleteItemFunctionName = ''
  if (itemType === 'medical_supplies') {
    deleteItemFunctionName = 'deleteMedicalSupplyItem'
  } else if (itemType === 'medicine') {
    deleteItemFunctionName = 'deleteMedicineItem'
  } else if (itemType === 'technical_services') {
    deleteItemFunctionName = 'deleteTechnicalServiceItem'
  }

  const { [deleteItemFunctionName]: deleteItem, isLoading: isDeleting } = useDeleteItem()

  // Handle delete main item
  const handleDeleteMainItem = () => {
    Modal.confirm({
      title: 'Xác nhận xóa',
      icon: <ExclamationCircleOutlined />,
      content: 'Bạn có chắc chắn muốn xóa dữ liệu này? Hành động này không thể hoàn tác.',
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      onOk: async () => {
        try {
          // Call API to delete the main item with all its details
          await deleteItem(id)
          message.success('Đã xóa dữ liệu thành công')
          navigate('/ssitem')
        } catch (error) {
          ui.notiError('Xóa dữ liệu thất bại: ' + error.message)
        }
      },
    })
  }

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (!item) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h2>Không tìm thấy dữ liệu</h2>
        <Button type="primary" onClick={handleBack}>
          <ArrowLeftOutlined /> Quay lại
        </Button>
      </div>
    )
  }

  // Handle save button click
  const handleSave = async () => {
    try {
      const values = await form.validateFields()

      // Format dates
      if (values.TU_NGAY) {
        values.TU_NGAY = values.TU_NGAY.format(FORMAT_DATE)
      }
      if (values.DEN_NGAY) {
        values.DEN_NGAY = values.DEN_NGAY.format(FORMAT_DATE)
      }

      // Add required fields for detail record
      let idField = `${itemType}_id`
      let detailIdField = `${itemType}_detail_id`

      // Handle special cases for field names
      if (itemType === 'medical_supplies') {
        idField = 'medical_supplies_id'
        detailIdField = 'medical_supplies_detail_id'
      } else if (itemType === 'technical_services') {
        idField = 'technical_services_id'
        detailIdField = 'technical_services_detail_id'
      }

      const detailData = {
        ...values,
        [idField]: id,
        [detailIdField]: uuidv4(),
        lu_user_id: currentUser?.user_id,
        lu_updated: new Date().toISOString(),
        active_flag: true,
      }

      // Create a new detail record
      await addDetail(detailData)

      // Update the main record with the same data
      // This would need to be implemented in the service
      // For now, we'll just show a success message
      message.success('Đã lưu thành công')

      // Refresh data
      refetch()
      setFormChanged(false)
    } catch (error) {
      ui.notiError('Lưu thất bại: ' + error.message)
    }
  }

  return (
    <div style={{ padding: '20px' }}>
      <Row justify="space-between" style={{ marginBottom: 16 }}>
        <Col>
          <Space>
            <Button type="primary" onClick={handleBack}>
              <ArrowLeftOutlined /> Quay lại
            </Button>
            <Popconfirm
              title="Bạn có chắc chắn muốn xóa?"
              onConfirm={handleDeleteMainItem}
              okText="Có"
              cancelText="Không">
              <Button type="primary" danger icon={<DeleteOutlined />}>
                Xóa
              </Button>
            </Popconfirm>
          </Space>
        </Col>
        <Col>
          <Space>
            {/* Excel Import/Export Component */}
            <SSItemExcel
              itemType={itemType}
              exportDetailFunction={excelHook.exportDetailFunction}
              importDetailFunction={excelHook.importDetailFunction}
              onImportComplete={refetch}
              columnFormats={{
                TU_NGAY: 'YYYYMMDD',
                DEN_NGAY: 'YYYYMMDD',
              }}
            />
            <Button
              type="primary"
              onClick={handleSave}
              icon={<SaveOutlined />}
              disabled={!formChanged}
              loading={isAdding}>
              Lưu
            </Button>
          </Space>
        </Col>
      </Row>

      <Card title="Thông tin chi tiết" style={{ marginBottom: 16 }}>
        <Form
          form={form}
          layout="vertical"
          onValuesChange={() => setFormChanged(checkFormChanged())}>
          <Row gutter={[16, 0]}>{renderFields()}</Row>
        </Form>
      </Card>

      <Divider orientation="left">Lịch sử chi tiết</Divider>

      <Table
        dataSource={details}
        columns={detailColumns}
        rowKey={(record) => record[`${itemType}_detail_id`]}
        pagination={{ defaultPageSize: 10 }}
        scroll={{ x: 'max-content' }}
      />
    </div>
  )
}

SSItemDetail.propTypes = propTypes

export default SSItemDetail
