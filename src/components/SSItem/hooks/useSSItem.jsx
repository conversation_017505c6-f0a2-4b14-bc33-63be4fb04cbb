import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import {
  getMedicalSupplies,
  getMedicalSupplyById,
  getMedicalSupplyDetails,
  addMedicalSupply,
  addMedicalSupplyDetail,
  updateMedicalSupplyDetail,
  deleteMedicalSupplyDetail,
  deleteMedicalSupply,
  getMedicines,
  getMedicineById,
  getMedicineDetails,
  addMedicine,
  addMedicineDetail,
  updateMedicineDetail,
  deleteMedicineDetail,
  deleteMedicine,
  getTechnicalServices,
  getTechnicalServiceById,
  getTechnicalServiceDetails,
  addTechnicalService,
  addTechnicalServiceDetail,
  updateTechnicalServiceDetail,
  deleteTechnicalServiceDetail,
  deleteTechnicalService,
} from '../SSItemService'
import {
  exportMedicalSuppliesDetails,
  exportMedicineDetails,
  exportTechnicalServiceDetails,
  importMedicalSuppliesDetails,
  importMedicineDetails,
  importTechnicalServiceDetails,
} from '../SSItemExcelService'
import { handleError } from '../../../common/helpers'
import { useUI } from '../../../common/UIProvider'

// Query keys
export const SS_ITEM_QUERY_KEYS = {
  MEDICAL_SUPPLIES: 'medicalSupplies',
  MEDICAL_SUPPLY_DETAILS: 'medicalSupplyDetails',
  MEDICINES: 'medicines',
  MEDICINE_DETAILS: 'medicineDetails',
  TECHNICAL_SERVICES: 'technicalServices',
  TECHNICAL_SERVICE_DETAILS: 'technicalServiceDetails',
  EXCEL_EXPORT: 'excelExport',
  EXCEL_IMPORT: 'excelImport',
}

// Hook for Medical Supplies
export const useMedicalSupplies = (searchText = '') => {
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  const medicalSuppliesQuery = useQuery({
    queryKey: [
      SS_ITEM_QUERY_KEYS.MEDICAL_SUPPLIES,
      searchText,
      pagination.current,
      pagination.pageSize,
    ],
    queryFn: async () => {
      const skip = (pagination.current - 1) * pagination.pageSize
      const response = await getMedicalSupplies({
        searchText,
        skip,
        top: pagination.pageSize,
        count: true,
      })

      setPagination((prev) => ({
        ...prev,
        total: response['@odata.count'] || 0,
      }))

      return response.value
    },
  })

  return {
    medicalSupplies: medicalSuppliesQuery.data || [],
    isLoading: medicalSuppliesQuery.isLoading,
    isError: medicalSuppliesQuery.isError,
    pagination,
    setPagination,
    refetch: medicalSuppliesQuery.refetch,
  }
}

// Hook for Medical Supply Details
export const useMedicalSupplyDetails = (medicalSupplyId) => {
  const medicalSupplyDetailsQuery = useQuery({
    queryKey: [SS_ITEM_QUERY_KEYS.MEDICAL_SUPPLY_DETAILS, medicalSupplyId],
    queryFn: async () => {
      if (!medicalSupplyId) return []
      const response = await getMedicalSupplyDetails(medicalSupplyId)
      return response.value
    },
    enabled: !!medicalSupplyId,
  })

  const medicalSupplyQuery = useQuery({
    queryKey: [SS_ITEM_QUERY_KEYS.MEDICAL_SUPPLIES, medicalSupplyId],
    queryFn: async () => {
      if (!medicalSupplyId) return null
      return await getMedicalSupplyById(medicalSupplyId)
    },
    enabled: !!medicalSupplyId,
  })

  return {
    medical_supplies: medicalSupplyQuery.data,
    medical_suppliesDetails: medicalSupplyDetailsQuery.data || [],
    isLoading: medicalSupplyDetailsQuery.isLoading || medicalSupplyQuery.isLoading,
    isError: medicalSupplyDetailsQuery.isError || medicalSupplyQuery.isError,
    refetch: () => {
      medicalSupplyDetailsQuery.refetch()
      medicalSupplyQuery.refetch()
    },
  }
}

// Hook for adding Medical Supply Detail
export const useAddMedicalSupplyDetail = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const addMedicalSupplyDetailMutation = useMutation({
    mutationFn: addMedicalSupplyDetail,
    onSuccess: (_, variables) => {
      ui.notiSuccess('Thêm chi tiết VTYT thành công')
      queryClient.invalidateQueries([
        SS_ITEM_QUERY_KEYS.MEDICAL_SUPPLY_DETAILS,
        variables.medical_supplies_id,
      ])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Thêm chi tiết VTYT thất bại')
    },
  })

  return {
    addMedicalSupplyDetail: addMedicalSupplyDetailMutation.mutate,
    isLoading: addMedicalSupplyDetailMutation.isLoading,
  }
}

// Hook for updating Medical Supply Detail
export const useUpdateMedicalSupplyDetail = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const updateMedicalSupplyDetailMutation = useMutation({
    mutationFn: ({ detailId, detail }) => updateMedicalSupplyDetail(detailId, detail),
    onSuccess: (_, variables) => {
      ui.notiSuccess('Cập nhật chi tiết VTYT thành công')
      queryClient.invalidateQueries([
        SS_ITEM_QUERY_KEYS.MEDICAL_SUPPLY_DETAILS,
        variables.detail.medical_supplies_id,
      ])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Cập nhật chi tiết VTYT thất bại')
    },
  })

  return {
    updateMedicalSupplyDetail: updateMedicalSupplyDetailMutation.mutate,
    isLoading: updateMedicalSupplyDetailMutation.isLoading,
  }
}

// Hook for deleting Medical Supply Detail
export const useDeleteMedicalSupplyDetail = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const deleteMedicalSupplyDetailMutation = useMutation({
    mutationFn: deleteMedicalSupplyDetail,
    onSuccess: (_, detailId) => {
      ui.notiSuccess('Xóa chi tiết VTYT thành công')
      // We need to invalidate all medical supply details queries since we don't know the parent ID
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.MEDICAL_SUPPLY_DETAILS])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Xóa chi tiết VTYT thất bại')
    },
  })

  return {
    deleteMedicalSupplyDetail: deleteMedicalSupplyDetailMutation.mutate,
    isLoading: deleteMedicalSupplyDetailMutation.isLoading,
  }
}

// Hook for Medicines
export const useMedicines = (searchText = '') => {
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  const medicinesQuery = useQuery({
    queryKey: [SS_ITEM_QUERY_KEYS.MEDICINES, searchText, pagination.current, pagination.pageSize],
    queryFn: async () => {
      const skip = (pagination.current - 1) * pagination.pageSize
      const response = await getMedicines({
        searchText,
        skip,
        top: pagination.pageSize,
        count: true,
      })

      setPagination((prev) => ({
        ...prev,
        total: response['@odata.count'] || 0,
      }))

      return response.value
    },
  })

  return {
    medicines: medicinesQuery.data || [],
    isLoading: medicinesQuery.isLoading,
    isError: medicinesQuery.isError,
    pagination,
    setPagination,
    refetch: medicinesQuery.refetch,
  }
}

// Hook for Medicine Details
export const useMedicineDetails = (medicineId) => {
  const medicineDetailsQuery = useQuery({
    queryKey: [SS_ITEM_QUERY_KEYS.MEDICINE_DETAILS, medicineId],
    queryFn: async () => {
      if (!medicineId) return []
      const response = await getMedicineDetails(medicineId)
      return response.value
    },
    enabled: !!medicineId,
  })

  const medicineQuery = useQuery({
    queryKey: [SS_ITEM_QUERY_KEYS.MEDICINES, medicineId],
    queryFn: async () => {
      if (!medicineId) return null
      return await getMedicineById(medicineId)
    },
    enabled: !!medicineId,
  })

  return {
    medicine: medicineQuery.data,
    medicineDetails: medicineDetailsQuery.data || [],
    isLoading: medicineDetailsQuery.isLoading || medicineQuery.isLoading,
    isError: medicineDetailsQuery.isError || medicineQuery.isError,
    refetch: () => {
      medicineDetailsQuery.refetch()
      medicineQuery.refetch()
    },
  }
}

// Hook for adding Medicine Detail
export const useAddMedicineDetail = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const addMedicineDetailMutation = useMutation({
    mutationFn: addMedicineDetail,
    onSuccess: (_, variables) => {
      ui.notiSuccess('Thêm chi tiết thuốc thành công')
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.MEDICINE_DETAILS, variables.medicine_id])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Thêm chi tiết thuốc thất bại')
    },
  })

  return {
    addMedicineDetail: addMedicineDetailMutation.mutate,
    isLoading: addMedicineDetailMutation.isLoading,
  }
}

// Hook for updating Medicine Detail
export const useUpdateMedicineDetail = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const updateMedicineDetailMutation = useMutation({
    mutationFn: ({ detailId, detail }) => updateMedicineDetail(detailId, detail),
    onSuccess: (_, variables) => {
      ui.notiSuccess('Cập nhật chi tiết thuốc thành công')
      queryClient.invalidateQueries([
        SS_ITEM_QUERY_KEYS.MEDICINE_DETAILS,
        variables.detail.medicine_id,
      ])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Cập nhật chi tiết thuốc thất bại')
    },
  })

  return {
    updateMedicineDetail: updateMedicineDetailMutation.mutate,
    isLoading: updateMedicineDetailMutation.isLoading,
  }
}

// Hook for deleting Medicine Detail
export const useDeleteMedicineDetail = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const deleteMedicineDetailMutation = useMutation({
    mutationFn: deleteMedicineDetail,
    onSuccess: () => {
      ui.notiSuccess('Xóa chi tiết thuốc thành công')
      // We need to invalidate all medicine details queries since we don't know the parent ID
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.MEDICINE_DETAILS])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Xóa chi tiết thuốc thất bại')
    },
  })

  return {
    deleteMedicineDetail: deleteMedicineDetailMutation.mutate,
    isLoading: deleteMedicineDetailMutation.isLoading,
  }
}

// Hook for Technical Services
export const useTechnicalServices = (searchText = '') => {
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  const technicalServicesQuery = useQuery({
    queryKey: [
      SS_ITEM_QUERY_KEYS.TECHNICAL_SERVICES,
      searchText,
      pagination.current,
      pagination.pageSize,
    ],
    queryFn: async () => {
      const skip = (pagination.current - 1) * pagination.pageSize
      const response = await getTechnicalServices({
        searchText,
        skip,
        top: pagination.pageSize,
        count: true,
      })

      setPagination((prev) => ({
        ...prev,
        total: response['@odata.count'] || 0,
      }))

      return response.value
    },
  })

  return {
    technicalServices: technicalServicesQuery.data || [],
    isLoading: technicalServicesQuery.isLoading,
    isError: technicalServicesQuery.isError,
    pagination,
    setPagination,
    refetch: technicalServicesQuery.refetch,
  }
}

// Hook for Technical Service Details
export const useTechnicalServiceDetails = (technicalServiceId) => {
  const technicalServiceDetailsQuery = useQuery({
    queryKey: [SS_ITEM_QUERY_KEYS.TECHNICAL_SERVICE_DETAILS, technicalServiceId],
    queryFn: async () => {
      if (!technicalServiceId) return []
      const response = await getTechnicalServiceDetails(technicalServiceId)
      return response.value
    },
    enabled: !!technicalServiceId,
  })

  const technicalServiceQuery = useQuery({
    queryKey: [SS_ITEM_QUERY_KEYS.TECHNICAL_SERVICES, technicalServiceId],
    queryFn: async () => {
      if (!technicalServiceId) return null
      return await getTechnicalServiceById(technicalServiceId)
    },
    enabled: !!technicalServiceId,
  })

  return {
    technical_services: technicalServiceQuery.data,
    technical_servicesDetails: technicalServiceDetailsQuery.data || [],
    isLoading: technicalServiceDetailsQuery.isLoading || technicalServiceQuery.isLoading,
    isError: technicalServiceDetailsQuery.isError || technicalServiceQuery.isError,
    refetch: () => {
      technicalServiceDetailsQuery.refetch()
      technicalServiceQuery.refetch()
    },
  }
}

// Hook for adding Technical Service Detail
export const useAddTechnicalServiceDetail = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const addTechnicalServiceDetailMutation = useMutation({
    mutationFn: addTechnicalServiceDetail,
    onSuccess: (_, variables) => {
      ui.notiSuccess('Thêm chi tiết DVKT thành công')
      queryClient.invalidateQueries([
        SS_ITEM_QUERY_KEYS.TECHNICAL_SERVICE_DETAILS,
        variables.technical_services_id,
      ])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Thêm chi tiết DVKT thất bại')
    },
  })

  return {
    addTechnicalServiceDetail: addTechnicalServiceDetailMutation.mutate,
    isLoading: addTechnicalServiceDetailMutation.isLoading,
  }
}

// Hook for updating Technical Service Detail
export const useUpdateTechnicalServiceDetail = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const updateTechnicalServiceDetailMutation = useMutation({
    mutationFn: ({ detailId, detail }) => updateTechnicalServiceDetail(detailId, detail),
    onSuccess: (_, variables) => {
      ui.notiSuccess('Cập nhật chi tiết DVKT thành công')
      queryClient.invalidateQueries([
        SS_ITEM_QUERY_KEYS.TECHNICAL_SERVICE_DETAILS,
        variables.detail.technical_services_id,
      ])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Cập nhật chi tiết DVKT thất bại')
    },
  })

  return {
    updateTechnicalServiceDetail: updateTechnicalServiceDetailMutation.mutate,
    isLoading: updateTechnicalServiceDetailMutation.isLoading,
  }
}

// Hook for deleting Technical Service Detail
export const useDeleteTechnicalServiceDetail = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const deleteTechnicalServiceDetailMutation = useMutation({
    mutationFn: deleteTechnicalServiceDetail,
    onSuccess: () => {
      ui.notiSuccess('Xóa chi tiết DVKT thành công')
      // We need to invalidate all technical service details queries since we don't know the parent ID
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.TECHNICAL_SERVICE_DETAILS])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Xóa chi tiết DVKT thất bại')
    },
  })

  return {
    deleteTechnicalServiceDetail: deleteTechnicalServiceDetailMutation.mutate,
    isLoading: deleteTechnicalServiceDetailMutation.isLoading,
  }
}

// Hook for adding new Medical Supply
export const useAddMedicalSupplyItem = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const addMedicalSupplyMutation = useMutation({
    mutationFn: addMedicalSupply,
    onSuccess: () => {
      ui.notiSuccess('Thêm vật tư y tế thành công')
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.MEDICAL_SUPPLIES])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Thêm vật tư y tế thất bại')
    },
  })

  return {
    addMedicalSupplyItem: addMedicalSupplyMutation.mutate,
    isLoading: addMedicalSupplyMutation.isLoading,
  }
}

// Hook for adding new Medicine
export const useAddMedicineItem = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const addMedicineMutation = useMutation({
    mutationFn: addMedicine,
    onSuccess: () => {
      ui.notiSuccess('Thêm thuốc thành công')
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.MEDICINES])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Thêm thuốc thất bại')
    },
  })

  return {
    addMedicineItem: addMedicineMutation.mutate,
    isLoading: addMedicineMutation.isLoading,
  }
}

// Hook for adding new Technical Service
export const useAddTechnicalServiceItem = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const addTechnicalServiceMutation = useMutation({
    mutationFn: addTechnicalService,
    onSuccess: () => {
      ui.notiSuccess('Thêm dịch vụ kỹ thuật thành công')
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.TECHNICAL_SERVICES])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Thêm dịch vụ kỹ thuật thất bại')
    },
  })

  return {
    addTechnicalServiceItem: addTechnicalServiceMutation.mutate,
    isLoading: addTechnicalServiceMutation.isLoading,
  }
}

// Hook for deleting Medical Supply with all its details
export const useDeleteMedicalSupplyItem = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const deleteMedicalSupplyMutation = useMutation({
    mutationFn: deleteMedicalSupply,
    onSuccess: () => {
      ui.notiSuccess('Xóa vật tư y tế thành công')
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.MEDICAL_SUPPLIES])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Xóa vật tư y tế thất bại')
    },
  })

  return {
    deleteMedicalSupplyItem: deleteMedicalSupplyMutation.mutate,
    isLoading: deleteMedicalSupplyMutation.isLoading,
  }
}

// Hook for deleting Medicine with all its details
export const useDeleteMedicineItem = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const deleteMedicineMutation = useMutation({
    mutationFn: deleteMedicine,
    onSuccess: () => {
      ui.notiSuccess('Xóa thuốc thành công')
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.MEDICINES])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Xóa thuốc thất bại')
    },
  })

  return {
    deleteMedicineItem: deleteMedicineMutation.mutate,
    isLoading: deleteMedicineMutation.isLoading,
  }
}

// Hook for deleting Technical Service with all its details
export const useDeleteTechnicalServiceItem = () => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const deleteTechnicalServiceMutation = useMutation({
    mutationFn: deleteTechnicalService,
    onSuccess: () => {
      ui.notiSuccess('Xóa dịch vụ kỹ thuật thành công')
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.TECHNICAL_SERVICES])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Xóa dịch vụ kỹ thuật thất bại')
    },
  })

  return {
    deleteTechnicalServiceItem: deleteTechnicalServiceMutation.mutate,
    isLoading: deleteTechnicalServiceMutation.isLoading,
  }
}

// Excel Export/Import Hooks

// Hook for Medical Supplies Excel Export/Import
export const useMedicalSuppliesExcel = () => {
  const queryClient = useQueryClient()
  const ui = useUI()

  // Export Medical Supplies Details
  const exportMedicalSuppliesDetailsMutation = useMutation({
    mutationKey: [SS_ITEM_QUERY_KEYS.EXCEL_EXPORT, 'medical_supplies_detail'],
    mutationFn: exportMedicalSuppliesDetails,
    onError: (error) => {
      handleError(error)
      ui.notiError('Xuất dữ liệu chi tiết vật tư y tế thất bại')
    },
  })

  // Import Medical Supplies Details
  const importMedicalSuppliesDetailsMutation = useMutation({
    mutationKey: [SS_ITEM_QUERY_KEYS.EXCEL_IMPORT, 'medical_supplies_detail'],
    mutationFn: importMedicalSuppliesDetails,
    onSuccess: () => {
      // Invalidate both parent and detail queries since the import function updates both
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.MEDICAL_SUPPLIES])
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.MEDICAL_SUPPLY_DETAILS])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Nhập dữ liệu chi tiết vật tư y tế thất bại')
    },
  })

  return {
    exportMedicalSuppliesDetails: exportMedicalSuppliesDetailsMutation.mutateAsync,
    importMedicalSuppliesDetails: importMedicalSuppliesDetailsMutation.mutateAsync,
    isExporting: exportMedicalSuppliesDetailsMutation.isLoading,
    isImporting: importMedicalSuppliesDetailsMutation.isLoading,
  }
}

// Hook for Medicines Excel Export/Import
export const useMedicinesExcel = () => {
  const queryClient = useQueryClient()
  const ui = useUI()

  // Export Medicine Details
  const exportMedicineDetailsMutation = useMutation({
    mutationKey: [SS_ITEM_QUERY_KEYS.EXCEL_EXPORT, 'medicine_detail'],
    mutationFn: exportMedicineDetails,
    onError: (error) => {
      handleError(error)
      ui.notiError('Xuất dữ liệu chi tiết thuốc thất bại')
    },
  })

  // Import Medicine Details
  const importMedicineDetailsMutation = useMutation({
    mutationKey: [SS_ITEM_QUERY_KEYS.EXCEL_IMPORT, 'medicine_detail'],
    mutationFn: importMedicineDetails,
    onSuccess: () => {
      // Invalidate both parent and detail queries since the import function updates both
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.MEDICINES])
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.MEDICINE_DETAILS])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Nhập dữ liệu chi tiết thuốc thất bại')
    },
  })

  return {
    exportMedicineDetails: exportMedicineDetailsMutation.mutateAsync,
    importMedicineDetails: importMedicineDetailsMutation.mutateAsync,
    isExporting: exportMedicineDetailsMutation.isLoading,
    isImporting: importMedicineDetailsMutation.isLoading,
  }
}

// Hook for Technical Services Excel Export/Import
export const useTechnicalServicesExcel = () => {
  const queryClient = useQueryClient()
  const ui = useUI()

  // Export Technical Service Details
  const exportTechnicalServiceDetailsMutation = useMutation({
    mutationKey: [SS_ITEM_QUERY_KEYS.EXCEL_EXPORT, 'technical_services_detail'],
    mutationFn: exportTechnicalServiceDetails,
    onError: (error) => {
      handleError(error)
      ui.notiError('Xuất dữ liệu chi tiết dịch vụ kỹ thuật thất bại')
    },
  })

  // Import Technical Service Details
  const importTechnicalServiceDetailsMutation = useMutation({
    mutationKey: [SS_ITEM_QUERY_KEYS.EXCEL_IMPORT, 'technical_services_detail'],
    mutationFn: importTechnicalServiceDetails,
    onSuccess: () => {
      // Invalidate both parent and detail queries since the import function updates both
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.TECHNICAL_SERVICES])
      queryClient.invalidateQueries([SS_ITEM_QUERY_KEYS.TECHNICAL_SERVICE_DETAILS])
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Nhập dữ liệu chi tiết dịch vụ kỹ thuật thất bại')
    },
  })

  return {
    exportTechnicalServiceDetails: exportTechnicalServiceDetailsMutation.mutateAsync,
    importTechnicalServiceDetails: importTechnicalServiceDetailsMutation.mutateAsync,
    isExporting: exportTechnicalServiceDetailsMutation.isLoading,
    isImporting: importTechnicalServiceDetailsMutation.isLoading,
  }
}
