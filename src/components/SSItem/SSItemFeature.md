Chức năng quản lý danh mục VTYT, Thuốc, DVKT (medical_supplies, medicine, technical_services)
- Đặt trong module Folder SSItem
- Tham khảo cấu trúc database trong file SSItemDatabase
- 3 danh mục này có cấu trúc tương tự nhau nên cần tận dụng tối ưu component
- Tạo một page chứa 3 tab ứng với 3 danh mục
- Mỗi tab gồm một table danh sách
- Khi bấm vào xem chi tiết thì mở page chi tiết tương ứng
- Trong trang chi tiết, sẽ hiện ra record tại table _detail tương ứng (medical_supplies_detail, medicine_detail, technical_services_detail)
- <PERSON><PERSON> bấm lưu lại, nếu có thay đổi thì lưu thêm 1 record vào table _detail (sort theo lu_updated mới nhất)

Chức năng Import/Export Excel
- Viết một hàm import Excel cho table gốc và table detail (medical_supplies và medical_supplies_detail, medicine và medicine_detail, technical_services và technical_services_detail)
- Table cha, kiểm tra tồn tại trên database, nếu tất cả các cột đều trùng (trừ lu_updated và lu_user_id) thì không lưu vào table, ngược lại nếu khác thì update record đó
- Table detail, kiểm tra tồn tại trên database, nếu tất cả các cột đều trùng (trừ lu_updated và lu_user_id) thì không lưu vào table, ngược lại nếu khác tạo thêm record mới
- Nếu record có id thì dùng id để kiểm tra tồn tại và update lại theo id (không tạo mới record)
- Hiển thị nút import/export chỉ trên danh sách table cha
- Phần nội dung Excel chỉ cần danh sách của những record detail, không cần record cha, khi import/export sẽ tự động lấy thông tin record cha từ record detail
- Không cần import/export parent, chỉ sử dụng chung 1 template import/export cho detail - tự động trích xuất parent từ detail vì các cột của parent giống tương tự như detail