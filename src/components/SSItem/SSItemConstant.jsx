import React from 'react'
import { Tag } from 'antd'
import PromiseContent from '../../common/components/PromiseContent'
import { getItemService } from '../../common/services'
import lists from '../../common/lists'
import dayjs from '../../common/dayjs'
import { FORMAT_DATE } from '../../common/constant'

// Tab keys
export const TAB_KEYS = {
  MEDICAL_SUPPLIES: '1',
  MEDICINE: '2',
  TECHNICAL_SERVICES: '3',
}

// Item types
export const ITEM_TYPES = {
  MEDICAL_SUPPLIES: 'medical_supplies',
  MEDICINE: 'medicine',
  TECHNICAL_SERVICES: 'technical_services',
}

// Table columns for Medical Supplies
export const MEDICAL_SUPPLIES_COLUMNS = [
  {
    title: 'Mã VTYT',
    dataIndex: 'MA_VAT_TU',
    key: 'MA_VAT_TU',
    width: 150,
  },
  {
    title: 'Tên VTYT',
    dataIndex: 'TEN_VAT_TU',
    key: 'TEN_VAT_TU',
    width: 300,
  },
  {
    title: 'Nhóm VTYT',
    dataIndex: 'NHOM_VAT_TU',
    key: 'NHOM_VAT_TU',
    width: 200,
  },
  {
    title: 'Mã nhóm VTYT',
    dataIndex: 'MA_NHOM_VAT_TU',
    key: 'MA_NHOM_VAT_TU',
    width: 150,
  },
  {
    title: 'Từ ngày',
    dataIndex: 'TU_NGAY',
    key: 'TU_NGAY',
    width: 120,
    render: (date) => (date ? dayjs(date).format(FORMAT_DATE) : 'N/A'),
  },
  {
    title: 'Đến ngày',
    dataIndex: 'DEN_NGAY',
    key: 'DEN_NGAY',
    width: 120,
    render: (date) => (date ? dayjs(date).format(FORMAT_DATE) : 'N/A'),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'active_flag',
    key: 'active_flag',
    width: 100,
    render: (active) => (
      <Tag color={active ? 'green' : 'red'}>{active ? 'Active' : 'Inactive'}</Tag>
    ),
  },
]

// Table columns for Medicine
export const MEDICINE_COLUMNS = [
  {
    title: 'Mã thuốc',
    dataIndex: 'MA_THUOC',
    key: 'MA_THUOC',
    width: 150,
  },
  {
    title: 'Tên thuốc',
    dataIndex: 'TEN_THUOC',
    key: 'TEN_THUOC',
    width: 300,
  },
  {
    title: 'Tên hoạt chất',
    dataIndex: 'TEN_HOAT_CHAT',
    key: 'TEN_HOAT_CHAT',
    width: 200,
  },
  {
    title: 'Hàm lượng',
    dataIndex: 'HAM_LUONG',
    key: 'HAM_LUONG',
    width: 150,
  },
  {
    title: 'Đơn vị tính',
    dataIndex: 'DON_VI_TINH',
    key: 'DON_VI_TINH',
    width: 100,
  },
  {
    title: 'Từ ngày',
    dataIndex: 'TU_NGAY',
    key: 'TU_NGAY',
    width: 120,
    render: (date) => (date ? dayjs(date).format(FORMAT_DATE) : 'N/A'),
  },
  {
    title: 'Đến ngày',
    dataIndex: 'DEN_NGAY',
    key: 'DEN_NGAY',
    width: 120,
    render: (date) => (date ? dayjs(date).format(FORMAT_DATE) : 'N/A'),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'active_flag',
    key: 'active_flag',
    width: 100,
    render: (active) => (
      <Tag color={active ? 'green' : 'red'}>{active ? 'Active' : 'Inactive'}</Tag>
    ),
  },
]

// Table columns for Technical Services
export const TECHNICAL_SERVICES_COLUMNS = [
  {
    title: 'Mã DVKT',
    dataIndex: 'MA_TUONG_DUONG',
    key: 'MA_TUONG_DUONG',
    width: 150,
  },
  {
    title: 'Tên DVKT phê duyệt',
    dataIndex: 'TEN_DVKT_PHEDUYET',
    key: 'TEN_DVKT_PHEDUYET',
    width: 300,
  },
  {
    title: 'Tên DVKT giá',
    dataIndex: 'TEN_DVKT_GIA',
    key: 'TEN_DVKT_GIA',
    width: 300,
  },
  {
    title: 'Từ ngày',
    dataIndex: 'TU_NGAY',
    key: 'TU_NGAY',
    width: 120,
    render: (date) => (date ? dayjs(date).format(FORMAT_DATE) : 'N/A'),
  },
  {
    title: 'Đến ngày',
    dataIndex: 'DEN_NGAY',
    key: 'DEN_NGAY',
    width: 120,
    render: (date) => (date ? dayjs(date).format(FORMAT_DATE) : 'N/A'),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'active_flag',
    key: 'active_flag',
    width: 100,
    render: (active) => (
      <Tag color={active ? 'green' : 'red'}>{active ? 'Active' : 'Inactive'}</Tag>
    ),
  },
]

// Render functions for detail items
export const renderMedicalSupplyDetail = (id) => {
  if (!id) return <span style={{ color: 'gray' }}>N/A</span>

  return (
    <PromiseContent
      promise={() => getItemService(lists.medical_supplies_detail, id)}
      render={(data) => {
        const detail = data
        return (
          <div>
            <div>
              <strong>{detail?.MA_VAT_TU || 'N/A'}</strong>
            </div>
            <div style={{ color: 'gray' }}>{detail?.TEN_VAT_TU}</div>
            <div style={{ fontSize: '11px', color: '#999' }}>
              {dayjs(detail?.lu_updated).format('DD/MM/YYYY HH:mm:ss')}
            </div>
          </div>
        )
      }}
    />
  )
}

export const renderMedicineDetail = (id) => {
  if (!id) return <span style={{ color: 'gray' }}>N/A</span>

  return (
    <PromiseContent
      promise={() => getItemService(lists.medicine_detail, id)}
      render={(data) => {
        const detail = data
        return (
          <div>
            <div>
              <strong>{detail?.MA_THUOC || 'N/A'}</strong>
            </div>
            <div style={{ color: 'gray' }}>{detail?.TEN_THUOC}</div>
            <div style={{ fontSize: '11px', color: '#999' }}>
              {dayjs(detail?.lu_updated).format('DD/MM/YYYY HH:mm:ss')}
            </div>
          </div>
        )
      }}
    />
  )
}

export const renderTechnicalServiceDetail = (id) => {
  if (!id) return <span style={{ color: 'gray' }}>N/A</span>

  return (
    <PromiseContent
      promise={() => getItemService(lists.technical_services_detail, id)}
      render={(data) => {
        const detail = data
        return (
          <div>
            <div>
              <strong>{detail?.MA_TUONG_DUONG || 'N/A'}</strong>
            </div>
            <div style={{ color: 'gray' }}>{detail?.TEN_DVKT_PHEDUYET}</div>
            <div style={{ fontSize: '11px', color: '#999' }}>
              {dayjs(detail?.lu_updated).format('DD/MM/YYYY HH:mm:ss')}
            </div>
          </div>
        )
      }}
    />
  )
}
