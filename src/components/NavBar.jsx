import { Breadcrumb } from 'antd'
import { Link, useLocation } from 'react-router-dom'
import PropTypes from '../common/PropTypes'
const propTypes = {
  items: PropTypes.array,
  appRcd: PropTypes.string,
  recordId: PropTypes.string,
}
const NavBar = ({ appRcd, recordId, items }) => {
  const location = useLocation()
  return <Breadcrumb items={items}></Breadcrumb>
}
NavBar.propTypes = propTypes
export default NavBar
