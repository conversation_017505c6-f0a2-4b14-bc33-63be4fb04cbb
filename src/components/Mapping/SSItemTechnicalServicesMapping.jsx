import React, { useState, useMemo } from 'react'
import { Form, Select, Spin, Button, Table, Modal, Popconfirm, Input, Space } from 'antd'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import debounce from 'lodash/debounce'
import { v4 as uuidv4 } from 'uuid'
import {
  getSSItemTechnicalServicesMappings,
  deleteSSItemTechnicalServicesMapping,
  getMedicalSupplies,
  getTechnicalServices,
  getMedicines,
  createSSItemTechnicalServicesMapping,
  updateSSItemTechnicalServicesMapping,
  checkDuplicateSSItemTechnicalServicesMapping,
} from './mappingService'
import { handleError } from '../../common/helpers'
import { useUI } from '../../common/UIProvider'
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import {
  renderMedicalSupply,
  renderMedicine,
  renderTechnicalService,
  renderDate,
} from './MappingConstant'

const { Option } = Select

const SSItemTechnicalServicesMapping = () => {
  const ui = useUI()
  const queryClient = useQueryClient()
  const [searchFilter, setSearchFilter] = useState(null)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [currentMapping, setCurrentMapping] = useState(null)
  const [modalMode, setModalMode] = useState('create')
  const [form] = Form.useForm()
  const [filterType, setFilterType] = useState('none')

  // State for dropdown search
  const [medicalSuppliesSearch, setMedicalSuppliesSearch] = useState('')
  const [technicalServicesSearch, setTechnicalServicesSearch] = useState('')
  const [medicinesSearch, setMedicinesSearch] = useState('')

  // Debounced search functions for dropdowns
  const debouncedMedicalSuppliesSearch = useMemo(
    () => debounce((value) => setMedicalSuppliesSearch(value), 500),
    [],
  )

  const debouncedTechnicalServicesSearch = useMemo(
    () => debounce((value) => setTechnicalServicesSearch(value), 500),
    [],
  )

  const debouncedMedicinesSearch = useMemo(
    () => debounce((value) => setMedicinesSearch(value), 500),
    [],
  )

  // Debounced search function for mapping ID
  const debouncedMappingIdSearch = useMemo(
    () =>
      debounce((value) => {
        if (value.trim()) {
          setSearchFilter({ mapping_id: value.trim() })
        } else {
          setSearchFilter(null)
        }
      }, 500),
    [],
  )

  // Query to fetch mappings with search
  const { data: mappingsData, isLoading } = useQuery({
    queryKey: ['ssItemTechnicalServicesMappings', searchFilter],
    queryFn: () => getSSItemTechnicalServicesMappings({ searchText: searchFilter }),
  })

  // Query to fetch medical supplies for dropdown with search
  const { data: medicalSuppliesData, isLoading: isLoadingMedicalSupplies } = useQuery({
    queryKey: ['medicalSupplies', medicalSuppliesSearch],
    queryFn: () => getMedicalSupplies({ searchText: medicalSuppliesSearch }),
  })

  // Query to fetch technical services for dropdown with search
  const { data: technicalServicesData, isLoading: isLoadingTechnicalServices } = useQuery({
    queryKey: ['technicalServices', technicalServicesSearch],
    queryFn: () => getTechnicalServices({ searchText: technicalServicesSearch }),
  })

  // Query to fetch medicines for dropdown with search
  const { data: medicinesData, isLoading: isLoadingMedicines } = useQuery({
    queryKey: ['medicines', medicinesSearch],
    queryFn: () => getMedicines({ searchText: medicinesSearch }),
  })

  // Mutation for deleting a mapping
  const deleteMutation = useMutation({
    mutationFn: (mappingId) => deleteSSItemTechnicalServicesMapping(mappingId),
    onSuccess: () => {
      ui.notiSuccess('Mapping deleted successfully')
      queryClient.invalidateQueries({ queryKey: ['ssItemTechnicalServicesMappings'] })
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to delete mapping')
    },
  })

  // Mutation for creating a mapping
  const createMutation = useMutation({
    mutationFn: (mapping) => createSSItemTechnicalServicesMapping(mapping),
    onSuccess: () => {
      ui.notiSuccess('Mapping created successfully')
      queryClient.invalidateQueries({ queryKey: ['ssItemTechnicalServicesMappings'] })
      setIsModalVisible(false)
      form.resetFields()
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to create mapping')
    },
  })

  // Mutation for updating a mapping
  const updateMutation = useMutation({
    mutationFn: ({ mappingId, mapping }) =>
      updateSSItemTechnicalServicesMapping(mappingId, mapping),
    onSuccess: () => {
      ui.notiSuccess('Mapping updated successfully')
      queryClient.invalidateQueries({ queryKey: ['ssItemTechnicalServicesMappings'] })
      setIsModalVisible(false)
      form.resetFields()
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to update mapping')
    },
  })

  // Use the data directly from the API response
  const mappings = mappingsData?.value || []

  // Handle adding or editing a mapping
  const handleAddEdit = (record) => {
    setModalMode(record ? 'edit' : 'create')
    setCurrentMapping(record)
    form.resetFields()

    if (record) {
      form.setFieldsValue({
        medical_supplies_id: record.medical_supplies_id,
        medicine_id: record.medicine_id,
        technical_services_id: record.technical_services_id,
      })
    }

    setIsModalVisible(true)
  }

  // Handle deleting a mapping
  const handleDelete = (mappingId) => {
    deleteMutation.mutate(mappingId)
  }

  // Check if a mapping with the same combination of IDs already exists using API
  const checkDuplicateMapping = async (values) => {
    // At least one of medical_supplies_id or medicine_id must be selected
    if (!values.medical_supplies_id && !values.medicine_id) {
      ui.notiWarning('At least one of Medical Supplies or Medicine must be selected')
      return true
    }

    try {
      // Get the current mapping ID if in edit mode
      const currentMappingId =
        modalMode === 'edit' ? currentMapping.ss_item_technical_services_mapping_id : null

      // Check for duplicates using the API
      const result = await checkDuplicateSSItemTechnicalServicesMapping(values, currentMappingId)

      if (result.isDuplicate && result.duplicateMapping) {
        ui.notiError('A mapping with this combination already exists')
        return true
      }

      return false
    } catch (error) {
      handleError(error)
      ui.notiError('Error checking for duplicate mappings')
      return true // Prevent form submission on error
    }
  }

  // Handle form submission with async duplicate check
  const handleSubmit = () => {
    form
      .validateFields()
      .then(async (values) => {
        // Check for duplicate mappings using API
        const isDuplicate = await checkDuplicateMapping(values)
        if (isDuplicate) {
          return
        }

        if (modalMode === 'create') {
          createMutation.mutate({
            ss_item_technical_services_mapping_id: uuidv4(),
            ...values,
            lu_updated: new Date().toISOString(),
          })
        } else {
          updateMutation.mutate({
            mappingId: currentMapping.ss_item_technical_services_mapping_id,
            mapping: {
              ...values,
              lu_updated: new Date().toISOString(),
            },
          })
        }
      })
      .catch((error) => {
        // Form validation error, already handled by Ant Design Form
        handleError(error, 'Form validation')
      })
  }

  // Table columns
  const columns = [
    {
      title: 'Medical Supplies',
      dataIndex: 'medical_supplies_id',
      key: 'medical_supplies_id',
      render: renderMedicalSupply,
    },
    {
      title: 'Medicine',
      dataIndex: 'medicine_id',
      key: 'medicine_id',
      render: renderMedicine,
    },
    {
      title: 'Technical Service',
      dataIndex: 'technical_services_id',
      key: 'technical_services_id',
      render: renderTechnicalService,
    },
    {
      title: 'Last Updated',
      dataIndex: 'lu_updated',
      key: 'lu_updated',
      render: renderDate,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button icon={<EditOutlined />} onClick={() => handleAddEdit(record)} title="Edit" />
          <Popconfirm
            title="Are you sure you want to delete this mapping?"
            onConfirm={() => handleDelete(record.ss_item_technical_services_mapping_id)}
            okText="Yes"
            cancelText="No">
            <Button danger icon={<DeleteOutlined />} title="Delete" />
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex', gap: '8px', width: '70%' }}>
          <Select
            placeholder="Select Medical Supply"
            optionLabelProp="label"
            style={{ width: 250 }}
            showSearch
            allowClear
            filterOption={false}
            onSearch={(value) => debouncedMedicalSuppliesSearch(value)}
            value={searchFilter?.medical_supplies_id}
            onChange={(value) => {
              if (value) {
                setSearchFilter({ medical_supplies_id: value, id: value })
              } else {
                setSearchFilter(null)
              }
            }}>
            {medicalSuppliesData?.value?.map((supply) => (
              <Option
                key={supply.medical_supplies_id}
                value={supply.medical_supplies_id}
                label={`${supply.MA_VAT_TU} - ${supply.TEN_VAT_TU}`}>
                {supply.MA_VAT_TU} - {supply.TEN_VAT_TU}
              </Option>
            ))}
          </Select>

          <Select
            placeholder="Select Medicine"
            style={{ width: 250 }}
            optionLabelProp="label"
            showSearch
            allowClear
            value={searchFilter?.medicine_id}
            filterOption={false}
            onSearch={(value) => debouncedMedicinesSearch(value)}
            onChange={(value) => {
              if (value) {
                setSearchFilter({ medicine_id: value, id: value })
              } else {
                setSearchFilter(null)
              }
            }}>
            {medicinesData?.value?.map((medicine) => (
              <Option
                key={medicine.medicine_id}
                value={medicine.medicine_id}
                label={`${medicine.MA_THUOC} - ${medicine.TEN_THUOC}`}>
                {medicine.MA_THUOC} - {medicine.TEN_THUOC}
              </Option>
            ))}
          </Select>

          <Select
            placeholder="Select Technical Service"
            style={{ width: 250 }}
            optionLabelProp="label"
            showSearch
            allowClear
            value={searchFilter?.technical_services_id}
            filterOption={false}
            onSearch={(value) => debouncedTechnicalServicesSearch(value)}
            onChange={(value) => {
              if (value) {
                setSearchFilter({ technical_services_id: value, id: value })
              } else {
                setSearchFilter(null)
              }
            }}>
            {technicalServicesData?.value?.map((service) => (
              <Option
                key={service.technical_services_id}
                value={service.technical_services_id}
                label={`${service.MA_TUONG_DUONG} - ${service.TEN_DVKT_PHEDUYET}`}>
                {service.MA_TUONG_DUONG} - {service.TEN_DVKT_PHEDUYET}
              </Option>
            ))}
          </Select>

          <Button onClick={() => setSearchFilter(null)} icon={<SearchOutlined />}>
            Show All
          </Button>
        </div>

        <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddEdit(null)}>
          Add Mapping
        </Button>
      </div>

      <Table
        size="small"
        columns={columns}
        dataSource={mappings}
        rowKey="ss_item_technical_services_mapping_id"
        loading={isLoading}
        pagination={{ defaultPageSize: 10 }}
      />

      {/* Modal for creating/editing mappings */}
      <Modal
        title={`${modalMode === 'create' ? 'Create' : 'Edit'} VTYT và DVKT Mapping`}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={handleSubmit}
        confirmLoading={createMutation.isPending || updateMutation.isPending}
        width={600}>
        <Form
          form={form}
          layout="vertical"
          validateMessages={{
            required: '${label} is required',
          }}>
          <Form.Item
            name="medical_supplies_id"
            label="Medical Supplies"
            dependencies={['medicine_id']}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value && !getFieldValue('medicine_id')) {
                    return Promise.reject(
                      new Error('At least one of Medical Supplies or Medicine must be selected'),
                    )
                  }
                  return Promise.resolve()
                },
              }),
            ]}>
            <Select
              placeholder="Select a medical supply"
              loading={isLoadingMedicalSupplies}
              showSearch
              allowClear
              filterOption={false}
              optionLabelProp="label"
              onSearch={(value) => debouncedMedicalSuppliesSearch(value)}
              notFoundContent={isLoadingMedicalSupplies ? <Spin size="small" /> : 'Type to search'}>
              {medicalSuppliesData?.value?.map((supply) => (
                <Option
                  key={supply.medical_supplies_id}
                  value={supply.medical_supplies_id}
                  label={`${supply.MA_VAT_TU} - ${supply.TEN_VAT_TU}`}>
                  {supply.MA_VAT_TU} - {supply.TEN_VAT_TU}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="medicine_id"
            label="Medicine"
            dependencies={['medical_supplies_id']}
            rules={[
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value && !getFieldValue('medical_supplies_id')) {
                    return Promise.reject(
                      new Error('At least one of Medical Supplies or Medicine must be selected'),
                    )
                  }
                  return Promise.resolve()
                },
              }),
            ]}>
            <Select
              placeholder="Select a medicine"
              loading={isLoadingMedicines}
              showSearch
              optionLabelProp="label"
              allowClear
              filterOption={false}
              onSearch={(value) => debouncedMedicinesSearch(value)}
              notFoundContent={isLoadingMedicines ? <Spin size="small" /> : 'Type to search'}>
              {medicinesData?.value?.map((medicine) => (
                <Option
                  key={medicine.medicine_id}
                  value={medicine.medicine_id}
                  label={`${medicine.MA_THUOC} - ${medicine.TEN_THUOC}`}>
                  {medicine.MA_THUOC} - {medicine.TEN_THUOC}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="technical_services_id"
            label="Technical Service"
            rules={[{ required: true, message: 'Please select a technical service' }]}>
            <Select
              placeholder="Select a technical service"
              loading={isLoadingTechnicalServices}
              showSearch
              filterOption={false}
              optionLabelProp="label"
              onSearch={(value) => debouncedTechnicalServicesSearch(value)}
              notFoundContent={
                isLoadingTechnicalServices ? <Spin size="small" /> : 'Type to search'
              }>
              {technicalServicesData?.value?.map((service) => (
                <Option
                  key={service.technical_services_id}
                  value={service.technical_services_id}
                  label={`${service.MA_TUONG_DUONG} - ${service.TEN_DVKT_PHEDUYET}`}>
                  {service.MA_TUONG_DUONG} - {service.TEN_DVKT_PHEDUYET}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <div style={{ color: 'red', marginBottom: '10px' }}>
            Note: Each combination of Medical Supplies, Medicine, and Technical Service must be
            unique.
          </div>
        </Form>
      </Modal>
    </div>
  )
}

export default SSItemTechnicalServicesMapping
