import React from 'react'
import { Select } from 'antd'
import { useMappingState } from './useMappingState'
import BaseMappingComponent from './BaseMappingComponent'
import { renderMedicine, renderItem, renderStatus, renderDate } from './MappingConstant'
import {
  getMedicineMappings,
  deleteMedicineMapping,
  getMedicines,
  createMedicineMapping,
  updateMedicineMapping,
  searchApiItems,
  checkDuplicateMedicineMapping,
} from './mappingService'

const { Option } = Select

const MedicineMapping = () => {
  // Use the shared mapping state hook
  const mappingState = useMappingState({
    entityType: 'medicine',
    getItems: getMedicines,
    getMappings: getMedicineMappings,
    createMapping: createMedicineMapping,
    updateMapping: updateMedicineMapping,
    deleteMapping: deleteMedicineMapping,
    checkDuplicateMapping: checkDuplicateMedicineMapping,
    idField: 'medicine_his_mapping_id',
    defaultFormValues: {
      active_flag: true,
    },
  })

  // Define table columns
  const columns = [
    {
      title: 'Medicine ID',
      dataIndex: 'medicine_id',
      key: 'medicine_id',
      render: renderMedicine,
    },
    {
      title: 'Item ID',
      dataIndex: 'item_id',
      key: 'item_id',
      render: renderItem,
    },

    {
      title: 'Status',
      dataIndex: 'active_flag',
      key: 'active_flag',
      render: renderStatus,
    },
    {
      title: 'Last Updated',
      dataIndex: 'lu_updated',
      key: 'lu_updated',
      render: renderDate,
    },
  ]

  // Define additional form fields
  const additionalFormFields = null

  // Render entity option for select
  const renderMedicineOption = (medicine) => (
    <Option
      key={medicine.medicine_id}
      value={medicine.medicine_id}
      label={`${medicine.MA_THUOC} - ${medicine.TEN_THUOC}`}>
      <div>
        <div>
          <b>{medicine.MA_THUOC}</b> - {medicine.TEN_THUOC}
        </div>
        <div>{medicine.TEN_HOAT_CHAT}</div>
        <div>{medicine.HAM_LUONG}</div>
      </div>
    </Option>
  )

  // Render item option for select
  const renderItemOption = (item) => (
    <Option key={item.item_id} value={item.item_id} label={`${item.item_code} - ${item.name_e}`}>
      {item.item_code} - {item.name_e}
    </Option>
  )

  return (
    <BaseMappingComponent
      title="Medicine Mapping"
      entityType="medicine"
      entityLabel="Medicine"
      entityField="medicine_id"
      idField="medicine_his_mapping_id"
      mappingState={mappingState}
      columns={columns}
      renderEntityOption={renderMedicineOption}
      renderItemOption={renderItemOption}
      entitySearchHandler={getMedicines}
      itemSearchHandler={searchApiItems}
      additionalFormFields={additionalFormFields}
    />
  )
}

export default MedicineMapping
