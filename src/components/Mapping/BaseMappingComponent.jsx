import React from 'react'
import PropTypes from 'prop-types'
import { Button, Table, Modal, Popconfirm, Space, Form } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import MappingFilter from './MappingFilter'
import MappingForm from './MappingForm'
import AsyncButton from '../../common/components/AsyncButton'
import { useQueryClient } from '@tanstack/react-query'

/**
 * Base component for all mapping screens
 */
const BaseMappingComponent = ({
  title,
  entityType,
  entityLabel,
  entityField,
  idField,
  mappingState,
  columns,
  renderEntityOption,
  renderItemOption,
  entitySearchHandler,
  itemSearchHandler,
  additionalFormFields,
  showRefreshButton = true,
}) => {
  const {
    searchFilter,
    setSearchFilter,
    isModalVisible,
    setIsModalVisible,
    currentMapping,
    setCurrentMapping,
    modalMode,
    setModalMode,
    filterType,
    setFilterType,
    selectOptions,
    setSelectOptions,
    isSearching,
    setIsSearching,
    mappings,
    itemsData,
    isLoading,
    isLoadingItems,
    debouncedSearch,
    debouncedMappingIdSearch,
    handleDelete,
    handleSubmit,
  } = mappingState

  const [form] = Form.useForm()
  const queryClient = useQueryClient()

  // Handle adding or editing a mapping
  const handleAddEdit = (record) => {
    setModalMode(record ? 'edit' : 'create')
    setCurrentMapping(record)
    form.resetFields()

    if (record) {
      form.setFieldsValue({
        [entityField]: record[entityField],
        item_id: record.item_id,
        active_flag: record.active_flag,
      })
    }

    setIsModalVisible(true)
  }

  // Handle form submission
  const onFormSubmit = () => {
    form
      .validateFields()
      .then(async (values) => {
        const success = await handleSubmit(values)
        if (success) {
          form.resetFields()
        }
      })
      .catch((error) => {
        console.error('Validation failed:', error)
      })
  }

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <MappingFilter
          filterType={filterType}
          setFilterType={setFilterType}
          searchFilter={searchFilter}
          setSearchFilter={setSearchFilter}
          debouncedMappingIdSearch={debouncedMappingIdSearch}
          entityOptions={itemsData?.value || []}
          entityLabel={entityLabel}
          entitySearchHandler={(value) => debouncedSearch(value)}
          entityOptionRenderer={renderEntityOption}
          itemSearchHandler={(value) => {
            if (value.length >= 2) {
              setIsSearching(true)
              itemSearchHandler(value)
                .then((data) => {
                  setSelectOptions(data.value || [])
                })
                .finally(() => {
                  setIsSearching(false)
                })
            }
          }}
          itemOptions={selectOptions}
          itemOptionRenderer={renderItemOption}
        />

        <div className="d-flex gap-2">
          {showRefreshButton && (
            <AsyncButton
              onClick={() =>
                queryClient.invalidateQueries({ queryKey: [`${entityType}Mappings`] })
              }>
              Refresh
            </AsyncButton>
          )}
          <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddEdit(null)}>
            Add Mapping
          </Button>
        </div>
      </div>

      <Table
        size="small"
        columns={[
          ...columns,
          {
            title: 'Actions',
            key: 'actions',
            render: (_, record) => (
              <Space>
                <Button
                  icon={<EditOutlined />}
                  onClick={() => handleAddEdit(record)}
                  title="Edit"
                />
                <Popconfirm
                  title="Are you sure you want to delete this mapping?"
                  onConfirm={() => handleDelete(record[idField])}
                  okText="Yes"
                  cancelText="No">
                  <Button danger icon={<DeleteOutlined />} title="Delete" />
                </Popconfirm>
              </Space>
            ),
          },
        ]}
        dataSource={mappings}
        rowKey={idField}
        loading={isLoading}
        pagination={{ defaultPageSize: 10 }}
      />

      {/* Modal for creating/editing mappings */}
      <Modal
        title={`${modalMode === 'create' ? 'Create' : 'Edit'} ${title}`}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={onFormSubmit}
        width={600}>
        <MappingForm
          form={form}
          entityField={entityField}
          entityLabel={entityLabel}
          entityOptions={itemsData?.value || []}
          isLoadingEntity={isLoadingItems}
          entitySearchHandler={(value) => debouncedSearch(value)}
          entityOptionRenderer={renderEntityOption}
          itemOptions={selectOptions}
          isSearchingItems={isSearching}
          itemSearchHandler={(value) => {
            if (value.length >= 2) {
              setIsSearching(true)
              itemSearchHandler(value)
                .then((data) => {
                  setSelectOptions(data.value || [])
                })
                .finally(() => {
                  setIsSearching(false)
                })
            }
          }}
          itemOptionRenderer={renderItemOption}
          additionalFields={additionalFormFields}
        />
      </Modal>
    </div>
  )
}

BaseMappingComponent.propTypes = {
  title: PropTypes.string.isRequired,
  entityType: PropTypes.string.isRequired,
  entityLabel: PropTypes.string.isRequired,
  entityField: PropTypes.string.isRequired,
  idField: PropTypes.string.isRequired,
  mappingState: PropTypes.object.isRequired,
  columns: PropTypes.array.isRequired,
  renderEntityOption: PropTypes.func.isRequired,
  renderItemOption: PropTypes.func.isRequired,
  entitySearchHandler: PropTypes.func.isRequired,
  itemSearchHandler: PropTypes.func.isRequired,
  additionalFormFields: PropTypes.node,
  showRefreshButton: PropTypes.bool,
}

export default BaseMappingComponent
