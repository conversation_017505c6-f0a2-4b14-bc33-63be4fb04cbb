import React from 'react'
import { Select } from 'antd'
import { useMappingState } from './useMappingState'
import BaseMappingComponent from './BaseMappingComponent'
import { renderMedicalSupply, renderItem, renderStatus, renderDate } from './MappingConstant'
import {
  getMedicalSuppliesMappings,
  deleteMedicalSuppliesMapping,
  getMedicalSupplies,
  createMedicalSuppliesMapping,
  updateMedicalSuppliesMapping,
  searchApiItems,
  checkDuplicateMedicalSuppliesMapping,
} from './mappingService'
import lists from '../../common/lists'

const { Option } = Select

const MedicalSuppliesMapping = () => {
  // Use the shared mapping state hook
  const mappingState = useMappingState({
    entityType: lists.medical_supplies.listName,
    getItems: getMedicalSupplies,
    getMappings: getMedicalSuppliesMappings,
    createMapping: createMedicalSuppliesMapping,
    updateMapping: updateMedicalSuppliesMapping,
    deleteMapping: deleteMedicalSuppliesMapping,
    checkDuplicateMapping: checkDuplicateMedicalSuppliesMapping,
    idField: 'medical_supplies_his_mapping_id',
    defaultFormValues: {
      active_flag: true,
    },
  })

  // Define table columns
  const columns = [
    {
      title: 'Medical Supplies ID',
      dataIndex: 'medical_supplies_id',
      key: 'medical_supplies_id',
      render: renderMedicalSupply,
    },
    {
      title: 'Item ID',
      dataIndex: 'item_id',
      key: 'item_id',
      render: renderItem,
    },

    {
      title: 'Status',
      dataIndex: 'active_flag',
      key: 'active_flag',
      render: renderStatus,
    },
    {
      title: 'Last Updated',
      dataIndex: 'lu_updated',
      key: 'lu_updated',
      render: renderDate,
    },
  ]

  // Define additional form fields
  const additionalFormFields = null

  // Render entity option for select
  const renderMedicalSupplyOption = (supply) => (
    <Option
      key={supply.medical_supplies_id}
      value={supply.medical_supplies_id}
      label={`${supply.MA_VAT_TU} - ${supply.TEN_VAT_TU}`}>
      {supply.MA_VAT_TU} - {supply.TEN_VAT_TU}
    </Option>
  )

  // Render item option for select
  const renderItemOption = (item) => (
    <Option key={item.item_id} value={item.item_id} label={`${item.item_code} - ${item.name_e}`}>
      {item.item_code} - {item.name_e}
    </Option>
  )

  return (
    <BaseMappingComponent
      title="Medical Supplies Mapping"
      entityType={lists.medical_supplies.listName}
      entityLabel="Medical Supplies"
      entityField="medical_supplies_id"
      idField="medical_supplies_his_mapping_id"
      mappingState={mappingState}
      columns={columns}
      renderEntityOption={renderMedicalSupplyOption}
      renderItemOption={renderItemOption}
      entitySearchHandler={getMedicalSupplies}
      itemSearchHandler={searchApiItems}
      additionalFormFields={additionalFormFields}
    />
  )
}

export default MedicalSuppliesMapping
