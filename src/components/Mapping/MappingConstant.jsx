import React from 'react'
import { Tag } from 'antd'
import PromiseContent from '../../common/components/PromiseContent'
import { getItemService } from '../../common/services'
import lists from '../../common/lists'

/**
 * Renders a medical supply in a table cell
 */
export const renderMedicalSupply = (id) => {
  if (!id) return <span style={{ color: 'gray' }}>N/A</span>

  return (
    <PromiseContent
      promise={() => getItemService(lists.medical_supplies, id)}
      render={(data) => {
        const medicalSupply = data
        return (
          <div>
            <div>
              <strong>{medicalSupply?.MA_VAT_TU || 'notfound'}</strong>
            </div>
            <div style={{ color: 'gray' }}>{medicalSupply?.TEN_VAT_TU}</div>
            <div style={{ fontSize: '11px', color: '#999' }}>{id}</div>
          </div>
        )
      }}
    />
  )
}

/**
 * Renders a medicine in a table cell
 */
export const renderMedicine = (id) => {
  if (!id) return <span style={{ color: 'gray' }}>N/A</span>

  return (
    <PromiseContent
      promise={() => getItemService(lists.medicine, id)}
      render={(data) => {
        const medicine = data
        return (
          <div>
            <div>
              <strong>{medicine?.MA_THUOC || 'notfound'}</strong>
            </div>
            <div style={{ color: 'gray' }}>{medicine?.TEN_THUOC}</div>
            <div style={{ color: 'gray' }}>{medicine?.HAM_LUONG}</div>
            <div style={{ fontSize: '11px', color: '#999' }}>{id}</div>
          </div>
        )
      }}
    />
  )
}

/**
 * Renders a technical service in a table cell
 */
export const renderTechnicalService = (id) => {
  if (!id) return <span style={{ color: 'gray' }}>N/A</span>

  return (
    <PromiseContent
      promise={() => getItemService(lists.technical_services, id)}
      render={(data) => {
        const service = data
        return (
          <div>
            <div>
              <strong>{service?.MA_TUONG_DUONG || 'notfound'}</strong>
            </div>
            <div style={{ color: 'gray' }}>{service?.TEN_DVKT_PHEDUYET}</div>
            <div style={{ fontSize: '11px', color: '#999' }}>{id}</div>
          </div>
        )
      }}
    />
  )
}

/**
 * Renders an item in a table cell
 */
export const renderItem = (id) => {
  return (
    <PromiseContent
      promise={() => getItemService(lists.api_item_view, id)}
      render={(data) => {
        const item = data
        return (
          <div>
            <div>
              <strong>{item?.item_code || 'notfound'}</strong>
            </div>
            <div style={{ color: 'gray' }}>{item?.name_e}</div>
            <div style={{ fontSize: '11px', color: '#999' }}>{id}</div>
          </div>
        )
      }}
    />
  )
}

/**
 * Renders a status tag
 */
export const renderStatus = (active) => (
  <Tag color={active ? 'green' : 'red'}>{active ? 'Active' : 'Inactive'}</Tag>
)

/**
 * Renders a date in a formatted way
 */
export const renderDate = (date) => new Date(date).toLocaleString()

/**
 * Renders a medical supply option for select
 */
export const renderMedicalSupplyOption = (supply) => (
  <option key={supply.medical_supplies_id} value={supply.medical_supplies_id}>
    {supply.MA_VAT_TU} - {supply.TEN_VAT_TU}
  </option>
)

/**
 * Renders a medicine option for select
 */
export const renderMedicineOption = (medicine) => (
  <option key={medicine.medicine_id} value={medicine.medicine_id}>
    {medicine.MA_THUOC} - {medicine.TEN_THUOC}
  </option>
)

/**
 * Renders a technical service option for select
 */
export const renderTechnicalServiceOption = (service) => (
  <option key={service.technical_services_id} value={service.technical_services_id}>
    {service.MA_TUONG_DUONG} - {service.TEN_DVKT_PHEDUYET}
  </option>
)

/**
 * Renders an item option for select
 */
export const renderItemOption = (item) => (
  <option key={item.item_id} value={item.item_id}>
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      <strong>{item.item_code}</strong>
      <span>{item.name_e}</span>
      <span style={{ fontSize: '11px', color: '#999' }}>{item.item_id}</span>
    </div>
  </option>
)
