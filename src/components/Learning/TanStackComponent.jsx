import React, { useState, useMemo, useRef } from 'react'
import { useReactTable, getCoreRowModel, getPaginationRowModel } from '@tanstack/react-table'
import { useVirtualizer } from '@tanstack/react-virtual'
import { Form, Input, InputNumber, Button } from 'antd'
import PropTypes from '../../../../common/PropTypes'

const EditableCell = React.memo(({ value, row, column, updateData }) => {
  const [localValue, setLocalValue] = useState(value)

  const handleChange = (e) => {
    const newValue = column.id === 'age' ? e : e.target.value
    setLocalValue(newValue)
  }

  const handleBlur = () => {
    if (localValue !== value) {
      updateData(row.index, column.id, localValue)
    }
  }

  const inputNode =
    column.id === 'age' ? (
      <InputNumber value={localValue} onChange={handleChange} onBlur={handleBlur} />
    ) : (
      <Input
        // value={localValue}
        value={localValue}
        onChange={handleChange}
        onBlur={handleBlur}
      />
    )

  return <Form.Item style={{ margin: 0 }}>{inputNode}</Form.Item>
})

const TanStackComponent = ({ tableColumns, tableDataSource }) => {
  const [data, setData] = useState(tableDataSource)

  const updateData = (rowIndex, columnId, value) => {
    setData((prev) => {
      const updatedRow = { ...prev[rowIndex], [columnId]: value }
      const newData = [...prev]
      newData[rowIndex] = updatedRow
      return newData
    })
  }

  const memoizedColumns = useMemo(
    () =>
      tableColumns.map((col) => ({
        accessorKey: col.dataIndex,
        header: col.title,
        cell: (info) => (
          <EditableCell
            value={info.getValue()}
            row={info.row}
            column={info.column}
            updateData={updateData}
          />
        ),
        isEditable: true,
      })),
    [tableColumns, updateData],
  )

  const table = useReactTable({
    data,
    columns: memoizedColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageIndex: 0,
        defaultPageSize: 10,
      },
    },
  })

  return (
    <Form>
      <table>
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th key={header.id}>
                  {header.isPlaceholder ? null : header.column.columnDef.header}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) => (
            <tr key={row.id}>
              {row.getVisibleCells().map((cell) => (
                <td key={cell.id}>{cell.column.columnDef.cell(cell)}</td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </Form>
  )
}
const propTypes = {
  tableColumns: PropTypes.array.isRequired,
  tableDataSource: PropTypes.array.isRequired,
}
TanStackComponent.propTypes = propTypes

export default TanStackComponent
