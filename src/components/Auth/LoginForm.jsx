import { useLocation, useNavigate } from 'react-router-dom'
import { useUI } from '../../common/UIProvider'
import { getAuth, getItemsService, loginService } from '../../common/services'
import { Button, Checkbox, Divider, Form, Image, Input, Tag } from 'antd'
import { useForm } from 'antd/es/form/Form'
import config from '../../common/config'
import { useDispatch, useSelector } from 'react-redux'
import { MODULE_AUTH, authWithOffice365 } from '../../store/auth'
import { useEffect } from 'react'
import { useMsal } from '@azure/msal-react'
import { loginRequest } from '../../office365AuthConfig'
import { LOGIN_TYPES } from '../../common/constant'
import lists from '../../common/lists'
import { handleError } from '../../common/helpers'
import imgLogo from '../../assets/Logo.png'
import imgMSLogo from '../../assets/microsoft-logo.png'
import { checkUserPermission } from '../../services/permissionService'
import { PERMISSION } from './AuthConstant'

function LoginForm() {
  const location = useLocation()
  const navigate = useNavigate()
  const ui = useUI()
  const [form] = useForm()
  const returnUrl = location.state?.returnUrl || '/'
  const authState = useSelector((state) => state[MODULE_AUTH])
  const dispatch = useDispatch()

  useEffect(() => {
    const savedUsername = localStorage.getItem(config.LOCAL_USERNAME)
    if (savedUsername) {
      form.setFieldsValue({ email: savedUsername, remember: true })
    }
  }, [form])

  const handleSubmit = async () => {
    try {
      await form.validateFields()
    } catch (error) {
      return
    }
    const values = form.getFieldsValue()

    ui.setLoading(true)

    try {
      const loginRes = await loginService(values.email, values.password)

      if (!loginRes?.user) {
        throw new Error('Empty user! Incorrect username or password!')
      }

      localStorage.setItem(config.LOCAL_ACCESS_TOKEN, loginRes.token.accessToken)
      localStorage.setItem(config.LOCAL_REFRESH_TOKEN, loginRes.token.refreshToken)
      localStorage.setItem(config.LOCAL_AUTHENTICATED, JSON.stringify(loginRes))
      localStorage.setItem(config.LOCAL_PROFILE, JSON.stringify(await getAuth()))

      if (values.remember) {
        localStorage.setItem(config.LOCAL_USERNAME, values.email)
      } else {
        localStorage.removeItem(config.LOCAL_USERNAME)
      }

      if (returnUrl === '/login') {
        navigate('/', { replace: true })
      } else {
        navigate(returnUrl, { replace: true })
      }
    } catch (error) {
      const message = handleError(error)

      if (message.indexOf('password') > -1 || message == '401') {
        ui.notiError('Thông tin đăng nhập chưa chính xác!')
      } else {
        ui.notiError('Lỗi hệ thống, vui lòng thử lại trong giây lát!')
      }
    }

    ui.setLoading(false)
  }

  return (
    <div>
      <div className="login-page">
        <div className="login-page-container">
          <div className="container">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <img className="login-page-container-logo" src={imgLogo} alt="" />
              </div>
              <div>
                <div className="fw-bold">Welcome FV Hospital </div>
                <div>LOGIN</div>
              </div>
            </div>
            <div className="row">
              <Form form={form} layout="vertical">
                <Form.Item
                  // initialValue="xyz100871"
                  label="Email đăng nhập"
                  name="email"
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập Email đăng nhập!',
                    },
                  ]}>
                  <Input onPressEnter={handleSubmit} />
                </Form.Item>

                <Form.Item
                  label="Mật khẩu"
                  name="password"
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập Mật khẩu!',
                    },
                  ]}>
                  <Input.Password onPressEnter={handleSubmit} />
                </Form.Item>

                <Form.Item initialValue={'checked'} name="remember" valuePropName="checked">
                  <Checkbox>Ghi nhớ đăng nhập</Checkbox>
                </Form.Item>

                {config.IN_TEST && (
                  <Tag className="w-100 mb-3">
                    <div>xyz103757 is CASHIER</div>
                    <div>xyz100871 is SUPER_ADMIN</div>
                    <div>xyz102143 is SIO_MANAGER</div>
                    <div>xyz100029 is SIO</div>
                    <div>xyz103867 is OT</div>
                    <div>xyz102475 is MS</div>
                  </Tag>
                )}

                <div className="d-flex justify-content-end">
                  <Button
                    icon={<i className="fa-solid fa-arrow-right-to-bracket"></i>}
                    loading={ui.loading}
                    onClick={handleSubmit}
                    type="primary">
                    Đăng nhập
                  </Button>
                </div>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoginForm
