# Drag & Drop PDF Field Positioning (Simplified)

## Tổng quan

Hệ thống PDF field positioning đã được đơn giản hóa, loại bỏ hoàn toàn việc trích xuất PDF form fields. Thay vào đó, người dùng chỉ cần kéo thả các trường dữ liệu trực tiếp vào PDF để xác định vị trí (x, y, page). Hệ thống hỗ trợ cấu hình mặc định trong code và tính năng export config để developer dễ dàng tạo template mới.

## Cách sử dụng

### 1. Chọn PDF Template
- Trong ApprovalLetterForm, chọn template PDF từ dropdown "Chọn Template"
- Hệ thống sẽ tự động tải PDF template và hiển thị giao diện kéo thả

### 2. Đặt vị trí trường dữ liệu

**Giao diện đơn giản hóa** - chỉ có một chế độ kéo thả:

1. **<PERSON>ên trái**: Panel điều khiển với:
   - <PERSON><PERSON> sách các trường dữ liệu có sẵn (fullName, sex, dob, etc.)
   - Nút "Thêm trường mặc định" để áp dụng cấu hình có sẵn
   - Nút "Preview PDF" để xem kết quả
   - Nút "Export Config" để xuất cấu hình hiện tại
   - Danh sách các trường đã được đặt vị trí

2. **Bên phải**: Giao diện PDF với khả năng nhận drop

3. **Thao tác**:
   - **Thêm trường mới**: Kéo trường dữ liệu từ panel trái thả trực tiếp vào vị trí mong muốn trên PDF
   - **Điều chỉnh vị trí**: Kéo các trường đã đặt (nhãn xanh) để canh chỉnh vị trí chính xác
   - **Hiển thị tọa độ**: Khi kéo sẽ hiển thị tọa độ real-time để định vị chính xác
   - **Visual feedback**: Trường đang kéo sẽ đổi màu vàng và có hiệu ứng shadow

### 3. Quản lý vị trí trường
- **Xem danh sách**: Panel bên trái hiển thị tất cả trường đã đặt với tọa độ chính xác
- **Điều chỉnh vị trí**: Kéo trực tiếp các nhãn trường trên PDF để canh chỉnh
- **Xóa trường**: Bấm nút xóa (🗑️) bên cạnh mỗi trường để loại bỏ
- **Thêm mặc định**: Bấm "Thêm trường mặc định" để áp dụng cấu hình có sẵn của template
- **Hiển thị tọa độ**: Tooltip hiển thị tọa độ chính xác khi di chuột hoặc kéo

### 4. Export Config và Lưu
1. **Export Config**: Bấm "Export Config" để xuất cấu hình vị trí ra console
2. **Preview**: Bấm "Preview PDF" để xem PDF với các trường đã được điền
3. **Lưu**: Bấm "Lưu vào DocumentStore" để lưu PDF và cấu hình vị trí

### 5. Tạo cấu hình mặc định cho template mới
1. Chọn template PDF
2. Kéo thả các trường vào vị trí mong muốn
3. Bấm "Export Config"
4. Copy cấu hình từ console
5. Paste vào `defaultFieldPositions` trong code với template ID chính xác

## Dữ liệu có sẵn

Các trường dữ liệu được tự động lấy từ:
- **Thông tin bệnh nhân**: fullName, sex, dob, hn, phone, cccd
- **Thông tin thẻ BHYT**: card, cardDate, address
- **Thông tin khám**: visitId, departmentName
- **Thông tin hệ thống**: currentDate, currentDateTime, hospitalName

## Lưu trữ cấu hình

- Cấu hình vị trí được lưu trong cột `note` của bảng `medical_record_form`
- Định dạng JSON bao gồm:
  - `fieldPositions`: Mảng các vị trí trường với x, y, page, fieldKey, value
  - `templateId`: ID của template được sử dụng
  - `lastSavedFileName`: Tên file PDF được lưu
  - `lastSavedAt`: Thời gian lưu

## Cấu hình mặc định

Mỗi template có thể có cấu hình vị trí mặc định được lưu trong code:
```javascript
const defaultFieldPositions = {
  template_1: {
    fullName: { x: 150, y: 200, page: 1 },
    sex: { x: 300, y: 200, page: 1 },
    dob: { x: 450, y: 200, page: 1 },
    // ... các trường khác
  },
  template_2: {
    fullName: { x: 180, y: 220, page: 1 },
    // ... cấu hình khác cho template 2
  }
}
```

## Export Config Feature

Tính năng "Export Config" giúp developer dễ dàng tạo cấu hình mặc định:

1. **Workflow**:
   - Kéo thả các trường vào vị trí mong muốn
   - Bấm "Export Config"
   - Console sẽ log ra cấu hình theo format:
   ```javascript
   {
     "template_id": {
       "fieldKey": { "x": 123, "y": 456, "page": 1 },
       // ... other fields
     }
   }
   ```

2. **Copy-paste friendly**: Format được thiết kế để dễ dàng copy từ console và paste vào code

3. **Tự động làm tròn**: Tọa độ được làm tròn để dễ đọc và chỉnh sửa

## Tính năng nổi bật

1. **Đơn giản hóa**: Loại bỏ hoàn toàn PDF form field extraction
2. **Kéo thả trực tiếp**: Thả trường vào vị trí chính xác trên PDF
3. **Điều chỉnh vị trí real-time**: Kéo các trường đã đặt để canh chỉnh vị trí
4. **Hiển thị tọa độ**: Tooltip hiển thị tọa độ X,Y chính xác khi kéo
5. **Visual feedback**: Hiệu ứng màu sắc và shadow khi kéo thả
6. **Export Config**: Xuất cấu hình vị trí để developer dễ dàng tạo template mới
7. **Cấu hình mặc định trong code**: Template có sẵn vị trí cho các trường phổ biến
8. **Hỗ trợ tiếng Việt**: Font tùy chỉnh cho ký tự tiếng Việt
9. **Copy-paste friendly**: Format config dễ dàng copy từ console vào code

## Cấu trúc code

- `PopulatePDF.jsx`: Component chính quản lý PDF (đã đơn giản hóa, loại bỏ form field extraction)
- `DragDropFieldMapper.jsx`: Component quản lý trường và vị trí (có thêm Export Config)
- `DroppablePdfViewer.jsx`: PDF viewer hỗ trợ drag & drop
- `ApprovalLetterForm.jsx`: Form chính với cấu hình mặc định trong code

## Workflow cho Developer

### Tạo template mới:
1. **Thêm template vào hệ thống**: Cập nhật array `pdfTemplates`
2. **Tạo cấu hình mặc định**:
   - Chọn template trong UI
   - Kéo thả các trường vào vị trí mong muốn
   - Bấm "Export Config"
   - Copy cấu hình từ console
3. **Cập nhật code**: Paste cấu hình vào `defaultFieldPositions` với template ID chính xác
4. **Test**: Template sẽ tự động có sẵn các vị trí mặc định

### Thêm trường dữ liệu mới:
1. Cập nhật object `availableFields` trong `ApprovalLetterForm.jsx`
2. Trường sẽ tự động xuất hiện trong panel kéo thả
3. Sử dụng Export Config để cập nhật cấu hình mặc định

### Lợi ích của workflow mới:
- ✅ **Không cần PDF form fields**: Hoạt động với bất kỳ PDF nào
- ✅ **Dễ dàng tạo template**: Chỉ cần kéo thả và copy config
- ✅ **Cấu hình trong code**: Dễ version control và maintain
- ✅ **Copy-paste friendly**: Format JSON chuẩn, dễ đọc và chỉnh sửa
