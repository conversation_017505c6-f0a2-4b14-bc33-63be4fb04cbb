import _ from 'lodash'
import { displayDate, handleError } from '../../common/helpers'
import dayjs from '../../common/dayjs'
import { v4 } from 'uuid'
import { MEDICAL_RECORD_FORM_TYPE } from './MedicalRecordConstant'
import {
  addListItemService,
  getItemsService,
  patchMultiRecordDetails,
  deleteListItemService,
} from '../../common/services'
import lists from '../../common/lists'
import { capitalizeWords } from '../../SI/helper'
import { MEDICAL_RECORD_FIELD_TYPE } from './MedicalRecordField'

export const addMedicalRecordForm = async (r) => {
  return addListItemService(lists.medical_record_form, r)
}

export const deleteMedicalRecordForm = async (medicalRecordFormId) => {
  try {
    if (!medicalRecordFormId) {
      throw new Error('Medical record form ID is required')
    }

    return await deleteListItemService(lists.medical_record_form, medicalRecordFormId)
  } catch (error) {
    handleError(error, 'deleteMedicalRecordForm')
    throw error
  }
}

export const handleInitTreatmentForm = async (
  visitChargeDetailsData = [],
  selectedVisit = null,
  mainVisit = null,
) => {
  if (!visitChargeDetailsData[0] || !selectedVisit) {
    return
  }

  try {
    // get existing medical_record_form by title && medical_record_form_type_rcd
    const existingMedicalRecordForms = await getItemsService(lists.medical_record_form, {
      filter: `medical_record_form_type_rcd eq '${MEDICAL_RECORD_FORM_TYPE.PHIEU_DIEU_TRI.key}'
        and patient_visit_id eq ${selectedVisit.patient_visit_id}`,
    })

    const validChargeList = visitChargeDetailsData.filter(
      (r) => !!r.doctor_name && !!r.manual_ss_cover_flag,
    )

    // 1. Nhóm bản ghi theo caregiver_employee_id
    const groups = _.groupBy(
      validChargeList.filter((r) => !!r.doctor_name),
      (r) => `${r.doctor_name}_${dayjs(r.NGAY_YL).format('YYYY-MM-DD')}`,
    )

    // 2. Với mỗi doctor_name và ngày tương ứng, tạo phiếu mới
    const medicalRecordFormList = Object.entries(groups)
      .map(([doctor_name_and_NGAY_YL, items]) => {
        const firstItem = items[0]
        const doctor_name = firstItem.doctor_name
        const NGAY_YL = firstItem.NGAY_YL
        const employee_id = firstItem.caregiver_employee_id

        // valid charge group
        if (!doctor_name || doctor_name == 'null' || !validChargeList.length) {
          return null
        }

        const minDate = dayjs(_.min(items.map((item) => item.NGAY_YL)))
        const minDateTitle = minDate.format('YYYY-MM-DD HH:mm')
        const formDate = minDate

        const existingForm = existingMedicalRecordForms?.value?.find((form) => {
          return (
            `${form.chairperson}_${dayjs(form.form_date_time).format('YYYY-MM-DD')}` ===
            doctor_name_and_NGAY_YL
          )
        })

        const form = {
          medical_record_form_id: existingForm?.medical_record_form_id || v4(),
          medical_record_form_type_rcd: MEDICAL_RECORD_FORM_TYPE.PHIEU_DIEU_TRI.key,
          patient_id: mainVisit.patient_id,
          patient_visit_id: selectedVisit.patient_visit_id,
          form_date_time: formDate,
          title: minDateTitle,
          chairperson: doctor_name,
          employee_id: employee_id,
        }

        return form
      })
      .filter(Boolean)

    await patchMultiRecordDetails(lists.medical_record_form, medicalRecordFormList)
  } catch (error) {
    handleError(error, 'handleInitTreatmentForm')
  }
}

export const handleInitConsultationForm = async (
  visitChargeDetailsData = [],
  selectedVisit = null,
  mainVisit = null,
) => {
  if (!visitChargeDetailsData[0]) {
    return
  }

  try {
    // Get existing consultation forms for this patient visit
    const existingMedicalRecordForms = await getItemsService(lists.medical_record_form, {
      filter: `medical_record_form_type_rcd eq '${MEDICAL_RECORD_FORM_TYPE.PHIEU_HOI_CHAN.key}'
        and patient_visit_id eq ${selectedVisit.patient_visit_id}`,
    })

    // Get medical_condition records (XML5 data) for this patient visit
    const medicalConditions = await getItemsService(lists.medical_condition, {
      filter: `patient_visit_id eq ${selectedVisit.patient_visit_id} and active_flag eq true`,
    })

    if (!medicalConditions?.value || medicalConditions.value.length === 0) {
      return // No medical conditions to create consultation forms from
    }

    // Get the visit date/time to use for consultation forms
    const visitDateTime = selectedVisit?.actual_visit_datetime || null

    // Create one consultation form for each medical_condition record
    const medicalRecordFormList = medicalConditions.value
      .map((condition) => {
        if (!condition.HOI_CHAN) {
          return
        }

        // Generate a title based on the visit date or creation date of the medical condition
        // Use visit date if available, otherwise fall back to condition update date or current time
        const conditionDate = visitDateTime || condition.lu_updated || dayjs().toISOString()
        const formTitle = `${dayjs(conditionDate).format('YYYY-MM-DD HH:mm')}`

        // Check if a form already exists for this medical_condition
        const existingForm = existingMedicalRecordForms?.value?.find(
          (form) => form.medical_condition_id === condition.medical_condition_id,
        )

        const form = {
          medical_record_form_id: existingForm?.medical_record_form_id || v4(),
          medical_record_form_type_rcd: MEDICAL_RECORD_FORM_TYPE.PHIEU_HOI_CHAN.key,
          patient_id: mainVisit.patient_id,
          patient_visit_id: selectedVisit.patient_visit_id,
          medical_condition_id: condition.medical_condition_id, // Link to the specific medical_condition
          form_date_time: conditionDate,
          title: formTitle,
          // Pre-populate the disease_and_treatment_summary with the HOI_CHAN field
          disease_and_treatment_summary: condition.HOI_CHAN || '',
        }

        return form
      })
      .filter(Boolean)

    await patchMultiRecordDetails(lists.medical_record_form, medicalRecordFormList)
  } catch (error) {
    handleError(error, 'handleInitConsultationForm')
  }
}

export const handleInitOPDMedicalRecord = async (
  visitChargeDetailsData = [],
  selectedVisit = null,
  mainVisit = null,
) => {
  // Check eligibility criteria for OPD medical record using mainVisit
  if (mainVisit?.visit_type_group_rcd === 'IPD') {
    return
  }

  if (!visitChargeDetailsData[0] || !selectedVisit || !mainVisit) {
    return
  }

  try {
    // Check if an OPD medical record already exists for this visit
    const existingMedicalRecordForms = await getItemsService(lists.medical_record_form, {
      filter: `medical_record_form_type_rcd eq '${MEDICAL_RECORD_FORM_TYPE.BENH_AN_NGOAI_TRU.key}'
        and patient_visit_id eq ${mainVisit.patient_visit_id}`,
    })

    // If an OPD medical record already exists, don't create a new one
    if (existingMedicalRecordForms?.value?.length > 0) {
      return
    }

    // Create a new OPD medical record for this visit
    // For OPD medical records, we create one per visit
    const visitDateTime = mainVisit?.actual_visit_datetime || dayjs().toISOString()
    const formTitle = `${dayjs(visitDateTime).format('YYYY-MM-DD HH:mm')}`

    const form = {
      medical_record_form_id: v4(),
      medical_record_form_type_rcd: MEDICAL_RECORD_FORM_TYPE.BENH_AN_NGOAI_TRU.key,
      patient_id: mainVisit.patient_id,
      patient_visit_id: mainVisit.patient_visit_id,
      form_date_time: visitDateTime,
      title: formTitle,
      // Pre-populate with any available data from the visit
      signed_date_time: dayjs().toISOString(),
    }

    await patchMultiRecordDetails(lists.medical_record_form, [form])
  } catch (error) {
    handleError(error, 'handleInitOPDMedicalRecord')
  }
}

export const initApprovalLetterFormByVisit = async (mainVisit = null) => {
  if (!mainVisit) {
    return
  }

  try {
    const existingMedicalRecordForms = await getItemsService(lists.medical_record_form, {
      filter: `medical_record_form_type_rcd eq '${MEDICAL_RECORD_FORM_TYPE.GIAY_CHAP_THUAN.key}'
        and (patient_visit_id eq ${mainVisit.patient_visit_id} or patient_id eq ${mainVisit.patient_id})`,
    })

    if (existingMedicalRecordForms?.value?.length > 0) {
      return
    }

    const visitDateTime = mainVisit?.actual_visit_datetime || dayjs().toISOString()
    const formTitle = `VISIT - ${dayjs(visitDateTime).format('YYYY-MM-DD HH:mm')}`

    const form = {
      medical_record_form_id: v4(),
      medical_record_form_type_rcd: MEDICAL_RECORD_FORM_TYPE.GIAY_CHAP_THUAN.key,
      patient_id: mainVisit.patient_id,
      patient_visit_id: mainVisit.patient_visit_id,
      form_date_time: visitDateTime,
      title: formTitle,
      signed_date_time: dayjs().toISOString(),
    }

    await patchMultiRecordDetails(lists.medical_record_form, [form])
  } catch (error) {
    handleError(error, 'handleInitApprovalLetterForm')
  }
}

export const initApprovalLetterFormByPatient = async (patient = null) => {
  if (!patient) {
    return
  }

  try {
    const existingMedicalRecordForms = await getItemsService(lists.medical_record_form, {
      filter: `medical_record_form_type_rcd eq '${MEDICAL_RECORD_FORM_TYPE.GIAY_CHAP_THUAN.key}'
        and patient_id eq ${patient.Patient_id} and patient_visit_id eq null`,
    })

    if (existingMedicalRecordForms?.value?.length > 0) {
      return
    }

    const formTitle = `HN - ${dayjs().format('YYYY-MM-DD HH:mm')}`

    const form = {
      medical_record_form_id: v4(),
      medical_record_form_type_rcd: MEDICAL_RECORD_FORM_TYPE.GIAY_CHAP_THUAN.key,
      patient_id: patient.Patient_id,
      patient_visit_id: null,
      form_date_time: dayjs(),
      title: formTitle,
      signed_date_time: dayjs(),
    }

    await patchMultiRecordDetails(lists.medical_record_form, [form])
  } catch (error) {
    handleError(error, 'handleInitApprovalLetterForm')
  }
}

export const handleInitApprovalLetterForm = async (mainVisit = null, patient = null) => {
  if (mainVisit) {
    await initApprovalLetterFormByVisit(mainVisit)
    return
  } else if (patient) {
    await initApprovalLetterFormByPatient(patient)
    return
  }
}

export const handleInitMedicalOrderForm = async (visitChargeDetailsData = [], mainVisit = null) => {
  if (
    mainVisit?.visit_type_group_rcd === 'IPD' ||
    !!mainVisit?.treatment_course_flag ||
    mainVisit?.department_ss_code === 'K33'
  ) {
    return
  }

  if (!visitChargeDetailsData[0] || !mainVisit) {
    return
  }

  try {
    const existingMedicalRecordForms = await getItemsService(lists.medical_record_form, {
      filter: `medical_record_form_type_rcd eq '${MEDICAL_RECORD_FORM_TYPE.PHIEU_CHI_DINH.key}'
        and patient_visit_id eq ${mainVisit.patient_visit_id}`,
    })

    const validChargeList = visitChargeDetailsData.filter(
      (r) =>
        !!r.doctor_name &&
        !!r.manual_ss_cover_flag &&
        ['1', '2', '3'].includes(r.ss_item_group_rcd),
    )

    // 1. Nhóm bản ghi theo caregiver_employee_id
    const groups = _.groupBy(
      validChargeList.filter((r) => !!r.doctor_name),
      (r) => r.doctor_name,
    )

    // 2. Với mỗi caregiver_employee_id tạo phiếu mới
    const medicalRecordFormList = Object.entries(groups)
      .map(([doctor_name, items]) => {
        const firstItem = items[0]
        const employee_id = firstItem.caregiver_employee_id

        const minDate = dayjs(_.min(items.map((item) => item.NGAY_YL)))
        const minDateTitle = minDate.format('YYYY-MM-DD HH:mm')
        const formDate = minDate

        const existingForm = existingMedicalRecordForms?.value?.find((form) => {
          return form.chairperson === doctor_name
        })

        const form = {
          medical_record_form_id: existingForm?.medical_record_form_id || v4(),
          medical_record_form_type_rcd: MEDICAL_RECORD_FORM_TYPE.PHIEU_CHI_DINH.key,
          patient_id: mainVisit.patient_id,
          patient_visit_id: mainVisit.patient_visit_id,
          form_date_time: formDate,
          title: minDateTitle,
          chairperson: doctor_name,
          employee_id: employee_id,
        }

        return form
      })
      .filter(Boolean)

    await patchMultiRecordDetails(lists.medical_record_form, medicalRecordFormList)
  } catch (error) {
    handleError(error, 'handleInitOPDMedicalRecord')
  }
}

export const formatChemoPrescriptionDetail = (chemoPrescriptionDetail) => {
  const sortedData = chemoPrescriptionDetail.sort((a, b) => a.p_order - b.p_order)

  return sortedData
    .map((item, index) => {
      const dose = (item.item_name_dose || '') + (item.item_name_dose_uom || '').trim()
      const lines = [`${index + 1}. ${(item.item_name || '').trim()} ${dose}`]

      if (item.solvent_v) {
        lines.push(`${item.solvent_v.trim()}. ${(item.infusion_v || '').trim()}`)
      }
      if (item.add_ins_v) {
        lines.push(`${item.add_ins_v.trim()}`)
      }

      return lines.join('\n')
    })
    .join('\n\n')
}

export const formatGeneralPrescriptionDetail = (details) => {
  return details
    .map((item, idx) => {
      const item_name = (item.item_name || '').trim()
      const dos_ins_v = capitalizeWords(item.dos_ins_v || '') // .split(`\n`)[0]
      const quantity = String(item.quantity).padStart(2, '0')
      const duration = String(item.duration).padStart(2, '0')
      const duration_uom_vn = (item.duration_uom_vn || '').trim()

      const line1 = `${idx + 1}. ${item_name}`
      const line2 = `${dos_ins_v}`
      const line3 = `Số lượng: ${quantity}. Tổng điều trị: ${duration} ${duration_uom_vn}`

      return [line1, line2, line3].join('\n')
    })
    .join('\n\n')
}
