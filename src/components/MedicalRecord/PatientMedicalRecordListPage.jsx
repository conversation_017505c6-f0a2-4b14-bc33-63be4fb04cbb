import React, { useState } from 'react'
import { usePatient } from '../../queryHooks/usePatient'
import { PATIENT_COLUMNS } from '../Patient/PatientConstant'
import { Button, Modal, Table } from 'antd'
import MedicalRecordList from './MedicalRecordList'
import PatientInfoSection from '../Patient/PatientInfoSection'
import { MEDICAL_RECORD_VIEW_MODE } from './MedicalRecordConstant'

const PatientMedicalRecordListPage = ({ filterHN }) => {
  const [columns, setColumns] = useState(PATIENT_COLUMNS)
  const [openMedicalRecordList, setOpenMedicalRecordList] = useState({
    open: false,
    patient: null,
  })

  const { patientList, isLoading, isFetching, refetchAll } = usePatient({
    filter: {
      filterHN,
      strict: true, // only show with HN
    },
  })

  return (
    <>
      <div className="text-warning text-center">
        <i>{filterHN ? '' : 'Vui lòng chọn HN'}</i>
      </div>
      <div hidden={filterHN ? false : true}>
        <Table
          dataSource={patientList}
          className="custom-table"
          size="small"
          scroll={{ x: columns.map((c) => c.width).reduce((a, b) => a + b) + 100 }}
          columns={[
            ...columns,
            {
              title: ' ',
              key: 'action',
              width: 30,
              align: 'right',
              render: (_, record) => {
                return (
                  <div className="d-flex justify-content-end gap-2">
                    <Button
                      variant="filled"
                      color="blue"
                      icon={<i className="fa-solid fa-folder-open ms-1"></i>}
                      onClick={() => {
                        setOpenMedicalRecordList({ open: true, patient: record })
                      }}></Button>
                  </div>
                )
              },
            },
          ]}
          rowKey="Patient_id"
          loading={isLoading || isFetching}
          pagination={{ defaultPageSize: 10 }}
        />
      </div>

      {openMedicalRecordList.open && (
        <Modal
          className="modal-full"
          width={2000}
          open={openMedicalRecordList.open}
          onCancel={() => setOpenMedicalRecordList({})}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
          footer={null}>
          <div className="mx-2">
            <div className="header sticky-top">
              <div className="header-title">HỒ SƠ BỆNH ÁN</div>
              <div className="header-back">
                <i className="fa fa-times ps-1 py-2" onClick={() => setOpenMedicalRecordList({})} />
              </div>
            </div>
            <PatientInfoSection currentPatient={openMedicalRecordList.patient} stickTop={44} />
            <MedicalRecordList
              patientId={openMedicalRecordList.patient?.Patient_id}
              medicalRecordViewMode={MEDICAL_RECORD_VIEW_MODE.BY_PATIENT}
            />
          </div>
        </Modal>
      )}
    </>
  )
}

export default PatientMedicalRecordListPage
