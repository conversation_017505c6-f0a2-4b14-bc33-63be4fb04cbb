import { DatePicker, Form, Input, Select } from 'antd'
import useApp from 'antd/es/app/useApp'
import { useForm } from 'antd/es/form/Form'
import React from 'react'
import { FORM_MODE, FORMAT_DATETIME } from '../../common/constant'
import { MEDICAL_RECORD_FORM_TYPE } from './MedicalRecordConstant'
import { filterOption, handleError } from '../../common/helpers'
import { addListItemService } from '../../common/services'
import lists from '../../common/lists'
import dayjs from '../../common/dayjs'
import { v4 } from 'uuid'
import AsyncButton from '../../common/components/AsyncButton'

const MedicalRecordForm = ({
  patientId,
  patientVisitId,
  formMode = FORM_MODE.edit,
  onSubmit = () => {},
}) => {
  const [form] = useForm()
  const app = useApp()

  const handleSubmit = async () => {
    try {
      await form.validateFields()
    } catch (error) {
      app.message.error('Vui lòng nhập thông tin bắt buộc!')
      return
    }

    try {
      const values = form.getFieldsValue()

      const newRecord = {
        medical_record_form_id: v4(),
        medical_record_form_type_rcd: values.medical_record_form_type_rcd,
        patient_id: patientId,
        patient_visit_id: patientVisitId,
        form_date_time: values.form_date_time,
        title: values.title,
      }

      const submited = await addListItemService(lists.medical_record_form, newRecord)

      onSubmit(submited)
    } catch (error) {
      handleError(error, 'MedicalRecordForm>handleSubmit')
      app.message.error('Tạo phiếu thất bại!')
    }
  }

  return (
    <div>
      <Form form={form} layout="vertical">
        <Form.Item
          name={'medical_record_form_type_rcd'}
          label="Loại phiếu"
          rules={[{ required: true, message: 'Vui lòng chọn loại phiếu' }]}>
          <Select showSearch filterOption={filterOption}>
            {Object.entries(MEDICAL_RECORD_FORM_TYPE).map(([key, item]) => (
              <Select.Option key={key} value={key}>
                {item.nameE}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="form_date_time"
          initialValue={dayjs()}
          label="Thời gian phiếu"
          rules={[{ required: true, message: 'Vui lòng nhập Thời gian phiếu' }]}>
          <DatePicker className="w-100" showTime format={'YYYY-MM-DD HH:mm'} />
        </Form.Item>

        <Form.Item
          name="title"
          initialValue={dayjs().format('YYYY-MM-DD HH:mm')}
          label="Tiêu đề"
          rules={[{ required: true, message: 'Vui lòng nhập tiêu đề' }]}>
          <Input />
        </Form.Item>

        <div className="d-flex justify-content-end gap-2">
          <AsyncButton
            variant="solid"
            color="primary"
            icon={<i className="fa fa-plus" />}
            onClick={handleSubmit}>
            TẠO
          </AsyncButton>
        </div>
      </Form>
    </div>
  )
}

export default MedicalRecordForm
