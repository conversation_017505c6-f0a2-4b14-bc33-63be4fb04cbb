import { But<PERSON>, <PERSON>, Switch, Row, Col } from 'antd'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useQueryClient } from '@tanstack/react-query'
import { useForm } from 'antd/es/form/Form'
import useDeepCompareEffect from 'use-deep-compare-effect'
import { useMedicalRecord, MEDICAL_RECORD_KEYS } from '../../queryHooks/useMedicalRecord'
import { displayDate, handleError } from '../../common/helpers'
import { updateListItemService } from '../../common/services'
import lists from '../../common/lists'
import { FORM_MODE } from '../../common/constant'
import MedicalRecordField, { MEDICAL_RECORD_FIELD_TYPE } from './MedicalRecordField'
import useApp from 'antd/es/app/useApp'
import DocumentStore from '../../common/components/DocumentStore/DocumentStore'
import PdfViewer from '../../common/components/PdfViewer'
import dayjs from '../../common/dayjs'
import { usePatientVisit } from '../Visit/hooks/usePatientVisit'
import fvLogoWithText from '../../assets/logoFV-withtext.png'
import { MODULE_AUTH } from '../../store/auth'
import { handlePrintPDF } from '../../SI/helper'
import { approvalLetterContent, MEDICAL_RECORD_FORM_TYPE } from './MedicalRecordConstant'
import AsyncButton from '../../common/components/AsyncButton'
import { usePatient } from '../../queryHooks/usePatient'

const CommonMedicalRecordForm = ({
  mainVisit,
  selectedMedicalRecordId,
  patientId,
  formType = MEDICAL_RECORD_FORM_TYPE.CCCD,
  editFields = [
    {
      fieldName: 'title',
      fieldType: MEDICAL_RECORD_FIELD_TYPE.TEXT,
      label: 'Tiêu đề',
    },
  ],
}) => {
  const [form] = useForm()
  const app = useApp()
  const queryClient = useQueryClient()

  // hooks
  const { medicalRecordDetail, medicalCondition } = useMedicalRecord({
    medicalRecordFormId: selectedMedicalRecordId,
  })
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  const [formMode, setFormMode] = useState(FORM_MODE.view)
  const [openDocumentStore, setOpenDocumentStore] = useState(false)
  const [attachments, setAttachments] = useState([])
  const [viewFileOnly, setViewFileOnly] = useState()

  const isEdit = formMode === FORM_MODE.edit

  // default values
  useDeepCompareEffect(() => {
    form.setFieldsValue({
      ...medicalRecordDetail,
    })
  }, [medicalRecordDetail, medicalCondition, form])

  useEffect(() => {
    setViewFileOnly(!!attachments[0] && !isEdit)
  }, [attachments, isEdit])

  const handleSaveMedicalRecordForm = async () => {
    if (!selectedMedicalRecordId) {
      app.message.error('Vui lòng chọn phiếu hội chẩn')
      return
    }

    const values = form.getFieldsValue()

    try {
      const newRecord = {
        ...values,
        lu_user_id: currentUser?.User_id,
      }

      await updateListItemService(lists.medical_record_form, selectedMedicalRecordId, newRecord)

      // Invalidate the query cache to force a refresh of the medical record data
      queryClient.invalidateQueries({
        queryKey: [MEDICAL_RECORD_KEYS.MEDICAL_RECORD, selectedMedicalRecordId],
      })

      app.message.success('Lưu thành công')
    } catch (error) {
      handleError(error, 'handleSaveMedicalRecordForm')
    }
  }

  return (
    <div>
      <div
        className="sticky-top d-flex justify-content-between align-items-center gap-2"
        style={{ top: 105 }}>
        <div></div>
        <div className="d-flex align-items-center gap-2">
          {/* toggle to view file or form */}
          <div className="d-flex align-items-center me-2 gap-2">
            <Switch onChange={() => setViewFileOnly(!viewFileOnly)} checked={viewFileOnly} /> Chỉ
            xem File đính kèm
          </div>
          <Button
            variant={isEdit ? 'outlined' : 'solid'}
            color={'blue'}
            icon={<i className="fa fa-edit" />}
            onClick={() => setFormMode(isEdit ? FORM_MODE.view : FORM_MODE.edit)}>
            {isEdit ? 'Tắt Chỉnh sửa' : 'Chỉnh sửa'}
          </Button>
          <AsyncButton
            icon={<i className="fa fa-save" />}
            hidden={!isEdit}
            onClick={handleSaveMedicalRecordForm}>
            Lưu
          </AsyncButton>
          <Button
            type="primary"
            style={{ background: '#2C9538' }}
            icon={
              openDocumentStore ? <i className="fa fa-close" /> : <i className="fa fa-upload" />
            }
            onClick={() => setOpenDocumentStore(!openDocumentStore)}>
            {openDocumentStore ? 'Đóng' : 'Mở'} upload
          </Button>
          <Button
            icon={<i className="fa fa-print" />}
            type="primary"
            style={{ backgroundColor: '#155E75' }}
            onClick={() => {
              setViewFileOnly(false)
              setOpenDocumentStore(false)
              setFormMode(FORM_MODE.view)

              handlePrintPDF(
                `${MEDICAL_RECORD_FORM_TYPE[formType].nameE}_${medicalRecordDetail?.title || ''}`,
              )
            }}>
            In phiếu
          </Button>
        </div>
      </div>

      <div
        className="mt-2 mb-4 shadow-md p-3 rounded-sm"
        style={{ display: openDocumentStore ? 'block' : 'none' }}>
        <DocumentStore
          dataSource={lists.medical_record_form.listName}
          parentID={4} // 4 is DocumentStore
          storeID={selectedMedicalRecordId}
          mode={'Edit'}
          setAttachments={setAttachments}
        />
      </div>

      <Form form={form} layout="vertical">
        <div id="medical-record-form-print" className="mt-3 px-4">
          <div className="mb-4">
            <div>
              <img
                src={fvLogoWithText}
                alt="FV Hospital"
                style={{ height: '40px', marginBottom: '10px' }}
                onError={(e) => {
                  e.target.style.display = 'none'
                  e.target.nextSibling.style.display = 'block'
                }}
              />
              <div style={{ display: 'none' }}>FV THOMSON</div>
            </div>
            <h4 className="text-center fw-bold">{MEDICAL_RECORD_FORM_TYPE[formType].title}</h4>
          </div>

          <div className="d-flex flex-column gap-2">
            {editFields.map((field) => (
              <div key={field.fieldName} className="d-flex align-items-center">
                <MedicalRecordField
                  form={form}
                  formMode={formMode}
                  fieldName={field.fieldName}
                  fieldType={field.fieldType}
                  label={field.label}
                  labelBold={false}
                  labelClassName="w-40"
                  underlineLabel={false}
                />
              </div>
            ))}
          </div>
        </div>
      </Form>

      <div hidden={!viewFileOnly} className="mt-3">
        <PdfViewer
          serverRelativeUrl={attachments[0]?.ServerRelativeUrl}
          fileName={attachments[0]?.Name}
        />
      </div>
    </div>
  )
}

export default CommonMedicalRecordForm
