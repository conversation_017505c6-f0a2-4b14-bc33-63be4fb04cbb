import { Image } from 'antd'
import React from 'react'
import imgPatient from '../../assets/patient-icon.png'
import endpoints from '../../common/endpoints'
import { displayDate } from '../../common/helpers'

const PatientInfoSection = ({ currentPatient = {}, visitCode = '', stickTop = 0 }) => {
  return (
    <div className="profile-container sticky-top p-2" style={{ top: stickTop }}>
      <div className="d-flex justify-content-md-start gap-2 align-items-center">
        <div className="d-flex align-items-center gap-2">
          <Image
            preview={false}
            src={
              currentPatient?.photoID ? endpoints.patientPhoto(currentPatient?.photoID) : undefined
            }
            fallback={imgPatient}
            width={50}
            className="profile-avatar"
            style={{
              imageOrientation: 'none',
              objectFit: 'cover',
            }}
          />
          <h1 style={{ fontSize: '18px' }} className="fw-bold">
            {currentPatient?.Fullname} |
          </h1>
        </div>
        <span>HN: {currentPatient?.HN} |</span>
        <span>NGÀY SINH: {displayDate(currentPatient?.DOB)} |</span>
        <span>GIỚI TÍNH: {currentPatient?.Sex} |</span>
        <span>CCCD: {currentPatient?.Nation_id} |</span>
        {visitCode ? <span>VISIT: {visitCode}</span> : ''}
      </div>
      <hr className="mt-2" />
    </div>
  )
}

export default PatientInfoSection
