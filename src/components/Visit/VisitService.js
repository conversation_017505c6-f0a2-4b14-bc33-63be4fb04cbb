import { FORMAT_DATE } from '../../common/constant'
import dayjs from '../../common/dayjs'
import { batchPromises, formatHN, handleError } from '../../common/helpers'
import lists from '../../common/lists'
import {
  getPortalInsuranceCardService,
  getItemsService,
  patchMultiRecordDetails,
  deleteListItemService,
  getItemService,
  syncVisitDetailChargeService,
  addListItemService,
  updateListItemService,
} from '../../common/services'
import { PROCESSING_STATUS } from './VisitConstant'
import { groupByStatus } from './VisitHelpers'

export const getPatientDatasetByFilterEndWithHN = async (filterHN) => {
  try {
    const formatedHN = formatHN(filterHN)
    let filter = `HN eq '${formatedHN}'`
    let data = await getItemsService(lists.patient_dataset, {
      filter: filter,
      top: 1,
      count: false,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getPatientDatasetByPatientId = async (patient_id) => {
  try {
    let filter = `(Patient_id eq ${patient_id})`
    let data = await getItemsService(lists.patient_dataset, {
      filter: filter,
      top: 1,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getVisitDatasetsByPatientId = async (selectedPatientId, skip = '', top = 10) => {
  try {
    let filter = `(patient_id eq ${selectedPatientId})`
    let data = await getItemsService(lists.visit_dataset, {
      filter: filter,
      skip,
      top,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getVisitDatasetByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
  top = 1,
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId})`
    let data = await getItemsService(lists.visit_dataset, {
      filter: filter,
      skip,
      top,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getPatientVisitsByPatientVisitId = async (patientVisitId, skip = '', top = 10) => {
  try {
    let filter = `(patient_visit_id eq ${patientVisitId})`
    let data = await getItemsService(lists.patient_visit_mapping_view, {
      filter: filter,
      skip,
      top,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}
//#region patient_visit_mapping_view
export const getPatientVisitMappingViews = async (
  skip = '',
  top = 10,
  filterQuery = '',
  count = false,
) => {
  try {
    let filter = filterQuery
    let data = await getItemsService(lists.patient_visit_mapping_view, {
      filter: filter,
      skip,
      top,
      orderBy: 'lu_updated desc',
      count,
      // orderBy: 'actual_visit_datetime desc'
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getPatientVisitMappingViewByPatientVisitId = async (
  patientVisitId,
  skip = '',
  top = 1,
) => {
  try {
    let filter = `(patient_visit_id eq ${patientVisitId})`
    let data = await getItemsService(lists.patient_visit_mapping_view, {
      filter: filter,
      skip,
      top,
    })
    return data
  } catch (error) {
    handleError(error)
  }
}

export const getAllPatientVisitMappingViewsByPatientId = async (patient_id, top = 2000) => {
  try {
    let filter = `(patient_id eq ${patient_id})`
    let data = await getItemsService(lists.patient_visit_mapping_view, {
      filter: filter,
      top,
      count: false,
    })
    return data
  } catch (error) {
    handleError(error)
  }
}

export const selectMergedVisitFields = [
  'patient_visit_id',
  'parent_patient_visit_id',
  'treatment_course_flag',
  'treatment_course_start_date',
  'treatment_course_end_date',
  'patient_id',
  'warning_status',
  'note',
  'ss_confirmation_flag',
  'ss_confirmation_note',
  'lu_user_id',
  'lu_updated',
  'visible_patient_id',
  'fullname',
  'dob',
  'sex',
  'visit_code',
  'visit_type_rcd',
  'visit_type_name_e',
  'visit_type_group_rcd',
  'actual_visit_datetime',
  'closure_visit_datetime',
  'parent_processing_status',
  'NGAY_RA',
  'creation_date_time',
]
export const getAllMergedPatientVisitMappingViewsByParentPatientVisitId = async (
  parent_patient_visit_id,
  top = 50,
) => {
  try {
    let filter = `(parent_patient_visit_id eq ${parent_patient_visit_id})`
    let data = await getItemsService(lists.patient_visit_mapping_view, {
      filter: filter,
      top,
      select: selectMergedVisitFields.join(','),
    })
    return data
  } catch (error) {
    handleError(error, 'getAllMergedPatientVisitMappingViewsByParentPatientVisitId')
  }
}

//#region patient_visit
export const getPatientVisitById = async (patientVisitId) => {
  try {
    let data = await getItemService(lists.patient_visit_mapping_view, patientVisitId)

    return data
  } catch (error) {
    handleError(error)
  }

  return null
}

export const addMultiMergedPatientVisits = async (
  mergedPatientVisitIds,
  parentPatientVisitId,
  currentUser,
  patientId,
) => {
  const createPatientVisit = async (patientVisitId) => {
    try {
      const result = await addListItemService(lists.patient_visit, {
        patient_visit_id: patientVisitId,
        parent_patient_visit_id: parentPatientVisitId,
        patient_id: patientId,
        lu_user_id: currentUser?.User_id,
        lu_updated: dayjs(),
        ss_confirmation_flag: true,
        ss_confirmation_note: 'Auto merge visit',
        processing_status: PROCESSING_STATUS.WAITING_BHYT.name_e,
      })
      return result
    } catch (error) {
      console.error(`Failed to add patient visit ${patientVisitId}`, error)
      return { error, patientVisitId }
    }
  }

  // Wrap each promise in a function
  const promises = mergedPatientVisitIds.map(
    (patientVisitId) => () => createPatientVisit(patientVisitId),
  )
  const results = await batchPromises(promises, 20) // Adjust the batch size as needed
}

export const getPatientVisitByPatientId = async (patientId) => {
  try {
    let filter = `(patient_id eq ${patientId}) and parent_patient_visit_id eq null`
    let data = await getItemsService(lists.patient_visit_mapping_view, {
      filter: filter,
      top: 1,
      orderBy: 'lu_updated desc',
    })
    return data
  } catch (error) {
    handleError(error)
  }
}

export const getPatientVisitList = async ({
  filter = {
    filterHN: '',
    filterVisitDate: [],
    filter_treatment_course_flag: '',
    filterVisitTypes: [],
    processing_status: 'ALL',
  },
} = {}) => {
  let filterString = []

  // Apply processing_status filter based on value
  if (filter.processing_status && filter.processing_status !== 'ALL') {
    filterString.push(`(processing_status eq '${filter.processing_status}')`)
  } else {
    // Default behavior - exclude WAITING_FULLFILL_INFO unless specifically requested
    filterString.push(`(processing_status ne '${PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e}')`)
  }

  if (filter.filterHN) {
    const formatedHN = formatHN(filter.filterHN)
    const tblFileTrackings = await getTblFileTrackingsByMaGiaoDich(filter.filterHN).then(
      (res) => res?.value,
    )
    if (tblFileTrackings?.length > 0) {
      const ssTable1FilterQuery = `(table_1_id eq ${tblFileTrackings[0]?.table_1_id}) and (include eq true)`
      const ssTable1 = await getSsTable1s(null, 1, ssTable1FilterQuery).then((res) => res?.value)
      if (ssTable1?.length > 0) {
        filterString.push(`(patient_visit_id eq ${ssTable1[0]?.patient_visit_id__})`)
      }
    }

    filterString.push(
      `(startswith(visit_code, '${filter.filterHN}') or visible_patient_id eq '${formatedHN}')`,
    )
  }

  if (filter.filterVisitDate?.[0]) {
    filterString.push(`(actual_visit_datetime ge ${filter.filterVisitDate[0].format(FORMAT_DATE)})`)
  }

  if (filter.filterVisitDate?.[1]) {
    filterString.push(
      `(actual_visit_datetime le ${filter.filterVisitDate[1].format(
        FORMAT_DATE,
      )} or actual_visit_datetime eq null)`,
    )
  }

  if (filter.filterVisitTypes[0]) {
    const values = filter.filterVisitTypes.map((item) => `'${item}'`).join(', ')
    filterString.push(`visit_type_rcd in (${values})`)
  }

  // Add filterString to exclude merged visits
  // filterString.push('(parent_patient_visit_id eq null)')

  if (filter.filter_treatment_course_flag) {
    filterString.push(' (treatment_course_flag eq true)')
  }

  filterString.push(
    `(ss_confirmation_flag eq true or processing_status ne '${PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e}')`,
  )

  try {
    const data = await getPatientVisitMappingViews('', 2000, filterString.join(' and '), false)
    const groupData = groupByStatus(
      data?.value?.map((item) => ({
        ...item,
        processing_status: item.parent_processing_status || item.processing_status,
      })),
    )
    return groupData
  } catch (error) {
    handleError(error, 'getPatientVisitList')
    return []
  }
}

export const getVisitTypeRef = async () => {
  const data = await getItemsService(lists.visit_type_ref_nl_view, {
    orderBy: 'visit_type_rcd asc',
    count: false,
  }).then((res) => res.value)

  return data
}

//#region ApiArInvoiceDetailView
export const getAllApiArInvoiceDetailViewsByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
  top = 2000,
  count = false,
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId})`
    let data = await getItemsService(lists.api_ar_invoice_detail_view, {
      filter: filter,
      skip,
      top,
      count,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getApiArInvoiceDetailViewsByInvoiceTransaction = async (
  invoiceTransaction,
  skip = '',
) => {
  try {
    let filter = `(ar_invoice_transaction_text eq '${invoiceTransaction}')`
    let data = await getItemsService(lists.api_ar_invoice_detail_view, {
      filter: filter,
      skip,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region ss_table_1
export const getSSTable1sByPatientVisitId = async (
  selectedPatientVisitId,
  filterParams = {},
  skip = '',
  top = 10,
) => {
  try {
    let filterConditions = [
      `(patient_visit_id__ eq ${selectedPatientVisitId}) and (include eq true)`,
    ]

    // Add filter for MA_LK if provided
    if (filterParams.ma_lk) {
      filterConditions.push(`contains(MA_LK, '${filterParams.ma_lk}')`)
    }

    // Add date range filter if provided
    if (filterParams.startDate && filterParams.endDate) {
      filterConditions.push(
        `(NGAY_VAO ge ${filterParams.startDate} and NGAY_VAO le ${filterParams.endDate})`,
      )
    }

    // Add status filter if provided
    if (filterParams.status) {
      filterConditions.push(`(gate_status eq '${filterParams.status}')`)
    }

    let filter = filterConditions.join(' and ')

    let data = await getItemsService(lists.ss_table_1, {
      filter: filter,
      skip,
      top,
      orderBy: 'lu_updated desc',
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getSsTable1s = async (skip = '', top = 10, filterQuery = '') => {
  try {
    let filter = filterQuery
    let data = await getItemsService(lists.ss_table_1_nl_view, {
      filter: filter,
      skip,
      top,
      orderBy: 'lu_updated desc',
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getTableXmlTracking = async (skip = '', top = 10, filterQuery = '') => {
  try {
    let filter = filterQuery
    let data = await getItemsService(lists.table_xml_tracking, {
      filter: filter,
      skip,
      top,
    })
    return data
  } catch (error) {
    handleError(error)
  }
}

//#region api_charge_detail_view
export const getFullApiChargeDetailViewsByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
  top = 2000,
  count = false,
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId})`
    let data = await getItemsService(lists.api_charge_detail_view, {
      filter: filter,
      skip,
      top,
      count,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getFullCoveredApiChargeDetailViewsByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId}) and (ss_cover_flag eq true)`
    let data = await getItemsService(lists.api_charge_detail_view, {
      filter: filter,
      skip,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getFullUncoveredApiChargeDetailViewsByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId}) and (ss_cover_flag eq false)`
    let data = await getItemsService(lists.api_charge_detail_view, {
      filter: filter,
      skip,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region prescription_diagnosis
export const getSeriousIllnessPrescriptionDiagnosis = async () => {
  try {
    const allData = []
    let skip = 0
    const top = 2000 // Số lượng dòng tối đa mỗi lần lấy
    let hasMoreData = true

    while (hasMoreData) {
      const data = await getItemsService(lists.prescription_diagnosis, {
        filter: '(serious_illness_flag eq true) and (active_flag eq true)',
        top,
        skip,
        orderBy: 'seq_num asc',
      })

      if (data?.value?.length > 0) {
        allData.push(...data.value) // Thêm dữ liệu vào mảng tổng
        skip += top // Tăng giá trị skip để lấy trang tiếp theo
      } else {
        hasMoreData = false // Dừng vòng lặp nếu không còn dữ liệu
      }
    }

    return allData
  } catch (error) {
    handleError(error)
  }
}

export const getSeriousIllnessPrescriptionDiagnosisByIcd10id = async (icd10id) => {
  try {
    const data = await getItemsService(lists.prescription_diagnosis, {
      filter: `(serious_illness_flag eq true) and (active_flag eq true) and (icd10id eq '${icd10id}')`,
      top: 1,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}
//#end region prescription_diagnosis

//#region visit_charge_detail_view
export const getFullVisitChargeDetailViewsByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId})`
    let data = await getItemsService(lists.visit_charge_detail_view, {
      filter: filter,
      skip,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getFullCoveredVisitChargeDetailViewsByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId}) and (manual_ss_cover_flag eq true)`
    let data = await getItemsService(lists.visit_charge_detail_view, {
      filter: filter,
      skip,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getFullOptionalCoveredVisitChargeDetailViewsByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId}) and (manual_ss_cover_flag eq true) and (ss_optional_cover_flag eq true)`
    let data = await getItemsService(lists.visit_charge_detail_view, {
      filter: filter,
      skip,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getFullNonOptionalCoveredVisitChargeDetailViewsByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId}) and (manual_ss_cover_flag eq true) and (ss_optional_cover_flag eq false)`
    let data = await getItemsService(lists.visit_charge_detail_view, {
      filter: filter,
      skip,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getFullUncoveredVisitChargeDetailViewsByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId}) and (manual_ss_cover_flag eq false)`
    let data = await getItemsService(lists.visit_charge_detail_view, {
      filter: filter,
      skip,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}
export const getFullOptionalUncoveredVisitChargeDetailViewsByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId}) and (manual_ss_cover_flag eq false) and (ss_optional_cover_flag eq true)`
    let data = await getItemsService(lists.visit_charge_detail_view, {
      filter: filter,
      skip,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getFullNonOptionalUncoveredVisitChargeDetailViewsByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId}) and (manual_ss_cover_flag eq false) and (ss_optional_cover_flag eq false)`
    let data = await getItemsService(lists.visit_charge_detail_view, {
      filter: filter,
      skip,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region visit_charge_detail
export const createVisitChargeDetailRecordsByPatientVisitId = async (
  selectedPatientVisitId,
  currentUser,
) => {
  // const [covered_visit_charge_details, uncovered_visit_charge_details] = await Promise.all([
  //   getFullCoveredApiChargeDetailViewsByPatientVisitId(selectedPatientVisitId),
  //   getFullUncoveredApiChargeDetailViewsByPatientVisitId(selectedPatientVisitId)
  // ])
  // const api_charge_details = [
  //   ...covered_visit_charge_details.value,
  //   ...uncovered_visit_charge_details.value
  // ]
  // const data = await getFullApiChargeDetailViewsByPatientVisitId(selectedPatientVisitId)
  // const api_charge_details = data.value
  // const processRecords = api_charge_details.map((record) => ({
  //   patient_visit_id: record.patient_visit_id,
  //   charge_detail_id: record.charge_detail_id,
  //   manual_ss_cover_flag: record?.ss_cover_flag,
  //   lu_user_id: currentUser.User_id,
  //   ...record
  // }))
  // await patchMultiRecordDetails(lists.visit_charge_detail, processRecords)

  await syncVisitDetailChargeService(selectedPatientVisitId, currentUser.User_id)
}

export const getAllVisitChargeDetailsByPatientVisitId = async (
  selectedPatientVisitId,
  skip = '',
) => {
  try {
    let filter = `(patient_visit_id eq ${selectedPatientVisitId})`
    let data = await getItemsService(lists.visit_charge_detail_view, {
      filter: filter,
      orderBy: 'ss_optional_cover_flag desc',
      skip,
      top: 2000,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}
export const addMultiVisitChargeDetails = async (selectedMergedPatientVisitIds, currentUser) => {
  try {
    await Promise.all(
      selectedMergedPatientVisitIds.map(async (id) => {
        await syncVisitDetailChargeService(id, currentUser?.User_id)
      }),
    )
  } catch (error) {
    handleError(error)
  }
}

export const removeMultiVisitChargeDetails = async (currentVisitChargeDetails) => {
  const promises = currentVisitChargeDetails.value.map(
    (item) => () => deleteListItemService(lists.visit_charge_detail, item.visit_charge_detail_id),
  )
  batchPromises(promises)
}

//#region health_insurance_card
const testCardRes = {
  maKetQua: '010',
  ghiChu:
    'Thẻ hết hạn! Họ tên : Huỳnh Tấn Khoa, Ngày sinh: 11/10/1964, Giới tính : Nam! (ĐC: Khu Phố 6, Phường 3, Thành phố Tây Ninh, Tỉnh Tây Ninh; Nơi KCBBĐ: 72009; Hạn thẻ: 14/09/2019 - 13/09/2020; Thời điểm đủ 5 năm liên tục: 01/01/2015).',
  maThe: 'GD472**********',
  hoTen: 'Huỳnh Tấn Khoa',
  ngaySinh: '11/10/1964',
  gioiTinh: 'Nam',
  diaChi: 'Khu Phố 6, Phường 3, Thành phố Tây Ninh, Tỉnh Tây Ninh',
  maDKBD: '72009',
  cqBHXH: 'VP Bảo hiểm Xã hội tỉnh Tây Ninh',
  gtTheTu: '14/09/2019',
  gtTheDen: '13/09/2020',
  maKV: '',
  ngayDu5Nam: '01/01/2015',
  maSoBHXH: '**********',
  maTheCu: '',
  maTheMoi: null,
  gtTheTuMoi: null,
  gtTheDenMoi: null,
  maDKBDMoi: null,
  tenDKBDMoi: null,
  dsLichSuKCB2018: null,
  dsLichSuKT2018: [],
}
export const getPortalInsuranceCards = async ({
  checkedFormListInsuranceCards = [],
  patient = {},
  isCheckByNationId = false,
}) => {
  let updatedInsuranceCards = []
  updatedInsuranceCards = await Promise.all(
    checkedFormListInsuranceCards.map(async (card) => {
      let response = null

      try {
        response = await getPortalInsuranceCardService(
          isCheckByNationId ? patient.Nation_id : card.cardCode,
          patient.fullName,
          patient.dob,
        )

        // response = testCardRes // TEST

        if (response) {
          return {
            ...card,
            ...response,
            initialCardCode: card.cardCode,
          }
        }
      } catch (error) {
        handleError(error)
      }

      return {
        ...card,
        initialCardCode: card.cardCode,
      }
    }),
  )
  return updatedInsuranceCards
}

export const getHealthInsuranceCards = async (selectedPatientId, skip = '', top = 10) => {
  try {
    let filter = `(patient_id eq ${selectedPatientId})`
    let data = await getItemsService(lists.health_insurance_card, {
      filter: '',
      skip,
      top,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getHealthInsuranceCardById = async (health_insurance_card_id, skip = '', top = 1) => {
  try {
    let filter = `(${lists.health_insurance_card.primaryKeyName} eq ${health_insurance_card_id})`
    let data = await getItemsService(lists.health_insurance_card, {
      filter: filter,
      skip,
      top,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getHealthInsuranceCardsByPatientVisitId = async (
  patientVisitId,
  skip = '',
  top = 10,
) => {
  try {
    let filter = `(patient_visit_id eq ${patientVisitId})`
    let data = await getItemsService(lists.health_insurance_card, {
      filter,
      skip,
      top,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getAllHealthInsuranceCardsByPatientId = async (patientId) => {
  try {
    let filter = `(patient_id eq ${patientId})`
    let data = await getItemsService(lists.health_insurance_card, {
      filter,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const clearProcessingPersonForUser = async (userId) => {
  try {
    if (!userId) return

    // Get all visits where this user is the processing person
    const userVisits = await getItemsService(lists.patient_visit_mapping_view, {
      filter: `processing_person_id eq ${userId}`,
      top: 1000,
    }).then((res) => res.value)

    // Clear processing_person for all these visits
    if (userVisits && userVisits.length > 0) {
      const updatePromises = userVisits.map((visit) =>
        updateListItemService(lists.patient_visit, visit.patient_visit_id, {
          processing_person_id: null,
          processing_person_name: null,
        }),
      )

      await Promise.all(updatePromises)
    }
  } catch (error) {
    handleError(error, 'clearProcessingPersonForUser')
  }
}

export const createHealthInsuranceCardRecords = async (rawInsuranceCards) => {
  const updatedRows = rawInsuranceCards

  for (let i = 0; i < rawInsuranceCards.length; i++) {
    const record = rawInsuranceCards[i]

    if (record.health_insurance_card_id == null) {
      const { health_insurance_card_id, ...rest } = record //do not remove health_insurance_card_id
      updatedRows[i] = {
        ...rest,
      }
    } else {
      updatedRows[i] = {
        health_insurance_card_id: record.health_insurance_card_id,
        ...record,
      }
    }
  }

  await patchMultiRecordDetails(lists.health_insurance_card, updatedRows)
}

export const deleteHealthInsuranceCardById = async (health_insurance_card_id) => {
  try {
    await deleteListItemService(lists.health_insurance_card, health_insurance_card_id)
  } catch (error) {
    handleError(error)
  }
}

//#region serious_illness

export const getSeriousIllnesses = async () => {
  try {
    let filter = '(active_flag eq true)'
    let data = await getItemsService(lists.serious_illness, {
      filter: filter,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region system_referral_type_ref
export const getSystemReferralTypeRefs = async () => {
  try {
    let filter = '( active_flag eq true ) '
    let data = await getItemsService(lists.system_referral_type_ref, {
      filter: filter,
      // top
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region system_referral_type_ref
export const getCardTypeRefs = async () => {
  try {
    let filter = '( active_flag eq true ) '
    let data = await getItemsService(lists.card_type_ref, {
      filter: filter,
      orderBy: 'seq_num asc',
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region ethnicity_ref
export const getEthnicityRefs = async (top = 2000) => {
  try {
    let filter = '( active_flag eq true ) '
    let data = await getItemsService(lists.ethnicity_ref, {
      filter: filter,
      orderBy: 'seq_num asc',
      top: top,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region occupation_ref
export const getOccupationRefs = async (top = 2000) => {
  try {
    let filter = '( active_flag eq true and MA_NGHE_NGHIEP ne null ) '
    let data = await getItemsService(lists.occupation_ref, {
      filter: filter,
      orderBy: 'seq_num asc',
      top: top,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region region_nl_view
export const getRegionNlViews = async (top = 2000) => {
  try {
    let filter = '( active_flag eq true ) '
    let data = await getItemsService(lists.region_nl_view, {
      filter: filter,
      orderBy: 'seq_num asc',
      top: top,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region subregion_nl_view
export const getSubregionNlViews = async (top = 2000) => {
  try {
    let filter = '( active_flag eq true ) '
    let data = await getItemsService(lists.subregion_nl_view, {
      filter: filter,
      orderBy: 'seq_num asc',
      top: top,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getSubregionNlViewsByRegionId = async (regionId) => {
  try {
    let filter = `(region_id eq ${regionId}) and (active_flag eq true)`
    let data = await getItemsService(lists.subregion_nl_view, {
      filter: filter,
      orderBy: 'seq_num asc',
      top: 2000,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getSubregionNlViewBySubRegionId = async (subRegionId) => {
  try {
    let data = await getItemService(lists.subregion_nl_view, subRegionId)

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getSubregionNlViewBySubRegionNameAndRegionId = async (subRegionName, regionId) => {
  try {
    let filter = `contains(name_l,'${subRegionName}') and (active_flag eq true) and (region_id eq ${regionId})`
    let data = await getItemsService(lists.subregion_nl_view, { filter: filter, top: 1 })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region ward_subregion_nl_view
export const getWardSubregionNlViews = async (top = 2000) => {
  try {
    let filter = '( active_flag eq true ) '
    let data = await getItemsService(lists.ward_subregion_nl_view, {
      filter: filter,
      top: top,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getWardSubregionNlViewByWardNameAndSubRegionCode = async (
  wardSubRegionName,
  subRegionCode,
) => {
  try {
    let filter = `( contains(name_l,'${wardSubRegionName}') or name1 eq '${wardSubRegionName}' or name2 eq '${wardSubRegionName}' or name3 eq '${wardSubRegionName}' ) and (active_flag eq true) and subregion_display_code eq '${subRegionCode}'`
    let data = await getItemsService(lists.ward_subregion_nl_view, {
      filter: filter,
      top: 1,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region system_referral_disposition_ref_nl_view
export const getSystemReferralDispositionRefs = async (top = 2000) => {
  try {
    let filter = `( active_status eq 'A' )`
    let data = await getItemsService(lists.system_referral_disposition_ref_nl_view, {
      filter: filter,
      top: top,
      orderBy: 'seq_num asc',
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getWardSubregionNlViewsBySubRegionCode = async (subRegionCode) => {
  try {
    let filter = `(subregion_display_code eq '${subRegionCode}') and (active_flag eq true)`
    let data = await getItemsService(lists.ward_subregion_nl_view, {
      filter: filter,
      top: 2000,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region technical_services
export const getTechnicalServicesByMedicalSuppliesId = async (medicalSuppliesId) => {
  if (!medicalSuppliesId || medicalSuppliesId === 'N/A') {
    return
  }

  try {
    let filter = `(medical_supplies_id eq ${
      medicalSuppliesId !== 'N/A' ? medicalSuppliesId : null
    })`
    let data = await getItemsService(lists.ss_item_technical_services_mapping, {
      filter: filter,
    })
    return data
  } catch (error) {
    handleError(error)
  }
}

export const getTechnicalServicesByMedicinesId = async (medicinesId) => {
  try {
    let filter = `(medicine_id eq ${medicinesId})`
    let data = await getItemsService(lists.ss_item_technical_services_mapping, {
      filter: filter,
    })
    return data
  } catch (error) {
    handleError(error)
  }
}

//#region tbl_file_tracking
export const getTblFileTrackingsByMaGiaoDich = async (maGiaoDich, skip = '', top = 1) => {
  try {
    let filter = `(maGiaoDich eq '${maGiaoDich}')`
    let data = await getItemsService(lists.tbl_file_tracking, {
      filter: filter,
      skip,
      top,
    })
    return data
  } catch (error) {
    handleError(error)
  }
}

//#region api_patient_visit_medical_coding_view
export const getApiPatientVisitMedicalCodingViewListByPatientVisitId = async (patientVisitId) => {
  try {
    let filter = `(patient_visit_id eq ${patientVisitId})`
    let data = await getItemsService(lists.api_patient_visit_medical_coding_view, {
      filter: filter,
      count: false,
    })
    return data
  } catch (error) {
    handleError(error)
  }
}

//#region tbl_SS_policy_short_code
export const getAllTblSsPolicyShortCodes = async (top = 2000) => {
  try {
    let filter = ``
    let data = await getItemsService(lists.tbl_SS_policy_short_code, {
      filter: filter,
      top: top,
    })
    // let data = TBL_SS_POLICY_SHORT_CODE
    return data
  } catch (error) {
    handleError(error)
  }
}

//#region tbl_SS_policy_short_code
export const getAllBaseSalary45Months = async (top = 2000) => {
  try {
    let filter = ``
    let data = await getItemsService(lists.base_salary_45_months, {
      filter: filter,
      top: top,
    })
    //let data = BASE_SALARY_45_MONTHS
    return data
  } catch (error) {
    handleError(error)
  }
}

//#region api_patient_visit_policy_ss_view
export const getApiPatientVisitPolicySsViewByPatientVisitId = async (patientVisitId) => {
  try {
    let filter = `patient_visit_id eq ${patientVisitId} and deleted_date_time eq null`
    let data = await getItemsService(lists.api_patient_visit_policy_ss_view, {
      filter: filter,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}
//#region api_patient_visit_policy_ss_view
export const getApiPolicySubscriptionSsViewByCardDateAndPatientId = async (
  patientId,
  effectiveDate,
  expirationDate,
) => {
  try {
    let filter = `policy_subscription_status_rcd eq 'A' and start_date le ${effectiveDate} and ( end_date ge ${expirationDate} or end_date eq null )
    and patient_id eq ${patientId}`
    let data = await getItemsService(lists.api_policy_subscription_ss_view, {
      filter: filter,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

//#region StoreRecord
export const getStoreRecordByName = async (name = 'DocumentStore') => {
  try {
    let filter = `Name eq '${name}' `
    let data = await getItemsService(lists.StoreRecords, {
      filter: filter,
      top: 1,
    })

    return data.value[0]
  } catch (error) {
    handleError(error)
  }
}
