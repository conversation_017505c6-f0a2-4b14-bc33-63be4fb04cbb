import React, { useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import { Table } from 'antd'
import { displayDateTime } from '../../common/helpers'
import { getAllPatientVisitMappingViewsByPatientId } from '../Visit/VisitService'
import { FORMAT_DATE } from '../../common/constant'

const MergeVisitDatasetTable = ({
  currentVisitDatasets,
  currentPatient,
  selectedPatientVisitId,
  setSelectedPatientVisitId,
  filterVisitDate = [],
  isSelectMultiple = false,
  selectedMergedPatientVisitIds = [],
  setSelectedMergedPatientVisitIds,
}) => {
  const [currentPatientVisitMappingViews, setCurrentPatientVisitMappingViews] = useState([])
  const handlegetAllPatientVisitMappingViewsByPatientId = async () => {
    const data = await getAllPatientVisitMappingViewsByPatientId(currentPatient?.Patient_id)
    setCurrentPatientVisitMappingViews(data.value)
    return data
  }

  useEffect(() => {
    //  console.log('run visit dataset table')
    handlegetAllPatientVisitMappingViewsByPatientId()
  }, [])

  useEffect(() => {
    if (currentPatientVisitMappingViews.length > 0) {
      handleDefaultCheckboxVisit()
    }
  }, [currentPatientVisitMappingViews])

  const handleDefaultCheckboxVisit = () => {
    if (isSelectMultiple) {
      setSelectedMergedPatientVisitIds(
        currentVisitDatasets
          .filter(
            (item) =>
              !currentPatientVisitMappingViews.find(
                (mapping) => mapping.patient_visit_id === item.patient_visit_id,
              ),
          )
          .map((item) => item.patient_visit_id),
      )
    } else return
  }

  const rowSelection = {
    type: isSelectMultiple ? 'checkbox' : 'radio',
    selectedRowKeys: isSelectMultiple
      ? selectedMergedPatientVisitIds
      : selectedPatientVisitId
        ? [selectedPatientVisitId]
        : [],
    onChange: (selectedKeys) => {
      if (isSelectMultiple) {
        setSelectedMergedPatientVisitIds(selectedKeys)
      } else {
        setSelectedPatientVisitId(selectedKeys.length > 0 ? selectedKeys[0] : null)
      }
    },
    getCheckboxProps: (record) => ({
      disabled: currentPatientVisitMappingViews.some(
        (mapping) => mapping.patient_visit_id === record.patient_visit_id,
      ),
    }),
  }

  return (
    <div>
      {filterVisitDate?.[0] && filterVisitDate?.[1] && (
        <>
          {filterVisitDate?.[0]?.format(FORMAT_DATE)}-----
          {filterVisitDate?.[1]?.format(FORMAT_DATE)}
        </>
      )}

      <div className="row mt-2">
        <Table
          size="small"
          className="custom-table"
          rowSelection={rowSelection}
          scroll={{ y: 400 }}
          columns={[
            {
              title: 'Mã lượt khám',
              key: 'Visit.No',
              render: (r) => <div>{r?.visit_code}</div>,
            },
            {
              title: 'Loại',
              key: 'VisitType',
              render: (r) => <div>{r.visit_type}</div>,
            },
            {
              title: 'Cơ sở',
              key: 'Facility',
              render: (r) => <div>{r.facility}</div>,
            },
            {
              title: 'Ngày bắt đầu',
              key: 'VistStart',
              align: 'right',
              render: (r) => <div>{displayDateTime(r.visit_start)}</div>,
            },
            {
              title: 'Ngày kết thúc',
              key: 'ClosureDateTime',
              align: 'right',
              render: (r) => <div>{displayDateTime(r.visit_end)}</div>,
            },
          ]}
          dataSource={currentVisitDatasets
            .map((item, index) => ({
              ...item,
              numberOrder: index + 1,
            }))
            .filter((r) => {
              const isDisabled = currentPatientVisitMappingViews.some(
                (mapping) => mapping.patient_visit_id === r.patient_visit_id,
              )

              return !isDisabled
            })}
          rowClassName={(r) =>
            r.patient_visit_id === selectedPatientVisitId ? 'highlight-row' : 'hover-row'
          }
          onRow={(r) => {
            const isDisabled = currentPatientVisitMappingViews.some(
              (mapping) => mapping.patient_visit_id === r.patient_visit_id,
            )

            return isDisabled
              ? {}
              : {
                  onClick: () => {
                    if (isSelectMultiple) {
                      setSelectedMergedPatientVisitIds((prevSelected) => {
                        if (prevSelected.includes(r.patient_visit_id)) {
                          return prevSelected.filter((id) => id !== r.patient_visit_id)
                        } else {
                          return [...prevSelected, r.patient_visit_id]
                        }
                      })
                    } else {
                      setSelectedPatientVisitId(r.patient_visit_id)
                    }
                  },
                  style: { cursor: 'pointer' },
                }
          }}
          pagination={false}
          rowKey="patient_visit_id"
        />
      </div>
    </div>
  )
}
MergeVisitDatasetTable.propTypes = {
  currentVisitDatasets: PropTypes.array,
  currentPatient: PropTypes.object,
  isSelectMultiple: PropTypes.bool,
  selectedPatientVisitId: PropTypes.string,
  setSelectedPatientVisitId: PropTypes.func,
  filterVisitDate: PropTypes.array,
  selectedMergedPatientVisitIds: PropTypes.array,
  setSelectedMergedPatientVisitIds: PropTypes.func,
}

export default MergeVisitDatasetTable
