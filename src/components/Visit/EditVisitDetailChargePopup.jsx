import React, { useEffect, useState } from 'react'
import { Modal, Form, DatePicker, Button, Input, Select } from 'antd'
import dayjs from 'dayjs'
import PropTypes from 'prop-types'
import { FORM_MODE, FORMAT_DATETIME, MODE_VIEW_DATA } from '../../common/constant'
import {
  getTechnicalServicesByMedicalSuppliesId,
  getTechnicalServicesByMedicinesId,
} from './VisitService'
import { displayDateTime, filterOption, handleError } from '../../common/helpers'
import { PP_VO_CAM_OPTIONS } from './ChargeDetail/ChargeDetailConstant'
import SelectFromTemplateText from './SelectFromTemplateText'
import MedicalRecordField from '../MedicalRecord/MedicalRecordField'
import { conclusionOptions } from '../MedicalRecord/MedicalRecordConstant'
import { useAuth } from '../../common/AuthProvider'
import { PERMISSION } from '../Auth/AuthConstant'
import { useDepartmentMapping } from '../../queryHooks/useDepartmentMapping'
import { updateListItemService } from '../../common/services'
import lists from '../../common/lists'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import { useUI } from '../../common/UIProvider'

const EditVisitDetailChargePopup = ({
  visible,
  onClose,
  onSave,
  record,
  visitChargeDetails,
  isOTMSMode = false,
  modeViewData,
  visitTypeGroup = '',
}) => {
  const { checkPermission } = useAuth()
  const { departmentMappings, departmentSS } = useDepartmentMapping()
  const ui = useUI()

  const [form] = Form.useForm()

  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  const [mappedChargeDetails, setMappedChargeDetails] = useState([])
  const [isOpenSelectTextModal, setIsOpenSelectTextModal] = useState(false)

  const handleGetTechnicalServicesIds = async (record) => {
    const validServices = []
    let technicalServices = []

    try {
      if (record.medical_supplies_id) {
        const response = await getTechnicalServicesByMedicalSuppliesId(record.medical_supplies_id)
        technicalServices = response?.value || []
      } else if (record.medicine_id) {
        const response = await getTechnicalServicesByMedicinesId(record.medicine_id)
        technicalServices = response?.value || []
      }

      // Only process if we have technical services
      if (technicalServices.length > 0) {
        for (const charge of visitChargeDetails) {
          const matchingService = technicalServices.find(
            (service) => service.technical_services_id === charge.technical_services_id,
          )
          if (matchingService) {
            validServices.push(charge)
          }
        }
      }

      setMappedChargeDetails(validServices)
    } catch (error) {
      console.error('Error fetching technical services:', error)
      setMappedChargeDetails([])
    }
  }

  useEffect(() => {
    handleGetTechnicalServicesIds(record)
    form.setFieldsValue({
      note: record.note,
      original_charged_date_time: record.original_charged_date_time
        ? dayjs(record.original_charged_date_time)
        : null,
      original_charged_reason: record.original_charged_reason,
      mapping_technical_services_name: record.mapping_technical_services_name,
      NGAY_YL: record.NGAY_YL ? dayjs(record.NGAY_YL) : null,
      NGAY_TH_YL: record.NGAY_TH_YL ? dayjs(record.NGAY_TH_YL) : null,
      NGAY_KQ: record.NGAY_KQ ? dayjs(record.NGAY_KQ) : null,
      KET_LUAN: record.KET_LUAN,
      MO_TA: record.MO_TA,
      PP_VO_CAM: record?.PP_VO_CAM || null,
      MA_KHOA: record.MA_KHOA,
    })
  }, [record, form])

  const handleRowSave = async (updatedRecord, mappedChargeDetails, values) => {
    try {
      let updatedMappingChargeDetails = {}

      if (updatedRecord?.mapping_technical_services_name) {
        const mappedChargeDetail = mappedChargeDetails?.find(
          (item) => item.visit_charge_detail_id === values.mapping_technical_services_name,
        )
        updatedMappingChargeDetails = {
          mapping_technical_services_name:
            mappedChargeDetail?.health_insurance_name || mappedChargeDetail?.item_name_e,
          mapping_technical_services_id: mappedChargeDetail.technical_services_id,
          technical_services_cd_id: mappedChargeDetail.charge_detail_id,
          NGAY_YL: mappedChargeDetail.charged_date_time,
          NGAY_TH_YL: mappedChargeDetail.serviced_date_time || mappedChargeDetail.charged_date_time,
        }
      } else {
        updatedMappingChargeDetails = {
          mapping_technical_services_name: updatedRecord.mapping_technical_services_name,
          mapping_technical_services_id: updatedRecord.mapping_technical_services_id,
          technical_services_cd_id: updatedRecord.technical_services_cd_id,
          NGAY_YL: updatedRecord.NGAY_YL,
          NGAY_TH_YL: updatedRecord.NGAY_TH_YL,
        }
      }

      updatedMappingChargeDetails = {
        ...updatedMappingChargeDetails,
        NGAY_KQ: updatedRecord.NGAY_KQ,
        KET_LUAN: updatedRecord.KET_LUAN,
        MO_TA: updatedRecord.MO_TA,
        PP_VO_CAM: updatedRecord.PP_VO_CAM,
        note: updatedRecord.note,
        MA_KHOA: updatedRecord.MA_KHOA,
      }

      await updateListItemService(
        lists.visit_charge_detail,
        updatedRecord.visit_charge_detail_id,
        updatedMappingChargeDetails,
      )

      ui.notiSuccess('Cập nhật thành công')

      onSave()
    } catch (error) {
      handleError(error)
      ui.notiError('Cập nhật thất bại')
    }
  }

  const handleSave = () => {
    form.validateFields().then(async (values) => {
      const hasChanges = Object.keys(values).some((key) => values[key] !== record[key])
      if (hasChanges) {
        await handleRowSave({ ...record, ...values }, mappedChargeDetails, values)
      } else {
        onClose()
      }
    })
  }

  return (
    <Modal
      open={visible}
      title={`ĐIỀU CHỈNH THÔNG TIN ITEM: ${record.extended_item_code || record.ss_item_code}`}
      onCancel={onClose}
      width={1000}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Huỷ
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          Lưu
        </Button>,
      ]}>
      <Form form={form} layout="vertical">
        <div className="fw-bold">Tên item</div>
        {record.item_name}

        <div className="fw-bold">Tên BHYT</div>
        {record.health_insurance_name}

        <div className="row mt-2">
          <Form.Item name="note" label="Ghi chú">
            <Input disabled={isOTMSMode}></Input>
          </Form.Item>

          <Form.Item name="MA_KHOA" label="MA_KHOA (XML2/XML3)">
            <Select
              allowClear
              showSearch
              filterOption={filterOption}
              optionLabelProp="label"
              disabled={!checkPermission(PERMISSION.SIO_MANAGER)}>
              {departmentSS.map((dept) => (
                <Select.Option
                  key={dept.ma_khoa_bhyt + dept.ten_khoa_bhyt}
                  value={dept.ma_khoa_bhyt}
                  label={dept.ma_khoa_bhyt}>
                  {dept.ma_khoa_bhyt} - {dept.ten_khoa_bhyt}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {modeViewData !== MODE_VIEW_DATA.MS && (
            <>
              <Form.Item
                hidden={!record?.medical_supplies_id}
                name="mapping_technical_services_name"
                label="Tên DVKT cụ thể">
                <Select
                  optionLabelProp="label"
                  onChange={(value, option) => {
                    form.setFieldValue('NGAY_YL', dayjs(option?.item?.charged_date_time))
                    form.setFieldValue(
                      'NGAY_TH_YL',
                      dayjs(option?.item?.serviced_date_time || option?.item?.charged_date_time),
                    )
                  }}>
                  {mappedChargeDetails?.map((detail) => (
                    <Select.Option
                      label={
                        (detail.health_insurance_name || detail.item_name_e) +
                        ' - ' +
                        detail.ss_item_code
                      }
                      key={detail.visit_charge_detail_id}
                      value={detail.visit_charge_detail_id}
                      item={detail}>
                      <div className="whitespace-normal">
                        {detail.health_insurance_name || detail.item_name_e} - {detail.ss_item_code}
                      </div>
                      <div>{displayDateTime(detail.charged_date_time)}</div>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item name="NGAY_YL" label="Ngày ra y lệnh">
                <DatePicker showTime className="w-100" format={FORMAT_DATETIME} />
              </Form.Item>
              <Form.Item name="NGAY_TH_YL" label="Ngày thực hiện y lệnh">
                <DatePicker showTime className="w-100" format={FORMAT_DATETIME} />
              </Form.Item>
              <Form.Item name="NGAY_KQ" label="Ngày có kết quả">
                <DatePicker showTime className="w-100" format={FORMAT_DATETIME} />
              </Form.Item>
              <Form.Item
                // hidden={
                //   !(
                //     record?.technical_services_id &&
                //     (record?.ss_item_group_rcd == '8' || record?.ss_item_group_rcd == '18')
                //   )
                // } // TODO: uncomment when ss_item_group_rcd available
                required={
                  record?.technical_services_id &&
                  (record?.ss_item_group_rcd == '8' || record?.ss_item_group_rcd == '18') &&
                  visitTypeGroup === 'IPD'
                }
                name="PP_VO_CAM"
                label="Phương pháp vô cảm">
                <Select placeholder="Chọn phương pháp vô cảm">
                  {PP_VO_CAM_OPTIONS.map((option) => (
                    <Select.Option key={option.value} value={option.value}>
                      {option.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </>
          )}

          {(modeViewData === MODE_VIEW_DATA.MS || modeViewData === MODE_VIEW_DATA.NORMAL) && (
            <>
              <div>
                <MedicalRecordField
                  form={form}
                  formMode={FORM_MODE.edit}
                  fieldName="KET_LUAN"
                  label="Kết luận"
                  labelBold={false}
                  templateOptions={conclusionOptions}
                  underlineLabel={false}
                />
              </div>

              <Form.Item hidden={!record?.technical_services_id} name="MO_TA" label="Mô tả">
                <Input.TextArea
                  autoSize={{ minRows: 2 }}
                  placeholder="Vui lòng nhập"></Input.TextArea>
              </Form.Item>
            </>
          )}
        </div>
      </Form>
    </Modal>
  )
}

EditVisitDetailChargePopup.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  record: PropTypes.object.isRequired,
  visitChargeDetails: PropTypes.array,
  isOTMSMode: PropTypes.bool,
  modeViewData: PropTypes.string,
}

export default EditVisitDetailChargePopup
