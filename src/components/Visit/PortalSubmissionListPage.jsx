import React, { useEffect, useState } from 'react'
import { Table, Button, Form, Input, DatePicker, Breadcrumb, Drawer } from 'antd'
import { FilterOutlined } from '@ant-design/icons'
import { Link, useNavigate } from 'react-router-dom'
import COLOR from '../../common/color'
import { getSsTable1s } from './VisitService'
import {
  ACTION_VISIT_HISTORY,
  PROCESSING_STATUS,
  TABLE_PORTAL_SUBMISSION_COLUMNS,
} from './VisitConstant'
import { useWatch } from 'antd/es/form/Form'
import { DANH_SACH_LUOT_KHAM_LINK, FORMAT_DATE } from '../../common/constant'
import { handleError } from '../../common/helpers'
import { useUI } from '../../common/UIProvider'
import { checkXML, sendXMLTableToThePortal } from '../Tool/XmlToolService'
import lists from '../../common/lists'
import { updateListItemService } from '../../common/services'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import { usePatientVisitHistory } from './hooks/usePatientVisitHistory'
import { XML_GATE_STATUS } from '../Tool/XmlConstant'
import PortalSubmissionDrawerFilter from './PortalSubmissionDrawerFilter'
import AsyncButton from '../../common/components/AsyncButton'

const { RangePicker } = DatePicker

const PortalSubmissionListPage = () => {
  //hooks
  const ui = useUI()
  const [form] = Form.useForm()
  const filterMALK = useWatch('filterMALK', form, 500)
  const filterVisitDate = useWatch('filterVisitDate', form)
  const navigate = useNavigate()
  // State
  const [loading, setLoading] = useState(false)
  const [portalSubmissionsData, setPortalSubmissionsData] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [selectedRows, setSelectedRows] = useState([])
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])
  const { addPatientVisitHistory } = usePatientVisitHistory()
  const [isDrawerVisible, setIsDrawerVisible] = useState(false)
  const [formDrawer] = Form.useForm()
  const gate_status = useWatch('gate_status', formDrawer)

  const handleGetPortalSubmissionData = async (skip = 0, top = 2000) => {
    setLoading(true)
    let data = []
    let filter = []

    if (filterMALK) {
      filter.push(`MA_LK eq '${filterMALK}'`)
    }
    if (filterVisitDate) {
      filter.push(
        `NGAY_VAO ge ${filterVisitDate[0].format(
          FORMAT_DATE,
        )} and (NGAY_VAO le ${filterVisitDate[1].format(FORMAT_DATE)} or NGAY_VAO eq null)`,
      )
    }
    filter.push(`approve_status eq 'approved'`)
    // Modify the gate_status filter to use the drawer selection
    if (gate_status) {
      filter.push(`gate_status eq '${gate_status}'`)
    } else {
      filter.push(`gate_status eq '${XML_GATE_STATUS.WAITING_SUBMIT.key}'`)
    }
    filter.push(`include eq true`)

    try {
      ui.setLoading(true)
      const response = await getSsTable1s(skip, top, filter.join(' and '))
      data = response
      setPortalSubmissionsData(response.value)
      ui.setLoading(false)
    } catch (error) {
      handleError(error)
    } finally {
      setLoading(false)
    }
    return data
  }

  useEffect(() => {
    handleGetPortalSubmissionData()
  }, [filterMALK, filterVisitDate, gate_status])

  // Handle Sending to Portal
  const handleSendToPortal = async (main_gate) => {
    // validate data
    if (!checkXML(selectedRows, ui)) {
      ui.notiError('Có lỗi xảy ra, vui lòng kiểm tra lại dữ liệu')
      return
    }

    for (const selectedRow of selectedRows) {
      try {
        // update status
        await updateListItemService(lists.ss_table_1, selectedRow?.table_1_id, {
          gate_status: XML_GATE_STATUS.SUBMITTING.key,
        })

        //lưu vào lịch sử
        addPatientVisitHistory.mutateAsync({
          historyData: {
            patient_visit_id: selectedRow?.patient_visit_id__,
            note: `MA_LK: ${selectedRow?.MA_LK}; T_BHTT: ${selectedRow?.T_BHTT}`,
          },
          action: ACTION_VISIT_HISTORY.SUBMIT_XML,
        })

        const res = await sendXMLTableToThePortal(selectedRow?.invoice_no_, ui, main_gate, false)
        if (!res.isSuccess) {
          ui.notiError('Đẩy cổng thất bại', res?.message)
          continue
        }

        await updateListItemService(lists.patient_visit, selectedRow?.patient_visit_id__, {
          processing_status: PROCESSING_STATUS.SENT_TO_GATEWAY.name_e,
        })
      } catch (error) {
        handleError(error)
      }
    }
    // Refresh the data after processing all rows
    await handleGetPortalSubmissionData()
    // Clear selections after processing
    setSelectedRowKeys([])
    setSelectedRows([])
  }

  // Modal Functions
  //   const handleOpenModal = () => setIsModalVisible(true)
  //   const handleCloseModal = () => setIsModalVisible(false)

  return (
    <div className="container-fluid">
      <Form form={form}>
        <div className="row mt-2">
          <Breadcrumb
            items={[
              {
                title: <Link to={DANH_SACH_LUOT_KHAM_LINK}>Danh sách lượt khám</Link>,
              },
              {
                title: 'Danh sách hồ sơ đẩy cổng',
              },
            ]}></Breadcrumb>
        </div>
        <div className="d-flex justify-content-md-start gap-2 mt-2">
          <Form.Item name="filterMALK" label="Mã LK:">
            <Input.Search
              allowClear
              loading={loading}
              onSearch={() => {
                handleGetPortalSubmissionData()
              }}
              placeholder="Nhập mã liên kết"
            />
          </Form.Item>
          <Form.Item name="filterVisitDate" label="Visit Date:">
            <RangePicker />
          </Form.Item>
          <Button
            icon={<i className="fa-solid fa-rotate ms-1"></i>}
            onClick={() => handleGetPortalSubmissionData()}></Button>

          <Form.Item>
            <Button
              icon={<FilterOutlined />}
              type="default"
              onClick={() => setIsDrawerVisible(true)}
            />
          </Form.Item>
        </div>
        <div className="d-flex justify-content-md-end gap-2">
          <Button
            type="primary"
            onClick={() => {
              handleSendToPortal(0)
            }}
            disabled={selectedRows.length === 0}>
            Xác nhận đẩy cổng
          </Button>
          {(currentUser?.User_name == 'khanh5.nguyen' ||
            currentUser?.User_name === 'thu16.nguyen' ||
            currentUser?.User_name == 'powerappsadmin') && (
            <AsyncButton
              type="primary"
              onClick={() => handleSendToPortal(1)}
              disabled={selectedRows.length === 0}>
              Xác nhận đẩy cổng chính
            </AsyncButton>
          )}
        </div>
        <Table
          size="small"
          className="custom-table mt-2"
          scroll={{
            x: TABLE_PORTAL_SUBMISSION_COLUMNS.map((c) => c.width).reduce((a, b) => a + b) + 100,
          }}
          dataSource={portalSubmissionsData.map((item, index) => ({ ...item, key: index }))} // Add key to each item
          columns={[
            ...TABLE_PORTAL_SUBMISSION_COLUMNS,
            {
              title: ' ',
              key: 'action',
              align: 'right',
              fixed: 'right',
              width: 30,
              render: (record) => {
                return (
                  <Link to={`/his/visit/${record.patient_visit_id__}`}>
                    <i style={{ color: COLOR.lime }} className="fas fa-eye"></i>
                  </Link>
                )
              },
            },
          ]}
          rowSelection={{
            type: 'checkbox', // Changed from 'radio' to 'checkbox'
            selectedRowKeys,
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRowKeys(selectedRowKeys)
              setSelectedRows(selectedRows)
            },
          }}
          loading={loading}
          pagination={false}></Table>
      </Form>

      {/* Add the drawer */}
      <Drawer
        title="Bộ lọc nâng cao"
        placement="right"
        width={400}
        onClose={() => setIsDrawerVisible(false)}
        open={isDrawerVisible}>
        <Form layout="vertical" form={formDrawer}>
          <PortalSubmissionDrawerFilter />
          <div>
            <Button
              block
              type="default"
              icon={<i className="fa-solid fa-rotate"></i>}
              onClick={() => {
                formDrawer.resetFields()
                setIsDrawerVisible(false)
              }}>
              Xóa lọc
            </Button>
            <Button
              block
              type="primary"
              icon={<i className="fa-solid fa-arrow-left"></i>}
              className="mt-2"
              onClick={() => {
                setIsDrawerVisible(false)
              }}>
              Xem
            </Button>
          </div>
        </Form>
      </Drawer>
    </div>
  )
}

export default PortalSubmissionListPage
