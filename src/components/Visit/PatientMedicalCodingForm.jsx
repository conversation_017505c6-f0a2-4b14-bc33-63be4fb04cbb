import React, { useEffect, useState } from 'react'
import { Table, Checkbox } from 'antd'
import { addListItemService, updateListItemService } from '../../common/services'
import lists from '../../common/lists'
import { useUI } from '../../common/UIProvider'
import { handleError } from '../../common/helpers'
import PropTypes from '../../common/PropTypes'
import { usePatientMedicalCoding } from './hooks/usePatientMedicalCoding'
import AsyncButton from '../../common/components/AsyncButton'
import { MODE_VIEW_DATA } from '../../common/constant'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import { PROCESSING_STATUS } from './VisitConstant'

const PatientMedicalCodingForm = ({
  onClose,
  patientVisitId, // selectedVisit
  mainVisit,
}) => {
  const ui = useUI()

  const { modeViewData } = useSelector((state) => state[MODULE_AUTH])

  const { refetchAll, patientMedicalCodingList, patientMedicalCodingMappingList } =
    usePatientMedicalCoding(patientVisitId)
  const [customPrimaryFlagId, setCustomPrimaryFlagId] = useState()

  useEffect(() => {
    setCustomPrimaryFlagId(
      patientMedicalCodingList.find((item) => item.primary_flag)?.patient_medical_coding_id,
    )
  }, [patientMedicalCodingList])

  const handleSubmit = async (newPrimaryFlagId) => {
    try {
      ui.setLoading(true)

      // Update old primary to false
      const secondaryList = patientMedicalCodingList.filter(
        (item) => item.patient_medical_coding_id !== newPrimaryFlagId,
      )
      await Promise.all(
        secondaryList.map((item) =>
          handleUpdateCustomPrimaryFlag(item.patient_medical_coding_id, false),
        ),
      )

      // Update new primary
      await handleUpdateCustomPrimaryFlag(newPrimaryFlagId, true)

      ui.notiSuccess('Cập nhật mã bệnh chính thành công')

      await refetchAll()
      onClose()
    } catch (error) {
      handleError(error, 'PatientMedicalCodingForm>handleSubmit')
      ui.notiError('Cập nhật mã bệnh chính thất bại')
    } finally {
      ui.setLoading(false)
    }
  }

  const handleUpdateCustomPrimaryFlag = async (patient_medical_coding_id, flag) => {
    const existingMapping = patientMedicalCodingMappingList.find(
      (mapping) => mapping.patient_medical_coding_id === patient_medical_coding_id,
    )

    if (existingMapping) {
      await updateListItemService(
        lists.patient_medical_coding_mapping,
        existingMapping.patient_medical_coding_id,
        {
          patient_visit_id: patientVisitId,
          active_flag: true,
          custom_primary_flag: flag,
        },
      )
    } else {
      await addListItemService(lists.patient_medical_coding_mapping, {
        patient_medical_coding_id: patient_medical_coding_id,
        patient_visit_id: patientVisitId,
        active_flag: true,
        custom_primary_flag: flag,
      })
    }
  }

  const columns = [
    {
      title: 'Mã bệnh',
      key: 'ma_benh',
      render: (_, r) => r.MA_BENH_CHINH || r.MA_BENH_KT,
    },
    {
      title: 'Tên bệnh',
      key: 'ten_benh',
      render: (_, r) => r.TEN_BENH_CHINH || r.TEN_BENH_KT,
    },
    {
      title: 'Mã bệnh chính (Tùy chỉnh)',
      key: 'primary_flag',
      align: 'center',
      render: (_, r) => {
        const isPrimaryFlag = customPrimaryFlagId === r.patient_medical_coding_id
        return (
          <div>
            <Checkbox
              disabled={![MODE_VIEW_DATA.NORMAL].includes(modeViewData)}
              checked={isPrimaryFlag}
              onChange={() => setCustomPrimaryFlagId(r.patient_medical_coding_id)}
            />
          </div>
        )
      },
    },
    {
      title: 'Bệnh hiểm nghèo',
      key: 'serious_illness_flag',
      align: 'center',
      render: (flag) => <Checkbox className="checkbox-warning" checked={flag} />,
    },
  ]

  return (
    <div>
      <Table
        columns={columns}
        dataSource={patientMedicalCodingList}
        rowKey="patient_medical_coding_id"
        pagination={false}
      />
      <div className="mt-3 d-flex justify-content-end gap-2">
        <AsyncButton
          disabled={
            ![MODE_VIEW_DATA.NORMAL].includes(modeViewData) ||
            mainVisit?.processing_status === PROCESSING_STATUS.SENT_TO_GATEWAY.name_e
          }
          onClick={() => handleSubmit(customPrimaryFlagId)}
          type="primary"
          icon={<i className="fa fa-save" />}>
          Lưu
        </AsyncButton>
      </div>
    </div>
  )
}

const propTypes = {
  onClose: PropTypes.func,
  patientVisitId: PropTypes.string,
  mainVisit: PropTypes.object,
}

PatientMedicalCodingForm.propTypes = propTypes

export default PatientMedicalCodingForm
