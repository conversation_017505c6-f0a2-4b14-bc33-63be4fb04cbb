import { <PERSON><PERSON>, Popover, Table } from 'antd'
import ResizableTitle from '../../common/components/ResizableTitle'
import { handleError, isNotToday, makeResizableColumns } from '../../common/helpers'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import { BUTTON_FONT_WEIGHT } from '../../common/constant'
import { useEffect, useState } from 'react'
import { PROCESSING_STATUS, TABLE_COVERED_CHARGE_DETAILS_OTMS_COLUMNS } from './VisitConstant'
import COLOR from '../../common/color'
import { useUI } from '../../common/UIProvider'
import PropTypes from '../../common/PropTypes'
import EditVisitDetailChargePopup from './EditVisitDetailChargePopup'
import lists from '../../common/lists'
import { updateListItemService } from '../../common/services'
import { useVisitChargeDetails } from './hooks/useVisitChargeDetails'
import { PERMISSION } from '../Auth/AuthConstant'
import { useAuth } from '../../common/AuthProvider'
import { useSearchParams } from 'react-router-dom'
import { groupChargeDetailsByItemGroup } from './VisitHelpers'
import useDeepCompareEffect from 'use-deep-compare-effect'
import dayjs from '../../common/dayjs'

const propTypes = {
  selectedVisit: PropTypes.object,
  mainVisit: PropTypes.object,
}

const OTMSItemChargePage = ({ selectedVisit, mainVisit }) => {
  const [searchParams] = useSearchParams()
  const { modeViewData } = useSelector((state) => state[MODULE_AUTH])

  const selectedTabKey = searchParams.get('selectedTabKey')

  const {
    data: visitChargeDetailsData,
    isError,
    refetchData,
    isLoading,
  } = useVisitChargeDetails(selectedVisit)

  const [coveredVisitChargeDetails, setCoveredVisitChargeDetails] = useState([])
  const [coveredChargeCols, setCoveredChargeCols] = useState([
    ...TABLE_COVERED_CHARGE_DETAILS_OTMS_COLUMNS,
  ])
  const [editingRecord, setEditingRecord] = useState(null)
  const [isPopupVisible, setIsPopupVisible] = useState(false)
  const [expandedRowKeys, setExpandedRowKeys] = useState([])
  const ui = useUI()
  const { checkPermission } = useAuth()

  useEffect(() => {
    if (isError) {
      ui.notiError('Failed to fetch charge details')
    }
  }, [isError])

  useDeepCompareEffect(() => {
    if (!visitChargeDetailsData || !visitChargeDetailsData.length) {
      // Clear the state when data is empty
      setCoveredVisitChargeDetails([])

      return
    }

    const coveredVisitChargeDetailItems = visitChargeDetailsData.filter(
      (item) => item.manual_ss_cover_flag == true,
    )
    const groupedData = groupChargeDetailsByItemGroup(coveredVisitChargeDetailItems, true)
    setCoveredVisitChargeDetails(groupedData)
  }, [visitChargeDetailsData])

  const handleRowEdit = (record) => {
    setEditingRecord(record)
    setIsPopupVisible(true)
  }

  const handleClose = () => {
    setIsPopupVisible(false)
  }

  const expandAllRows = () => {
    const allParentKeys = coveredVisitChargeDetails
      .filter((record) => record.children?.length > 0)
      .map((record) => record.key)
    setExpandedRowKeys(allParentKeys)
  }

  const collapseAllRows = () => {
    setExpandedRowKeys([])
  }

  useEffect(() => {
    expandAllRows()
  }, [coveredVisitChargeDetails])

  return (
    <div className="container-fluid">
      <div className="d-flex justify-content-between align-items-center mt-2">
        <div className="d-flex align-items-center gap-2">
          <Button size="small" icon={<i className="fa-solid fa-plus" />} onClick={expandAllRows} />
          <Button
            size="small"
            icon={<i className="fa-solid fa-minus" />}
            onClick={collapseAllRows}
          />
          <div className="fw-bold" style={{ color: COLOR.cyan, fontWeight: BUTTON_FONT_WEIGHT }}>
            Danh sách item nằm trong Bảo hiểm
          </div>
        </div>
        <Button size="small" onClick={refetchData} icon={<i className="fa-solid fa-sync-alt" />}>
          Làm mới
        </Button>
      </div>
      <Table
        size="small"
        bordered
        className="custom-table mt-2"
        expandable={{
          expandRowByClick: true,
          expandedRowKeys: expandedRowKeys,
          onExpandedRowsChange: setExpandedRowKeys,
        }}
        scroll={{
          x: coveredChargeCols.map((c) => c.width).reduce((a, b) => a + b) + 100,
        }}
        rowClassName={(record) => {
          if (record?.children) {
            return 'highlight-row-gray'
          }

          if (record.serviced_date_time === null) {
            return 'highlight-row-blue'
          }

          if (record?.ss_item_group_rcd == '2' && !record?.KET_LUAN) {
            return 'highlight-row-red'
          }

          return ''
        }}
        loading={isLoading}
        components={{
          header: {
            cell: ResizableTitle,
          },
        }}
        columns={[
          ...makeResizableColumns(
            coveredChargeCols,
            setCoveredChargeCols,
            coveredVisitChargeDetails,
          ),
          {
            title: ' ',
            dataIndex: 'operation',
            key: 'operation',
            width: 50,
            fixed: 'right',
            align: 'right',
            render: (_, record) => {
              const disabled =
                (![
                  PROCESSING_STATUS.WAITING_MS.name_e,
                  PROCESSING_STATUS.WAITING_HSBA.name_e,
                ].includes(mainVisit?.processing_status) ||
                  isNotToday(selectedVisit?.actual_visit_datetime)) &&
                !checkPermission(PERMISSION.SIO_MANAGER)

              return record.visit_charge_detail_id ? (
                <Popover
                  content={
                    disabled
                      ? `Chỉ được chỉnh sửa khi ${PROCESSING_STATUS.WAITING_MS.name_e} hoặc ${PROCESSING_STATUS.WAITING_HSBA.name_e}, và lượt khám ngày hôm nay`
                      : ''
                  }>
                  <Button
                    disabled={disabled}
                    size="small"
                    onClick={() => handleRowEdit(record)}
                    icon={<i className="fa-solid fa-pen-to-square" />}
                  />
                </Popover>
              ) : null
            },
          },
        ]}
        dataSource={coveredVisitChargeDetails.sort((a, b) => parseFloat(a.key) - parseFloat(b.key))}
        locale={{
          emptyText: 'Không có dữ liệu',
        }}
      />

      {isPopupVisible && (
        <EditVisitDetailChargePopup
          visible={isPopupVisible}
          record={editingRecord}
          onSave={async () => {
            await refetchData()
            setIsPopupVisible(false)
          }}
          onClose={handleClose}
          modeViewData={modeViewData}
          visitTypeGroup={selectedVisit?.visit_type_group_rcd}
        />
      )}
    </div>
  )
}

OTMSItemChargePage.propTypes = propTypes

export default OTMSItemChargePage
