import React from 'react'

const SelectFromTemplateText = ({ options, onSelect }) => {
  return (
    <div className="container-fluid">
      {options.map((option) => (
        <div
          key={option.sqNumber}
          onClick={() => onSelect(option.nameL)}
          className="cursor-pointer mb-2 shadow-sm rounded-sm">
          <div className="p-2">Mẫu {option.sqNumber}:</div>

          <div className="p-2">
            <pre>{option.nameL}</pre>
          </div>
        </div>
      ))}
    </div>
  )
}

export default SelectFromTemplateText
