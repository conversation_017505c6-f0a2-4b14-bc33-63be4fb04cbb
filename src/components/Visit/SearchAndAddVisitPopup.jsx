import { Checkbox, Form, Image, Input, Select, DatePicker, Radio, Tag, Button } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'

import { useWatch } from 'antd/es/form/Form'

import imgPatient from '../../assets/patient-icon.png'
import PropTypes from '../../common/PropTypes'
import AsyncButton from '../../common/components/AsyncButton'

import { MODULE_AUTH } from '../../store/auth'
import { useDispatch, useSelector } from 'react-redux'
import { validateForm } from '../../SI/helper'
import { displayDate, handleError, handleSetDateValue } from '../../common/helpers'
import { useUI } from '../../common/UIProvider'
import {
  addListItemService,
  getItemService,
  getItemsService,
  updateListItemService,
} from '../../common/services'
import lists from '../../common/lists'
import {
  createHealthInsuranceCardRecords,
  createVisitChargeDetailRecordsByPatientVisitId,
  getCardTypeRefs,
  getEthnicityRefs,
  getHealthInsuranceCardsByPatientVisitId,
  getOccupationRefs,
  getPatientDatasetByFilterEndWithHN,
  getPatientVisitByPatientId,
  getPatientVisitById,
  getPatientVisitsByPatientVisitId,
  getRegionNlViews,
  getSeriousIllnessPrescriptionDiagnosisByIcd10id,
  getSystemReferralDispositionRefs,
  getSystemReferralTypeRefs,
  getStoreRecordByName,
} from '../Visit/VisitService'
import COLOR from '../../common/color'
import {
  ACTION_VISIT_HISTORY,
  PROCESSING_STATUS,
  REFERRAL_TYPES,
  VALID_INSURANCE_VALID_CODES,
} from './VisitConstant'

import DocumentStore from '../../common/components/DocumentStore/DocumentStore'
import DynamicInsuranceCardList from './DynamicInsuranceCardList'
import {
  isRightChannelReferral,
  mapSelectedFormInsuranceCardsToHealthInsuranceCards,
  processPatientVisitData,
} from './VisitHelpers'
import { FORMAT_DATE_DMY } from '../../common/constant'
import { MODULE_VISIT, visitActions } from '../../store/Visit'
import MergeVisitDatasetTable from './MergeVisitDatasetTable'
import { isEmpty } from 'lodash'
import LazySelect from '../../common/components/LazySelect'
import endpoints from '../../common/endpoints'
import dayjs from '../../common/dayjs'
import { usePatientVisit, VISIT_QUERY_KEYS } from './hooks/usePatientVisit'
import { useQueryClient } from '@tanstack/react-query'
import PatientInfoSection from '../Patient/PatientInfoSection'

const propTypes = {
  onBack: PropTypes.func,
  onSave: PropTypes.func,
  formMode: PropTypes.oneOf(['edit', 'add']),
  initialSelectedPatientVisitId: PropTypes.string,
  initialCurrentPatient: PropTypes.object,
  visible_patient_id: PropTypes.string,
}

const { RangePicker } = DatePicker

const { TextArea } = Input

const SearchAndAddVisitPopup = ({
  onBack,
  onSave,
  formMode,
  initialSelectedPatientVisitId,
  initialCurrentPatient,
}) => {
  // hooks
  const [form] = Form.useForm()
  const ui = useUI()
  const dispatch = useDispatch()
  const {
    data: { currentPatientVisit, currentPatientDataset },
    refetchAll: refetchAllPatientVisit,
  } = usePatientVisit(initialSelectedPatientVisitId)
  //state
  const [isLoading, setIsLoading] = useState(false)
  const [currentVisitDatasets, setCurrentVisitDatasets] = useState([])
  const [currentPatient, setCurrentPatient] = useState({})
  const [selectedPatientVisitId, setSelectedPatientVisitId] = useState(null)
  const [selectedSeriousIllness, setSelectedSeriousIllness] = useState({})
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])
  const [selectedFormListInsuranceCards, setSelectedFormListInsuranceCards] = useState([])
  const [systemReferralTypeRefs, setSystemReferralTypeRefs] = useState([])
  const [selectedSystemReferralTypeRef, setSelectedSystemReferralTypeRef] = useState(null)
  const [cardTypeRefs, setCardTypeRefs] = useState([])
  const [ethnicityRefs, setEthnicityRefs] = useState([])
  const [occupationRefs, setOccupationRefs] = useState([])
  const [regionNlViews, setRegionNlViews] = useState([])
  const [isVisiblePatientVisitInfo, setIsVisiblePatientVisitInfo] = useState(false)

  const [patientVisitAttachments, setPatientVisitAttachments] = useState([])
  const [selectedEthnicityRef, setSelectedEthnicityRef] = useState({})
  const [defaultPatientVisit, setDefaultPatientVisit] = useState(null)
  const [documentStoreId, setDocumentStoreId] = useState(null)

  //store
  const { systemReferralDispositionRefList } = useSelector((state) => state[MODULE_VISIT])

  const filterHN = useWatch('filterHN', form)
  const checkedSeriousIllness = useWatch('seriousIllnessFlag', form)
  const referralTypeRcd = useWatch('referralType', form)
  const checkedFreeCopay = useWatch('freeCopayFlag', form)
  const referralDestination = useWatch(['referralDestination'], form)
  const insuranceCards = useWatch(['insuranceCards'], form)

  const isTransitInformationRequire = useMemo(
    () => referralTypeRcd === REFERRAL_TYPES.RIGHT_CHANNEL_WITH_TRANSFER_CERTIFICATE,
    [referralTypeRcd],
  )

  // const [selectedPatientId, setSelectedPatientId] = useState(null)

  const handleGetPatientDatasetsByHN = async (withLoading = true) => {
    withLoading && setIsLoading(true)
    try {
      const patients = await getPatientDatasetByFilterEndWithHN(filterHN)
      setCurrentPatient(patients.value[0])
      if (!patients.value[0]) {
        ui.notiError('Không tìm thấy bệnh nhân')
        setIsLoading(false)
        return
      }

      setIsLoading(false)
    } catch (error) {
      ui.notiError('An error occurred while fetching patient or visits.')
      handleError(error)
    }
  }

  const handleSearchPatientDatasetsByHN = async () => {
    const isValid = await validateForm(form, ['filterHN'], ui) // Wait for validation and get result
    if (!isValid) return // Stop further execution if validation failed
    setIsLoading(true)

    await handleGetPatientDatasetsByHN()
  }

  const handleSubmit = async () => {
    const isValid = await validateForm(form, [], ui) // Wait for validation and get result
    if (!isValid) return // Stop further execution if validation failed
    let data = []
    data = await getPatientVisitsByPatientVisitId(selectedPatientVisitId)
    if (data.value.length > 0 && formMode === 'add') {
      ui.notiWarning('Hệ thống đã tồn tại patient_visit_id mã: ' + selectedPatientVisitId)
      return
    }

    // Check if any insurance card has an invalid code
    const invalidCard = insuranceCards.find(
      (card) => !!card?.maKetQua && !VALID_INSURANCE_VALID_CODES.includes(card?.maKetQua),
    )
    if (invalidCard?.cardCode) {
      ui.notiWarning(`Lỗi thẻ BHYT ! ${invalidCard?.cardCheckDetails}.`)
      return
    }

    try {
      setIsLoading(true)
      ui.setLoading(true)

      const formValues = form.getFieldsValue()
      const processPatientVisit = processPatientVisitData(
        formValues,
        systemReferralTypeRefs,
        currentUser,
        currentPatient,
        selectedPatientVisitId,
        selectedSeriousIllness,
      )

      onBack()
      if (formMode === 'add') {
        ui.notiInfo('Đang tạo lượt khám, vui lòng đợi')

        await addListItemService(lists.patient_visit, {
          ...processPatientVisit,
          processing_status: PROCESSING_STATUS.WAITING_BHYT.name_e,
          ss_confirmation_flag: true,
        })

        // Add history for creating new visit
        await addListItemService(lists.patient_visit_history, {
          patient_visit_id: selectedPatientVisitId,
          action: ACTION_VISIT_HISTORY.CREATE_VISIT,
          performed_date_time: dayjs(),
          performed_by: currentUser?.User_id,
          note: processPatientVisit.note,
        })

        await createVisitChargeDetailRecordsByPatientVisitId(selectedPatientVisitId, currentUser)
      } else {
        await addListItemService(lists.patient_visit_history, {
          patient_visit_id: selectedPatientVisitId,
          action: ACTION_VISIT_HISTORY.SAVE_INFO,
          performed_date_time: dayjs(),
          performed_by: currentUser?.User_id,
          note: processPatientVisit.note,
        })

        const newProcessingStatus =
          !currentPatientVisit.processing_status ||
          currentPatientVisit.processing_status === PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e
            ? PROCESSING_STATUS.WAITING_BHYT.name_e
            : currentPatientVisit.processing_status

        await updateListItemService(lists.patient_visit, selectedPatientVisitId, {
          ...processPatientVisit,
          processing_status: newProcessingStatus,
          ss_confirmation_flag: true,
        })
      }

      if (form.getFieldValue('insuranceCards').length > 0) {
        const rawInsuranceCards = mapSelectedFormInsuranceCardsToHealthInsuranceCards(
          form.getFieldValue('insuranceCards'),
          selectedPatientVisitId,
          currentUser,
          systemReferralDispositionRefList,
          currentPatient,
        )
        await createHealthInsuranceCardRecords(rawInsuranceCards)
      }

      setIsLoading(false)
      onSave()

      refetchAllPatientVisit()

      ui.notiSuccess('Success')
    } catch (error) {
      handleError(error)
      ui.notiError(handleError(error))
    } finally {
      ui.setLoading(false)
      setIsLoading(false)
    }
  }

  const handleGetAllVisitDatasetsByCurrentPatientId = async (
    skip = '',
    top = 2000,
    count = false,
  ) => {
    try {
      const data = await getItemsService(lists.visit_dataset, {
        filter: `patient_id eq ${currentPatient?.Patient_id}`,
        skip,
        top,
        count,
        orderBy: 'visit_start desc',
      })
      setCurrentVisitDatasets(data.value)
      setIsVisiblePatientVisitInfo(true)
      return data
    } catch (error) {
      handleError(error)
    }
  }

  const handleGetDetaultPatientVisit = async () => {
    if (initialSelectedPatientVisitId) {
      return
    }

    const defaultPatientVisit = await getPatientVisitByPatientId(currentPatient?.Patient_id).then(
      (res) => res.value[0],
    )

    setDefaultPatientVisit(defaultPatientVisit)
    handleDefaultRegistrationValues(defaultPatientVisit)
  }

  const handleDefaultRegistrationValues = (defaultPatientVisit) => {
    form.setFieldsValue({
      ethnicity: defaultPatientVisit?.ethnicity_id,
      occupation: defaultPatientVisit?.occupation_id,
      signedHealthInsuranceConsentFormFlag:
        defaultPatientVisit?.signed_health_insurance_consent_form_flag,
    })

    // Check if the current patient is from Vietnam and set ethnicity to "Kinh-1"
    if (currentPatient?.Nationality === 'Vietnam') {
      const kinhEthnicity = ethnicityRefs.find((ref) => ref.MA_DAN_TOC == '1')
      if (kinhEthnicity) {
        form.setFieldsValue({ ethnicity: kinhEthnicity.ethnicity_id })
        setSelectedEthnicityRef(kinhEthnicity)
      }
    }

    // Check if occupation_id is missing and set default occupation
    if (!defaultPatientVisit?.occupation_id) {
      const defaultOccupation = occupationRefs.find((ref) => ref.MA_NGHE_NGHIEP === '00000')
      if (defaultOccupation) {
        form.setFieldsValue({ occupation: defaultOccupation.occupation_id })
      }
    }

    // Set additional fields if referral type matches
    if (
      defaultPatientVisit?.referral_type_rcd ===
      REFERRAL_TYPES.RIGHT_CHANNEL_WITH_TRANSFER_CERTIFICATE
    ) {
      form.setFieldsValue({
        referralType: defaultPatientVisit?.referral_type_rcd || null,
        referralDestination: defaultPatientVisit?.referral_disposition_rcd || null,
        referralCode: defaultPatientVisit?.referral_code || '',
        medicalRecordCode: defaultPatientVisit?.medical_record_code || '',
        referralReason: defaultPatientVisit?.referral_reason || '',
        referralDate: handleSetDateValue(defaultPatientVisit?.referral_date) || null,
      })
    }
  }

  useEffect(() => {
    if (currentPatient?.Patient_id) {
      handleGetAllVisitDatasetsByCurrentPatientId()
      handleGetDetaultPatientVisit()
    }
  }, [currentPatient])

  // const handleSync

  //chay nen
  const getInitData = async () => {
    if (formMode === 'edit') {
      const patientVisit = await getPatientVisitById(initialSelectedPatientVisitId)

      const seriousIllness = await getSeriousIllnessPrescriptionDiagnosisByIcd10id(
        patientVisit?.serious_illness_icd10id,
      ).then((res) => res.value[0])
      setSelectedSeriousIllness(seriousIllness)

      setSelectedPatientVisitId(patientVisit?.patient_visit_id)

      if (patientVisit) {
        setIsVisiblePatientVisitInfo(true)
        form.setFieldsValue({
          ethnicity: patientVisit?.ethnicity_id,
          cardType: patientVisit?.card_type_id,
          designatedIllnessReason: patientVisit?.designated_illness_reason,
          emergencyCaseFlag: patientVisit?.emergency_case_flag,
          freeCopayFlag: patientVisit?.free_copay_flag,
          medicalRecordCode: patientVisit?.medical_record_code,
          note: patientVisit?.note,
          occupation: patientVisit?.occupation_id,
          referralCode: patientVisit?.referral_code,
          referralDate: handleSetDateValue(patientVisit?.referral_date),
          referralDestination: patientVisit?.referral_disposition_rcd,
          referralReason: patientVisit?.referral_reason,
          referralType: patientVisit?.referral_type_rcd,
          freeCopayDate: [
            handleSetDateValue(patientVisit?.free_copay_end_date),
            handleSetDateValue(patientVisit?.free_copay_end_date),
          ],
          seriousIllness: patientVisit?.serious_illness_icd10id,
          seriousIllnessFlag: patientVisit?.serious_illness_flag,
          signedHealthInsuranceConsentFormFlag:
            patientVisit?.signed_health_insurance_consent_form_flag,
        })
      }

      await getHealthInsuranceCardsByPatientVisitId(patientVisit?.patient_visit_id)

      if (initialCurrentPatient) {
        setCurrentPatient(initialCurrentPatient)
      } else {
        const patient = await getItemService(lists.patient_dataset, patientVisit?.patient_id)
        setCurrentPatient(patient)
      }
    }
    //form.setFieldValue('occupation', '74207327-00e0-4e1d-89a9-a16f6286c132')
  }

  const handleGetSystemReferralTypeRefs = async () => {
    const data = await getSystemReferralTypeRefs()
    setSystemReferralTypeRefs(data.value)
    return data
  }

  const handleGetCardTypeRefs = async () => {
    const data = await getCardTypeRefs()
    setCardTypeRefs(data.value)
    return data
  }

  const handleGetEthinicityRefs = async () => {
    const data = await getEthnicityRefs()
    setEthnicityRefs(
      data.value.map((item) => ({
        ...item,
        name_l_with_code: item?.name_l + '  -  ' + item?.MA_DAN_TOC,
      })),
    )
    return data
  }

  const handleGetOccupationRefs = async () => {
    const data = await getOccupationRefs()
    setOccupationRefs(
      data.value.map((item) => ({
        ...item,
        name_l_with_code: item?.name_l + '  -  ' + item?.MA_NGHE_NGHIEP,
      })),
    )
    return data
  }

  const handleGetRegionNlViews = async () => {
    const data = await getRegionNlViews()
    setRegionNlViews(data.value)
    return data
  }

  const handleGetSystemReferralDispositionRefs = async () => {
    const data = await getSystemReferralDispositionRefs()
    dispatch(visitActions.setSystemReferralDispositionRefList(data.value))

    return data
  }

  useEffect(() => {
    getInitData()
    // handleGetSeriousIllness()
    handleGetSystemReferralTypeRefs()
    handleGetCardTypeRefs()
    handleGetEthinicityRefs()
    handleGetOccupationRefs()
    handleGetRegionNlViews()

    handleGetSystemReferralDispositionRefs()
  }, [])

  useEffect(() => {
    const FV_REGISTER_PLACE = 'T0031'
    const isFVRegisterPlaceInsuranceCard = insuranceCards?.some(
      (card) => card?.registerPlace === FV_REGISTER_PLACE,
    )
    if (isFVRegisterPlaceInsuranceCard) {
      const selectedSystemReferralTypeRef = systemReferralTypeRefs.find(
        (ref) => ref.referral_type_rcd === REFERRAL_TYPES.FV_REGISTER,
      )

      setSelectedSystemReferralTypeRef(selectedSystemReferralTypeRef)
      form.setFieldsValue({ referralType: REFERRAL_TYPES.FV_REGISTER })
    }
  }, [insuranceCards])

  const handleChangeReferralType = (value) => {
    const selectedSystemReferralTypeRef = systemReferralTypeRefs.find(
      (ref) => ref.referral_type_rcd === value,
    )

    setSelectedSystemReferralTypeRef(selectedSystemReferralTypeRef)
  }

  const handleFillTransitInformation = async () => {
    try {
      let lastPatientVisitWithRef = await getItemsService(lists.patient_visit_mapping_view, {
        filter: `patient_id eq ${currentPatient?.Patient_id} and referral_code ne null`,
        top: 1,
        orderBy: 'lu_updated desc',
      })
      lastPatientVisitWithRef = lastPatientVisitWithRef.value[0]

      if (!lastPatientVisitWithRef) {
        ui.message.warning('Không tìm thấy thông tin chuyển tuyến từ lượt khám gần nhất')
        return
      }

      form.setFieldsValue({
        referralDestination: lastPatientVisitWithRef?.referral_disposition_rcd || null,
        referralCode: lastPatientVisitWithRef?.referral_code || '',
        medicalRecordCode: lastPatientVisitWithRef?.medical_record_code || '',
        referralReason: lastPatientVisitWithRef?.referral_reason || '',
        referralDate: handleSetDateValue(lastPatientVisitWithRef?.referral_date) || null,
      })

      ui.message.success('Đã điền thông tin chuyển tuyến từ lượt khám gần nhất')
      return
    } catch (error) {
      ui.message.error('Unexpected error')
      handleError(error, 'handleFillTransitInformation')
    }
  }

  // Add this useEffect to fetch the DocumentStore ID when the component mounts
  useEffect(() => {
    const fetchDocumentStoreId = async () => {
      try {
        const storeRecord = await getStoreRecordByName('DocumentStore')
        if (storeRecord?.Id) {
          setDocumentStoreId(storeRecord.Id)
        }
      } catch (error) {
        handleError(error, 'fetchDocumentStoreId')
      }
    }

    fetchDocumentStoreId()
  }, [])

  return (
    <div className="container-fluid">
      {/* <Button
        onClick={() => {
          const invalidCard = insuranceCards.find(
            (card) => !VALID_INSURANCE_VALID_CODES.includes(card?.maKetQua),
          )
          if (invalidCard) {
            ui.notiWarning(`Lỗi thẻ BHYT ! ${invalidCard?.cardCheckDetails}.`)
            return
          }
        }}></Button> */}
      <Form form={form} scrollToFirstError>
        {formMode === 'add' && (
          <div className="row mt-2">
            <div className=" d-flex justify-content-md-start gap-3">
              <Form.Item
                label="HN"
                name="filterHN"
                rules={[{ required: true, message: 'Vui lòng nhập HN' }]}>
                <Input.Search
                  placeholder="Nhập mã HN"
                  allowClear
                  enterButton="Search"
                  loading={isLoading}
                  onSearch={() => {
                    handleSearchPatientDatasetsByHN()
                  }}
                  //  onChange={(e) => setSearchEmployee(e.target.value)}
                />
              </Form.Item>
            </div>
          </div>
        )}
        {/* User Info */}
        {currentPatient?.HN && (
          <PatientInfoSection
            currentPatient={currentPatient}
            visitCode={
              currentVisitDatasets?.find(
                (cvisit) => cvisit.patient_visit_id === selectedPatientVisitId,
              )?.visit_code
            }
          />
        )}

        {/* DANH SACH LUOT KHAM - THONG TIN DANG KY - DANH SACH THE BHYT */}
        {isVisiblePatientVisitInfo && (
          <div>
            <div>
              {formMode === 'add' && (
                <div>
                  <div className="mt-2 fw-bold" style={{ color: COLOR.cyan }}>
                    Danh sách lượt khám
                  </div>
                  <div className="row mt-2">
                    <MergeVisitDatasetTable
                      isSelectMultiple={false}
                      currentVisitDatasets={currentVisitDatasets}
                      selectedPatientVisitId={selectedPatientVisitId}
                      setSelectedPatientVisitId={setSelectedPatientVisitId}
                      currentPatient={currentPatient}></MergeVisitDatasetTable>
                  </div>
                </div>
              )}
            </div>
            <div>
              <div className="fw-bold" style={{ color: COLOR.cyan }}>
                Danh sách thẻ bảo hiểm y tế
              </div>

              <DynamicInsuranceCardList
                currentPatient={currentPatient}
                form={form}
                formMode={formMode}
                // onCheckboxChange={handleCheckboxInsuranceCardChange}
                setSelectedFormListInsuranceCards={setSelectedFormListInsuranceCards}
                selectedFormListInsuranceCards={selectedFormListInsuranceCards}
                selectedPatientVisitId={selectedPatientVisitId}
                regionNlViews={regionNlViews}
              />
            </div>
            {/* Thông tin đăng kí */}
            {checkedFreeCopay}
            <hr className="pt-2" />
            <div className="fw-bold" style={{ color: COLOR.cyan }}>
              Thông tin đăng ký
            </div>

            <div className="row mt-2">
              <Form.Item
                label="Loại thẻ"
                name="cardType"
                rules={[{ required: true, message: 'Loại thẻ không được để trống' }]}>
                <Radio.Group>
                  {cardTypeRefs.map((item, index) => (
                    <Radio key={index} value={item?.card_type_id}>
                      {item?.name_l}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>

              <Form.Item
                style={{ width: '100%' }}
                label="Dân tộc"
                name="ethnicity"
                placeholder="Chọn dân tộc"
                rules={[{ required: true, message: 'Dân tộc không được để trống' }]}>
                <Select
                  showSearch
                  placeholder="Chọn dân tộc"
                  options={ethnicityRefs.map((ref) => ({
                    value: ref?.ethnicity_id,
                    label: <span>{ref?.name_l_with_code}</span>,
                  }))}
                  onChange={(value) => {
                    const selectedEthnicity = ethnicityRefs.find(
                      (ref) => ref.ethnicity_id === value,
                    )
                    setSelectedEthnicityRef(selectedEthnicity)
                  }}
                  filterOption={(input, option) =>
                    option?.label?.props?.children?.toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
              <Form.Item
                label="Nghề nghiệp"
                name="occupation"
                placeholder="Chọn nghề nghiệp"
                // initialValue={(() => {
                //   const occupation = occupationRefs.find((ref) => ref.MA_NGHE_NGHIEP === '00000')
                //   return occupation?.occupation_id
                // })()}
                rules={[{ required: true, message: 'Nghề nghiệp không được để trống' }]}>
                <Select
                  showSearch
                  placeholder="Chọn nghề nghiệp"
                  style={{ width: '100%' }}
                  options={occupationRefs.map((ref) => ({
                    value: ref?.occupation_id,
                    label: <span>{ref?.name_l_with_code}</span>,
                  }))}
                  filterOption={(input, option) =>
                    option?.label?.props?.children?.toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
              <Form.Item
                label="Đối tượng khám chữa bệnh"
                name="referralType"
                placeholder="Chọn đối tượng khám chữa bệnh"
                rules={[
                  { required: true, message: 'Đối tượng khám chữa bệnh không được để trống' },
                ]}>
                <Select
                  placeholder="Chọn đối tượng khám chữa bệnh"
                  style={{ width: '100%' }}
                  onChange={handleChangeReferralType}
                  options={systemReferralTypeRefs.map((ref) => ({
                    value: ref.referral_type_rcd,
                    label: <span>{ref.ss_name_l}</span>,
                  }))}
                />
              </Form.Item>

              <div className="d-flex justify-content-start gap-2">
                {['9ED', 'TRC', 'RRC', 'CB7', 'RBC', 'ROC'].includes(
                  form.getFieldValue('referralType'),
                ) && (
                  <Form.Item
                    rules={[
                      {
                        validator: () => {
                          const isRightChannel = isRightChannelReferral(
                            systemReferralTypeRefs,
                            form.getFieldValue('referralType'),
                          )

                          if (
                            form.getFieldValue('freeCopayFlag') &&
                            !isRightChannel &&
                            form.getFieldValue('referralType')
                          ) {
                            return Promise.reject(
                              'Hưởng miễn đồng chi trả chỉ áp dụng cho đúng tuyến',
                            )
                          }
                          return Promise.resolve()
                        },
                      },
                    ]}
                    label="Hưởng miễn đồng chi trả"
                    valuePropName="checked"
                    name="freeCopayFlag">
                    <Checkbox></Checkbox>
                  </Form.Item>
                )}

                {checkedFreeCopay && (
                  <Form.Item
                    label="Từ ngày"
                    name="freeCopayDate"
                    rules={[
                      {
                        validator: () => {
                          if (
                            form.getFieldValue('freeCopayDate')?.[0] == null ||
                            form.getFieldValue('freeCopayDate')?.[1] == null
                          ) {
                            return Promise.reject('Vui lòng chọn ngày hưởng miễn đồng chi trả')
                          }
                          return Promise.resolve()
                        },
                      },
                    ]}>
                    <RangePicker format={FORMAT_DATE_DMY} placeholder={['Từ ngày', 'Đến ngày']} />
                  </Form.Item>
                )}
              </div>

              <Form.Item label="Cấp cứu" name="emergencyCaseFlag" valuePropName="checked">
                <Checkbox></Checkbox>
              </Form.Item>
              <div className="d-flex justify-content-start gap-2">
                <Form.Item
                  label="Bệnh hiểm nghèo"
                  valuePropName="checked"
                  name="seriousIllnessFlag"
                  rules={[
                    {
                      required: selectedSystemReferralTypeRef?.referral_type_rcd === 'SII',
                      message: 'Vui lòng tick chọn bệnh hiểm nghèo',
                    },
                  ]}>
                  <Checkbox
                    onChange={(e) => {
                      if (!e.target.checked) {
                        setSelectedSeriousIllness({}) // Đặt lại selectedSeriousIllness thành null khi không tích chọn
                        form.setFieldValue('seriousIllness', null) // Xóa giá trị của trường "seriousIllness" trong form
                      }
                    }}></Checkbox>
                </Form.Item>
                {checkedSeriousIllness && (
                  <div
                    className="d-flex align-items-baseline justify-content-start gap-2"
                    style={{ width: '90%' }}>
                    <Form.Item
                      label="Mã bệnh"
                      rules={[
                        { required: checkedSeriousIllness, message: 'Vui lòng chọn mã bệnh' },
                      ]}
                      name="seriousIllness"
                      placeholder="Chọn mã bệnh"
                      style={{ width: '60%' }}>
                      <LazySelect
                        list={lists.prescription_diagnosis}
                        keyProp="icd10id"
                        isKeyPropString
                        filter="(serious_illness_flag eq true) and (active_flag eq true)"
                        orderBy="seq_num asc"
                        searchFields={['name_l', 'code']}
                        placeholder="Chọn mã bệnh"
                        defaultSelected={selectedSeriousIllness}
                        renderOption={(item) => (
                          <Select.Option key={item.icd10id} value={item.icd10id} item={item}>
                            <div>
                              {item.code} - {item.name_l}
                            </div>
                          </Select.Option>
                        )}
                        setSelectedUser={(item) => {
                          setSelectedSeriousIllness(item)
                          form.setFieldValue('seriousIllness', item?.icd10id)
                        }}
                      />
                    </Form.Item>
                    <span style={{ color: 'red' }}>
                      {selectedSeriousIllness?.ss_cover_condition}
                    </span>
                  </div>
                )}
              </div>

              {referralTypeRcd == 'SII' && (
                <Form.Item
                  rules={[
                    {
                      required: referralTypeRcd == 'SII',
                      message: 'Vui lòng nhập lí do cho đối tượng khám chữa bệnh',
                    },
                  ]}
                  label="Lí do"
                  name="designatedIllnessReason">
                  <Input placeholder="Nhập lí do"></Input>
                </Form.Item>
              )}
              <Form.Item label="Đính kèm" name="attachment">
                {selectedPatientVisitId ? (
                  <DocumentStore
                    dataSource="patient_visit"
                    parentID={documentStoreId}
                    storeID={selectedPatientVisitId}
                    setAttachments={setPatientVisitAttachments}
                    mode={'Edit'}
                    key={selectedPatientVisitId} // Add key prop to force re-render when ID changes
                  />
                ) : (
                  <div className="text-gray-500">Vui lòng chọn lượt khám để xem đính kèm</div>
                )}
              </Form.Item>

              <div className="mb-2">
                <AsyncButton
                  type="primary"
                  ghost
                  onClick={handleFillTransitInformation}
                  hidden={!isTransitInformationRequire}>
                  Giấy chuyển tuyến 365 ngày
                </AsyncButton>
              </div>
              <Form.Item
                label="Nơi chuyển tuyến đến"
                name="referralDestination"
                rules={[
                  {
                    required: isTransitInformationRequire,
                    message: 'Nơi chuyển tuyến đến không được để trống',
                  },
                ]}>
                <Select
                  style={{ width: '100%' }}
                  allowClear
                  showSearch
                  placeholder="Chọn nơi chuyển tuyến đến"
                  filterOption={(input, option) =>
                    option?.label?.props?.children?.toLowerCase().includes(input.toLowerCase())
                  }
                  options={systemReferralDispositionRefList.map((disposition) => ({
                    value: disposition.referral_disposition_rcd,
                    label: <span>{disposition.name_l}</span>,
                  }))}></Select>
              </Form.Item>
              <Form.Item
                label="Số chuyển tuyến"
                name="referralCode"
                rules={[
                  {
                    required: isTransitInformationRequire || !isEmpty(referralDestination),
                    message: 'Số chuyển tuyến không được để trống',
                  },
                ]}>
                <Input placeholder="Nhập..."></Input>
              </Form.Item>
              <Form.Item
                label="Số hồ sơ"
                name="medicalRecordCode"
                rules={[
                  {
                    required: isTransitInformationRequire || !isEmpty(referralDestination),
                    message: 'Số hồ sơ không được để trống',
                  },
                ]}>
                <Input placeholder="Nhập..."></Input>
              </Form.Item>
              <Form.Item
                label="Lí do chuyển đến"
                name="referralReason"
                rules={[
                  {
                    required: isTransitInformationRequire || !isEmpty(referralDestination),
                    message: 'Lí do chuyển đến không được để trống',
                  },
                ]}>
                <Input placeholder="Nhập lí do"></Input>
              </Form.Item>
              <Form.Item
                label="Ngày chuyển đến"
                name="referralDate"
                rules={[
                  {
                    required: isTransitInformationRequire || !isEmpty(referralDestination),
                    message: 'Ngày chuyển đến không được để trống',
                  },
                ]}>
                <DatePicker style={{ width: '100%' }} placeholder="Chọn ngày"></DatePicker>
              </Form.Item>
              <Form.Item label="Ghi chú" name="note">
                <TextArea rows={4} placeholder="vui lòng nhập ghi chú tại đây..." />
              </Form.Item>
              {!form.getFieldValue('signedHealthInsuranceConsentFormFlag') && (
                <Tag color="orange" style={{ marginLeft: '8px' }}>
                  Vui lòng lấy chữ ký của bệnh nhân cho giấy chấp thuận sử dụng BHYT
                </Tag>
              )}
              <Form.Item
                name="signedHealthInsuranceConsentFormFlag"
                valuePropName="checked"
                rules={[
                  {
                    required: true,
                    validator: (_, value) => {
                      if (!value) {
                        return Promise.reject(
                          'Vui lòng lấy chữ ký của bệnh nhân cho giấy chấp thuận sử dụng BHYT',
                        )
                      }
                      return Promise.resolve()
                    },
                  },
                ]}
                label="Đã ký giấy chấp thuận sử dụng BHYT">
                <Checkbox />
              </Form.Item>
            </div>
          </div>
        )}
        {/* Nút */}
        <div className="row mt-2 ">
          <div className="d-flex justify-content-end gap-2">
            <AsyncButton
              type="primary"
              // loading={isLoading}
              disabled={formMode === 'add' && selectedPatientVisitId == null} // Disable if no visit selected or visits are empty
              // style={{
              //   backgroundColor: COLOR.cyan,
              //   color: 'white'
              // }}
              onClick={handleSubmit}>
              {formMode === 'add' ? 'Tạo' : 'Lưu'}
            </AsyncButton>
            <AsyncButton
              style={{ backgroundColor: 'white', color: COLOR.red, borderColor: COLOR.red }}
              onClick={() => {
                onBack()
              }}>
              Huỷ
            </AsyncButton>
          </div>
        </div>
      </Form>
    </div>
  )
}
SearchAndAddVisitPopup.propTypes = propTypes
export default SearchAndAddVisitPopup
