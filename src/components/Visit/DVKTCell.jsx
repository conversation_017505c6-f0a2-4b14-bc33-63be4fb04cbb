import { useState, useEffect } from 'react'
import PropTypes from '../../common/PropTypes'
import { handleCheckMappedChargeDetailItems } from './VisitHelpers'
import { useSelector } from 'react-redux'
import lists from '../../common/lists'
import { updateListItemService } from '../../common/services'
import { Popover } from 'antd'
import { isMatch } from 'lodash'
import { usePatientVisit } from './hooks/usePatientVisit'
import { MODULE_AUTH } from '../../store/auth'
import { useVisitChargeDetails, VISIT_CHARGE_QUERY_KEYS } from './hooks/useVisitChargeDetails'
import { useQueryClient } from '@tanstack/react-query'
import { useSSItemTechnicalServiceMapping } from './hooks/useSSItemTechnicalServiceMapping'
import { useUI } from '../../common/UIProvider'
import useDeepCompareEffect from 'use-deep-compare-effect'
import AsyncButton from '../../common/components/AsyncButton'
import { MODE_VIEW_DATA } from '../../common/constant'

const DVKTCell = ({ record }) => {
  const [checkDVKT, setCheckDVKT] = useState(null)
  const { modeViewData } = useSelector((state) => state[MODULE_AUTH])
  const ui = useUI()

  const {
    data: { currentPatientVisit },
  } = usePatientVisit(record?.patient_visit_id)

  const { data: visitChargeDetails } = useVisitChargeDetails(currentPatientVisit)
  // TODO: For debug only, need to change from useVisitChargeDetails
  // const { data: visitChargeDetails } = useVisitChargeDetails(
  //   { patient_visit_id: '8193eabe-9701-48f8-9797-08dd71a24fad' },
  // )

  const { ssItemTechnicalServiceMapping } = useSSItemTechnicalServiceMapping(
    record?.medical_supplies_id || record?.medicine_id,
  )

  const queryClient = useQueryClient()

  const handleAutoUpdateChargeDetail = async (record, mappedChargeDetail) => {
    const mappedItem = mappedChargeDetail[0]

    const updatedMappingChargeDetails = {
      mapping_technical_services_name: mappedItem?.health_insurance_name || mappedItem?.item_name_e,
      mapping_technical_services_id: mappedItem.technical_services_id,
      technical_services_cd_id: mappedItem.charge_detail_id,
      NGAY_YL: mappedItem.charged_date_time,
      NGAY_TH_YL: mappedItem.serviced_date_time || mappedItem.charged_date_time,
    }
    // do not update technical_services with the same technical_services_id
    if (
      !isMatch(record, updatedMappingChargeDetails) &&
      mappedItem.technical_services_id !== record.technical_services_id
    ) {
      handleUpdateChargeDetail(updatedMappingChargeDetails)
    }
  }

  const handleUpdateChargeDetail = async (newRecord) => {
    try {
      await updateListItemService(
        lists.visit_charge_detail,
        record.visit_charge_detail_id,
        newRecord,
      )

      queryClient.setQueryData(
        [
          VISIT_CHARGE_QUERY_KEYS.VISIT_CHARGE_DETAILS,
          currentPatientVisit?.patient_visit_id,
          modeViewData,
        ],
        (oldData) => {
          return oldData.map((item) =>
            item.visit_charge_detail_id === record.visit_charge_detail_id
              ? { ...item, ...newRecord }
              : item,
          )
        },
      )
    } catch (error) {
      ui.notiError('Failed to update resource')
    }
  }

  useDeepCompareEffect(() => {
    if (record && visitChargeDetails && ssItemTechnicalServiceMapping) {
      const result = handleCheckMappedChargeDetailItems(
        record,
        visitChargeDetails,
        ssItemTechnicalServiceMapping,
      )
      setCheckDVKT(result)
    }
  }, [record, ssItemTechnicalServiceMapping, visitChargeDetails])

  useEffect(() => {
    if (checkDVKT?.status === 2) {
      handleAutoUpdateChargeDetail(record, checkDVKT.mappedChargeDetailItems)
    }
  }, [checkDVKT])

  return (
    <Popover
      content={
        checkDVKT?.status === 1
          ? 'Vui lòng chọn DVKT tương ứng'
          : checkDVKT?.status === 0
            ? 'Chưa có DVKT tương ứng được charge'
            : checkDVKT?.status === 3
              ? 'DVKT chưa được move up'
              : ''
      }>
      <div
        style={{
          minWidth: '70px',
          color:
            checkDVKT?.status === 1 || checkDVKT?.status === 3
              ? 'red'
              : checkDVKT?.status === 0
                ? 'red'
                : '',
        }}>
        {record?.mapping_technical_services_name && modeViewData === MODE_VIEW_DATA.NORMAL && (
          <Popover content="Loại khỏi gói VTYT">
            <AsyncButton
              variant="filled"
              color="danger"
              className="me-1"
              size="small"
              onClick={async () =>
                await handleUpdateChargeDetail({
                  mapping_technical_services_name: null,
                  mapping_technical_services_id: null,
                  technical_services_cd_id: null,
                  NGAY_YL: null,
                  NGAY_TH_YL: null,
                })
              }
              icon={<i className="fa fa-times ms-1" />}></AsyncButton>
          </Popover>
        )}
        {record?.mapping_technical_services_name} {record?.GOI_VTYT && '(' + record?.GOI_VTYT + ')'}
      </div>
    </Popover>
  )
}

DVKTCell.propTypes = {
  record: PropTypes.object.isRequired,
}

export default DVKTCell
