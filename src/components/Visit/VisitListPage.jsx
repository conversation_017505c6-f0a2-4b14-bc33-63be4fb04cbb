import { Button, DatePicker, Drawer, Form, Input, Tabs } from 'antd'
import React, { useEffect, useState } from 'react'

import { FORMAT_DATE, MODE_VIEW_DATA } from '../../common/constant'

import { useSearchParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import OrionVisitListPage from './OrionVisitListPage'
import { PERMISSION } from '../Auth/AuthConstant'
import { useAuth } from '../../common/AuthProvider'
import VisitListTab from './VisitListTab'
import { useForm, useWatch } from 'antd/es/form/Form'
import { useDebounceValue } from 'usehooks-ts'
import dayjs from '../../common/dayjs'
import { PATIENT_VISIT_LIST_KEYS } from './hooks/usePatientVisitList'
import { useQueryClient } from '@tanstack/react-query'
import { ORION_VISIT_QUERY_KEYS } from './hooks/useOrionPatientVisit'
import VisitListDrawerFilter from './VisitListDrawerFilter'
import config from '../../common/config'
import PatientMedicalCodingForm from './PatientMedicalCodingForm'
import PatientMedicalRecordListPage from '../MedicalRecord/PatientMedicalRecordListPage'

const VisitListPage = () => {
  //hooks
  const { modeViewData } = useSelector((state) => state[MODULE_AUTH])
  const { checkPermission } = useAuth()
  const [searchParams, setSearchParams] = useSearchParams()
  const [visitListPageForm] = useForm()
  const [formDrawer] = useForm()
  const queryClient = useQueryClient()

  // state
  const [selectedTabKey, setSelectedTabKey] = useState(searchParams.get('selectedTabKey') || '2')
  const [isDrawerVisible, setIsDrawerVisible] = useState(false)
  const [selectedVisitTypes, setSelectedVisitTypes] = useState([]) // visit_type_rcd

  // watch form
  const [filterHN] = useDebounceValue(useWatch('HN', visitListPageForm), 500)
  const filterVisitDate = useWatch('VisitDate', visitListPageForm)
  const filter_treatment_course_flag = useWatch('treatment_course_flag', formDrawer)
  const ss_confirmation_flag = useWatch('ss_confirmation_flag', formDrawer)
  const processing_status = useWatch('processing_status', formDrawer)

  // For searchParams
  const pFilterHN = searchParams.get('filterHN')
  const pFilterVisitDate = searchParams.getAll('filterVisitDate')

  // For searchParams
  useEffect(() => {
    const newParams = {
      selectedTabKey: selectedTabKey || '2',
      modeViewData: modeViewData || MODE_VIEW_DATA.NORMAL,
      filterHN: filterHN || '',
      filterVisitDate: filterVisitDate?.[0]
        ? [filterVisitDate[0]?.format(FORMAT_DATE), filterVisitDate[1]?.format(FORMAT_DATE)]
        : [dayjs().format(FORMAT_DATE), dayjs().format(FORMAT_DATE)],
      filter_treatment_course_flag: filter_treatment_course_flag || false,
      processing_status: processing_status || 'ALL',
      ss_confirmation_flag: ss_confirmation_flag || 'ALL',
      selectedVisitTypes: selectedVisitTypes || [],
    }

    setSearchParams(newParams)
  }, [
    selectedTabKey,
    modeViewData,
    filterVisitDate,
    filterHN,
    filter_treatment_course_flag,
    processing_status,
    ss_confirmation_flag,
    selectedVisitTypes,
  ])

  const handleTabChange = (key) => {
    setSelectedTabKey(key)
  }

  const handleRefreshByTab = () => {
    if (selectedTabKey == '1') {
      queryClient.invalidateQueries([PATIENT_VISIT_LIST_KEYS.PATIENT_VISIT])
    }
    if (selectedTabKey == '2') {
      queryClient.invalidateQueries([ORION_VISIT_QUERY_KEYS.ORION_PATIENT_VISIT_VIEW])
    }
  }

  const tabItems = [
    {
      key: '1',
      label: 'Danh sách lượt khám Orion',
      visible: checkPermission(PERMISSION.ORION_VISIT),
      children:
        selectedTabKey === '1' ? (
          <OrionVisitListPage
            filterVisitDate={filterVisitDate}
            filterHN={filterHN}
            ss_confirmation_flag={ss_confirmation_flag}
            processing_status={processing_status}
            selectedVisitTypes={selectedVisitTypes}
          />
        ) : (
          ''
        ),
    },
    {
      key: '2',
      label: 'Danh sách lượt khám',
      visible: true,
      children:
        selectedTabKey === '2' ? (
          <VisitListTab
            filterHN={filterHN}
            filterVisitDate={filterVisitDate}
            filter_treatment_course_flag={filter_treatment_course_flag}
            selectedVisitTypes={selectedVisitTypes}
            processing_status={processing_status}
          />
        ) : (
          ''
        ),
    },
    {
      key: 'MedicalRecord',
      visible: checkPermission(PERMISSION.EDIT_VISIT),
      label: 'Hồ sơ bệnh án',
      children:
        selectedTabKey === 'MedicalRecord' ? (
          <PatientMedicalRecordListPage filterHN={filterHN} />
        ) : (
          ''
        ),
    },
    {
      key: '3',
      visible: checkPermission(PERMISSION.TASK),
      label: 'Danh sách Task chờ phê duyệt',
      children: <p>Content for &quot;Danh sách Task chờ phê duyệt&quot; tab</p>,
    },
    {
      key: '4',
      visible: checkPermission(PERMISSION.TASK),
      label: 'Danh sách Task đã phê duyệt',
      children: <p>Content for &quot;Danh sách Task đã phê duyệt&quot; tab</p>,
    },
  ]

  return (
    <Form form={visitListPageForm}>
      <div className="container-fluid">
        <div className="d-flex justify-content-md-start gap-2 mt-4">
          <Form.Item name="HN" label="HN:" initialValue={pFilterHN}>
            <Input placeholder="Nhập mã bệnh nhân (HN)" style={{ width: '300px' }} allowClear />
          </Form.Item>
          <Form.Item
            hidden={!['1', '2'].includes(selectedTabKey)}
            name="VisitDate"
            label="Ngày bắt đầu:"
            initialValue={
              pFilterVisitDate
                ? [dayjs(pFilterVisitDate[0]), dayjs(pFilterVisitDate[1])]
                : [dayjs(), dayjs()]
            }>
            <DatePicker.RangePicker
              allowEmpty={[true, true]}
              style={{ width: '300px' }}
              allowClear
            />
          </Form.Item>
          <Button
            icon={<i className="fa-solid fa-rotate ms-1"></i>}
            onClick={handleRefreshByTab}></Button>
          <Button
            hidden={!['1', '2'].includes(selectedTabKey)}
            icon={<i className="fa-solid fa-filter ms-1" />}
            type="default"
            onClick={() => setIsDrawerVisible(true)}
          />
        </div>

        <Tabs
          size="small"
          activeKey={selectedTabKey}
          onChange={handleTabChange}
          items={tabItems.filter((item) => item.visible)}
        />

        <div className="row mt-2"></div>
      </div>
      <Drawer
        title="Bộ lọc nâng cao"
        placement="right"
        width={400}
        onClose={() => setIsDrawerVisible(false)}
        open={isDrawerVisible}>
        <Form layout="vertical" form={formDrawer}>
          <VisitListDrawerFilter
            selectedVisitTypes={selectedVisitTypes}
            setSelectedVisitTypes={setSelectedVisitTypes}
            visible={isDrawerVisible}
            onClose={() => setIsDrawerVisible(false)}
            form={formDrawer}
            selectedTabKey={selectedTabKey}
          />
          <div className="mt-3">
            <Button
              block
              type="default"
              icon={<i className="fa-solid fa-rotate"></i>}
              onClick={() => {
                formDrawer.resetFields()
                setSelectedVisitTypes([])
                setIsDrawerVisible(false)
              }}>
              Xóa lọc
            </Button>
            <Button
              block
              type="primary"
              icon={<i className="fa-solid fa-arrow-left"></i>}
              className="mt-2 d-flex"
              onClick={() => {
                setIsDrawerVisible(false)
              }}>
              Xem
            </Button>
          </div>
        </Form>
      </Drawer>
    </Form>
  )
}

export default VisitListPage
