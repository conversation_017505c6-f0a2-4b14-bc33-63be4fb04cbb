import { Popover, Select, Tag } from 'antd'
import React from 'react'
import PropTypes from 'prop-types'

import { usePatientVisit } from './hooks/usePatientVisit'
import { useVisitChargeDetails } from './hooks/useVisitChargeDetails'
import { usePolicySubscription } from './hooks/usePolicySubscription'
import dayjs from '../../common/dayjs'
import AsyncButton from '../../common/components/AsyncButton'
import { BUTTON_FONT_WEIGHT } from '../../common/constant'
import { InfoCircleOutlined } from '@ant-design/icons'
import { displayDate } from '../../common/helpers'
import {
  ACTION_VISIT_HISTORY,
  POLICY_SUBSCRIPTION_STATUS,
  VISIT_SUBSCRIBE_POLICY_HISTORY_SNAPSHOT,
} from './VisitConstant'
import { usePatientVisitHistory } from './hooks/usePatientVisitHistory'
import { useUI } from '../../common/UIProvider'
import { MODULE_AUTH } from '../../store/auth'
import { useSelector } from 'react-redux'
import { isEqual } from 'lodash'

const propTypes = {
  onOk: PropTypes.func.isRequired,
  mainVisit: PropTypes.object,
  selectedPatientVisitMappingViews: PropTypes.array,
  healthInsuranceCards: PropTypes.array,
}
const PolicySubscriptionPopup = ({
  onOk,
  mainVisit,
  selectedPatientVisitMappingViews,
  healthInsuranceCards,
}) => {
  const { refetchAll: refetchAllPatientVisit } = usePatientVisit(
    selectedPatientVisitMappingViews[0]?.patient_visit_id,
    !!selectedPatientVisitMappingViews[0]?.patient_visit_id,
  )
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])
  const isAdmin = currentUser?.User_name === 'xyz100871'

  const { data: rawChargeDetailsData } = useVisitChargeDetails(selectedPatientVisitMappingViews[0])

  // exclude extended items
  const visitChargeDetailsData = rawChargeDetailsData.filter((item) => !item.extended_item_flag)

  const {
    responseSubscription,
    patientPolicySubscriptions,
    patientVisitPolicySubscription,
    filteredTblSsPolicyShortCodes,
    selectedPolicySubscription,
    setSelectedPolicySubscription,
    suggestedPolicySubscriptions,
    subscribePolicyMutation,
  } = usePolicySubscription({
    mainVisit,
    visitChargeDetailsData: visitChargeDetailsData || [], // Chỉ truyền dữ liệu khi đã sẵn sàng, // truyền dữ liệu visitChargeDetailsData đã lấy từ query hoặc props
    healthInsuranceCards, // tương tự, truyền danh sách thẻ BHYT
    selectedPatientVisitMappingViews,
  })

  const { addPatientVisitHistory } = usePatientVisitHistory(mainVisit?.patient_visit_id)

  const ui = useUI()
  const handleSubscribePolicy = async () => {
    const suggestedPolicy = suggestedPolicySubscriptions[0]
    if (responseSubscription?.status === POLICY_SUBSCRIPTION_STATUS.NEED_SUBSCRIBE.name_e) {
      if (selectedPolicySubscription?.short_code != suggestedPolicy?.short_code) {
        ui.notiError(`Cần subscribe bệnh nhân với policy ${suggestedPolicy?.name_e}`)
        return
      }

      const policySubscriptionSsSnapShot = {
        card_code: suggestedPolicy?.card_code,
        patientId: mainVisit?.patient_id,
        policyId: selectedPolicySubscription?.policy_id,
        policyShortCode: selectedPolicySubscription?.short_code,
        policyName: selectedPolicySubscription?.name_e,
        startDate: dayjs(suggestedPolicy?.effective_date).format('YYYY-MM-DD'),
        endDate: dayjs(suggestedPolicy?.expiration_date).format('YYYY-MM-DD'),
      }
      const suggestedSubscribePolicyDetailSnapShot = responseSubscription

      await subscribePolicyMutation.mutateAsync(policySubscriptionSsSnapShot)

      await refetchAllPatientVisit()

      const historyData = {
        patient_visit_id: mainVisit?.patient_visit_id,
        note: '',
        data_snapshot: VISIT_SUBSCRIBE_POLICY_HISTORY_SNAPSHOT[
          ACTION_VISIT_HISTORY.SUBSCRIBE_POLICY
        ].makeSnapshot(policySubscriptionSsSnapShot, suggestedSubscribePolicyDetailSnapShot),
      }
      await addPatientVisitHistory.mutateAsync({
        historyData,
        action: ACTION_VISIT_HISTORY.SUBSCRIBE_POLICY,
      })

      // Trong trường hợp subscribe lại policy, nếu policy đã subscribe cho bệnh nhân khác với policy đã chọn và policy của lượt khám == null thì cập nhật lại visit_detail_charge theo mức hưởng mới
      // if (
      //   patientVisitPolicySubscription[0]?.short_code != selectedPolicySubscription?.short_code &&
      //   patientVisitPolicySubscription.short_code == null
      // ) {
      //   await syncVisitDetailChargeService(mainVisit?.patient_visit_id, currentUser?.User_id)
      // }

      //onOk()
      // await refetchChargeDetails() // khong can dung ham nay vì khi addPatientVisitHistory.mutateAsync thực hiện thành công là sẽ chạy luôn hàm visitChargeDetailsQuery.refech()
      // Hiển thị popup yêu cầu refresh
      // Modal.confirm({
      //   title: 'Cập nhật thành công',
      //   content:
      //     'Bạn cần refresh lại trình duyệt để áp dụng thay đổi. Bạn có muốn refresh ngay không?',
      //   okText: 'Refresh',
      //   cancelText: 'Hủy',
      //   onOk: () => {
      //     window.location.reload() // Refresh lại trình duyệt
      //   }
      // })
    } else {
      // subscribePolicyMutation.mutate({
      //   patientId: mainVisit?.patient_id,
      //   policyId: selectedPolicySubscription?.policy_id,
      //   startDate: '2024-01-11', //dayjs(suggestedPolicy?.effective_date).format('YYYY-MM-DD'),
      //   endDate: '2025-01-10' //dayjs(suggestedPolicy?.expiration_date).format('YYYY-MM-DD')
      // })
      onOk()
    }
  }

  const unsubscribe = async () => {
    // only for visit cb6a2549-d171-4402-00d1-08db2c58c5e9
    await subscribePolicyMutation.mutateAsync({
      patientId: mainVisit?.patient_id,
      policyId: '1d58cfb3-18ef-11e5-9aa6-d319b8074c1d',
      startDate: '2024-01-11', //dayjs(suggestedPolicy?.effective_date).format('YYYY-MM-DD'),
      endDate: '2025-01-10', //dayjs(suggestedPolicy?.expiration_date).format('YYYY-MM-DD')
    })

    await refetchAllPatientVisit()

    const historyData = {
      patient_visit_id: mainVisit?.patient_visit_id,
      note: '',
    }

    await addPatientVisitHistory.mutateAsync({
      historyData,
      action: ACTION_VISIT_HISTORY.SUBSCRIBE_POLICY,
    })
  }

  const renderHealthInsuranceCardDetails = () => {
    if (!healthInsuranceCards || healthInsuranceCards.length === 0) {
      return <div>Không có thông tin thẻ BHYT</div>
    }

    return (
      <div>
        {healthInsuranceCards.map((card, index) => (
          <div key={index} style={{ marginBottom: '8px' }}>
            <div>
              <strong>Mã thẻ:</strong> {card.card_code}
            </div>
            <div>
              <strong>Ngày hiệu lực:</strong> {displayDate(card.effective_date)}
            </div>
            <div>
              <strong>Ngày hết hạn:</strong> {displayDate(card.expiration_date)}
            </div>
            <div>
              <strong>Nơi đăng ký ban đầu:</strong> {card.referral_disposition_name_l}
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <>
      {/* <div className="fw-bold">NOTIFICATION</div> */}
      <div className="container-fluid">
        <div
          style={{
            fontSize: '15px',
            color:
              responseSubscription?.status === POLICY_SUBSCRIPTION_STATUS.SUBSCRIBED.name_e
                ? 'green'
                : 'red',
            fontWeight: BUTTON_FONT_WEIGHT,
          }}>
          {responseSubscription?.message}
        </div>
        <div className="d-flex justify-content-start mt-2 mb-2 align-items-center gap-1">
          <div style={{ fontStyle: 'italic' }}>
            Policy bệnh nhân: {patientPolicySubscriptions[0]?.name_e || ''} | Policy lượt khám:{' '}
            {patientVisitPolicySubscription?.name_e || ''} | Pricing class:{' '}
            {mainVisit?.pricing_class || ''}
          </div>{' '}
          <Popover
            content={renderHealthInsuranceCardDetails()}
            title="Thông tin thẻ BHYT"
            // trigger="click"
          >
            <InfoCircleOutlined style={{ color: '#007bff', cursor: 'pointer' }} />
          </Popover>
        </div>
        {responseSubscription?.status === POLICY_SUBSCRIPTION_STATUS.NEED_SUBSCRIBE.name_e && (
          <div className="d-flex justify-content-start mt-2 mb-2 align-items-center gap-1">
            <span>Policy cần subscribe: </span>
            <Select
              style={{ width: '100%' }}
              placeholder="Chọn policy"
              value={selectedPolicySubscription?.short_code}
              onChange={(_, option) => {
                setSelectedPolicySubscription(option?.item)
              }}>
              {filteredTblSsPolicyShortCodes
                .sort((a, b) => a.short_code - b.short_code)
                .map((policy) => (
                  <Select.Option key={policy?.short_code} value={policy?.short_code} item={policy}>
                    <div className="whitespace-normal">
                      <Tag color="orange">
                        {policy?.short_code} - {policy?.name_e}
                      </Tag>
                    </div>
                  </Select.Option>
                ))}
            </Select>
          </div>
        )}
        <div className="d-flex justify-content-end align-items-center">
          {/* <div>
            Policy bệnh nhân: {patientPolicySubscriptions[0]?.name_e} | Policy lượt khám:{' '}
            {patientVisitPolicySubscription?.name_e}
          </div> */}
          <div className="d-flex justify-content-end gap-2">
            {isAdmin && <AsyncButton onClick={unsubscribe}>Unsbuscribe</AsyncButton>}
            <AsyncButton type="primary" onClick={handleSubscribePolicy}>
              OK
            </AsyncButton>
          </div>
        </div>
      </div>
    </>
  )
}

PolicySubscriptionPopup.propTypes = propTypes

const compareProps = (prevProps, nextProps) => {
  // Check mainVisit equality
  const mainVisitEqual = isEqual(prevProps.mainVisit, nextProps.mainVisit)

  // Check selectedPatientVisitMappingViews equality
  const mappingViewsEqual = isEqual(
    prevProps.selectedPatientVisitMappingViews,
    nextProps.selectedPatientVisitMappingViews,
  )

  // Check healthInsuranceCards equality
  const cardsEqual = isEqual(prevProps.healthInsuranceCards, nextProps.healthInsuranceCards)

  return mainVisitEqual && mappingViewsEqual && cardsEqual
}

export default React.memo(PolicySubscriptionPopup, compareProps)
