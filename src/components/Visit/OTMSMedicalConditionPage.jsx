import { useEffect, useState } from 'react'
import COLOR from '../../common/color'
import { BUTTON_FONT_WEIGHT, FORM_MODE } from '../../common/constant'
import { addListItemService, deleteListItemService, getItemsService } from '../../common/services'
import lists from '../../common/lists'
import PropTypes from '../../common/PropTypes'
import { Button, Modal, Popconfirm, Table } from 'antd'
import { PROCESSING_STATUS, TABLE_MEDICAL_TREATMENT_COLUMNS } from './VisitConstant'
import ResizableTitle from '../../common/components/ResizableTitle'
import OTMSMedicalConditionForm from './OTMSMedicalConditionForm'
import { PERMISSION } from '../Auth/AuthConstant'
import { useAuth } from '../../common/AuthProvider'
import { displayDate, handleError, isNotToday, makeResizableColumns } from '../../common/helpers'
import AsyncButton from '../../common/components/AsyncButton'
import { MODULE_AUTH } from '../../store/auth'
import { useSelector } from 'react-redux'
import dayjs from '../../common/dayjs'

const propTypes = {
  mainVisit: PropTypes.object,
  currentPatientDataset: PropTypes.object,
  selectedPatientVisit: PropTypes.object,
}

const OTMSMedicalConditionPage = ({ mainVisit, currentPatientDataset, selectedPatientVisit }) => {
  const { modeViewData, currentUser } = useSelector((state) => state[MODULE_AUTH])

  const { checkPermission } = useAuth()

  const [medicalConditionList, setMedicalConditionList] = useState([])
  const [selectedMedicalCondition, setSelectedMedicalCondition] = useState({
    mode: FORM_MODE.new,
    item: null,
  })
  const [medicalConditionFormVisible, setMedicalConditionFormVisible] = useState(false)
  const [medicalConditionCols, setMedicalConditionCols] = useState([
    ...TABLE_MEDICAL_TREATMENT_COLUMNS,
  ])

  const disabledForMS =
    (![PROCESSING_STATUS.WAITING_MS.name_e, PROCESSING_STATUS.WAITING_HSBA.name_e].includes(
      mainVisit?.processing_status,
    ) ||
      isNotToday(selectedPatientVisit?.actual_visit_datetime)) &&
    !checkPermission(PERMISSION.SIO_MANAGER)

  const getMedicalConditionList = async () => {
    if (!selectedPatientVisit) {
      return
    }

    try {
      const patientVisitId = selectedPatientVisit?.patient_visit_id

      const result = await getItemsService(lists.medical_condition, {
        filter: `patient_visit_id eq ${patientVisitId} and active_flag eq true`,
      }).then((res) => res.value)

      setMedicalConditionList(result)
    } catch (error) {
      handleError(error, 'getMedicalConditionList')
    }
  }

  const handleClickCreate = () => {
    setSelectedMedicalCondition({
      mode: FORM_MODE.new,
      item: null,
    })
    setMedicalConditionFormVisible(true)
  }

  const handleClickEdit = (record) => {
    setSelectedMedicalCondition({
      mode: FORM_MODE.edit,
      item: record,
    })
    setMedicalConditionFormVisible(true)
  }

  const handleClickDelete = async (record) => {
    await deleteListItemService(lists.medical_condition, record.medical_condition_id)
    getMedicalConditionList()
  }

  const onClose = () => {
    setMedicalConditionFormVisible(false)
  }

  useEffect(() => {
    getMedicalConditionList()
  }, [selectedPatientVisit?.patient_visit_id])

  const handleInitMedicalConditionFromTreatmentForm = async () => {
    try {
      let treatmentFormList = await getItemsService(lists.medical_record_form, {
        filter: `medical_record_form_type_rcd eq 'PHIEU_DIEU_TRI'
          and patient_visit_id eq ${selectedPatientVisit?.patient_visit_id}`,
      })
      treatmentFormList = treatmentFormList.value

      if (!treatmentFormList[0]) {
        return
      }

      const newRecord = {
        DIEN_BIEN_LS: treatmentFormList
          .map((r) => {
            const line = []
            line.push(
              `Ngày ${displayDate(r.form_date_time, 'DD/MM/YYYY')}: Lý do khám bệnh: ${r.visit_reason}. Đáp ứng điều trị: ${r.treatment_response}. Kết luận: ${r.conclusion}. Điều trị: ${r.treatment}`,
            )

            return line
              .join('. ')
              .replaceAll('\n', ' ')
              .replaceAll('-	', ' ')
              .replaceAll('  ', ' ')
              .replaceAll('. .', '.')
              .replaceAll(' .', '.')
          })
          .join('. '),
        GIAI_DOAN_BENH: treatmentFormList.map((r) => r.disease_progression).join('; '),
        patient_visit_id: selectedPatientVisit.patient_visit_id,
        active_flag: true,
        lu_user_id: currentUser?.User_id,
      }

      await addListItemService(lists.medical_condition, newRecord)

      await getMedicalConditionList()
    } catch (error) {
      handleError(error, 'handleInitMedicalCondition')
    }
  }

  return (
    <div className="container-fluid">
      <div className="d-flex justify-content-between align-items-center">
        <div className="d-flex gap-2">
          <Button
            disabled={disabledForMS}
            type="primary"
            icon={<i className="fa fa-plus" />}
            onClick={handleClickCreate}>
            Tạo
          </Button>
          <AsyncButton
            disabled={disabledForMS}
            variant="filled"
            color="green"
            icon={<i className="fa fa-check" />}
            onClick={handleInitMedicalConditionFromTreatmentForm}>
            Tạo dữ liệu từ phiếu điều trị
          </AsyncButton>

          {/* refresh */}
          <AsyncButton
            icon={<i className="fa fa-refresh ms-1" />}
            onClick={getMedicalConditionList}>
            Refresh
          </AsyncButton>
        </div>
        <div className="text-right">
          <span className="fw-bold">Lượt khám đang chọn: </span>
          <span>
            {selectedPatientVisit?.actual_visit_datetime
              ? new Date(selectedPatientVisit.actual_visit_datetime).toLocaleString()
              : 'Không có'}
          </span>
        </div>
      </div>
      <div className="mt-2 fw-bold" style={{ color: COLOR.cyan, fontWeight: BUTTON_FONT_WEIGHT }}>
        Danh sách dữ liệu bảng XML5
      </div>
      <Table
        size="small"
        bordered
        className="custom-table mt-2"
        components={{
          header: {
            cell: ResizableTitle,
          },
        }}
        rowKey="medical_condition_id"
        dataSource={medicalConditionList}
        scroll={{
          x: medicalConditionCols.map((c) => c.width).reduce((a, b) => a + b) + 100,
        }}
        columns={[
          ...makeResizableColumns(medicalConditionCols, setMedicalConditionCols),
          {
            title: ' ',
            dataIndex: 'operation',
            key: 'operation',
            fixed: 'right',
            align: 'right',
            width: 70,
            render: (_, record) => {
              return (
                <div className="d-flex justify-content-end gap-2">
                  <Button
                    size="small"
                    disabled={disabledForMS}
                    icon={<i className="fa fa-edit ms-1" />}
                    onClick={() => handleClickEdit(record)}></Button>
                  <Popconfirm
                    title="Bạn có chắc chắn muốn xóa?"
                    okText="Xóa"
                    cancelText="Hủy"
                    onConfirm={() => handleClickDelete(record)}
                    okButtonProps={{ danger: true }} // Nút OK có màu đỏ
                  >
                    <Button
                      size="small"
                      disabled={disabledForMS}
                      icon={<i className="fa fa-trash ms-1" />}
                      danger
                    />
                  </Popconfirm>
                </div>
              )
            },
          },
        ]}
        pagination={false}
      />

      <Modal
        className="custom-modal modal-full"
        open={medicalConditionFormVisible}
        title={
          selectedMedicalCondition.mode === FORM_MODE.new
            ? 'TẠO DỮ LIỆU BẢNG XML5'
            : 'CẬP NHẬT DỮ LIỆU BẢNG XML5'
        }
        onCancel={onClose}
        destroyOnClose
        footer={null}>
        <OTMSMedicalConditionForm
          record={selectedMedicalCondition?.item}
          onClose={() => {
            onClose()
            getMedicalConditionList()
          }}
          mode={selectedMedicalCondition.mode === FORM_MODE.new ? FORM_MODE.new : FORM_MODE.edit}
          currentPatientVisit={selectedPatientVisit}
        />
      </Modal>
    </div>
  )
}

OTMSMedicalConditionPage.propTypes = propTypes

export default OTMSMedicalConditionPage
