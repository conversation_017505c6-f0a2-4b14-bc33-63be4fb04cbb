import React from 'react'
import { Form, Input, Select, DatePicker, Checkbox, Button, Upload } from 'antd'
import { UploadOutlined } from '@ant-design/icons'

const { Option } = Select

const CreateInsurancePopup = () => {
  const [form] = Form.useForm()

  const handleSubmit = (values) => {
    //  console.log('Form Values:', values)
  }

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      style={{ maxWidth: '800px', margin: '0 auto' }}>
      <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
        <Form.Item
          name="cardNumber"
          label="Nhập mã thẻ"
          rules={[{ required: true, message: 'Vui lòng nhập mã thẻ' }]}
          style={{ flex: '1' }}>
          <Input placeholder="Vui lòng nhập" />
        </Form.Item>
        <Form.Item
          name="initialHospital"
          label="Chọn nơi đăng ký khám ban đầu"
          rules={[{ required: true, message: 'Vui lòng chọn nơi đăng ký' }]}
          style={{ flex: '1' }}>
          <Select placeholder="Vui lòng chọn">
            <Option value="hospital1">Bệnh viện 1</Option>
            <Option value="hospital2">Bệnh viện 2</Option>
          </Select>
        </Form.Item>
      </div>

      <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
        <Form.Item
          name="type"
          label="Chọn loại"
          rules={[{ required: true, message: 'Vui lòng chọn loại' }]}
          style={{ flex: '1' }}>
          <Select placeholder="Vui lòng chọn">
            <Option value="type1">Loại 1</Option>
            <Option value="type2">Loại 2</Option>
          </Select>
        </Form.Item>
        <Form.Item
          name="style"
          label="Chọn kiểu"
          rules={[{ required: true, message: 'Vui lòng chọn kiểu' }]}
          style={{ flex: '1' }}>
          <Select placeholder="Vui lòng chọn">
            <Option value="paper">Thẻ giấy</Option>
            <Option value="digital">Thẻ điện tử</Option>
          </Select>
        </Form.Item>
      </div>

      <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
        <Form.Item name="effectiveDate" label="Ngày hiệu lực" style={{ flex: '1' }}>
          <DatePicker style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item name="expiryDate" label="Ngày hết hạn" style={{ flex: '1' }}>
          <DatePicker style={{ width: '100%' }} />
        </Form.Item>
      </div>

      <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
        <Form.Item name="issueDate" label="Ngày phát hành" style={{ flex: '1' }}>
          <DatePicker style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item
          name="continuousCoverageDate"
          label="Thời điểm nằm năm liên tục"
          style={{ flex: '1' }}>
          <DatePicker style={{ width: '100%' }} />
        </Form.Item>
      </div>

      <Form.Item name="notes" label="Ghi chú">
        <Input.TextArea autoSize={{ minRows: 2 }} placeholder="Vui lòng nhập" rows={3} />
      </Form.Item>

      <Form.Item label="Đính kèm" valuePropName="fileList">
        <Upload>
          <Button icon={<UploadOutlined />}>Tải file</Button>
        </Upload>
      </Form.Item>

      <div
        style={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: '16px' }}>
        <Form.Item name="isExempt" valuePropName="checked">
          <Checkbox>Được hưởng miễn đồng chi trả</Checkbox>
        </Form.Item>
        <Form.Item name="startDate" label="Từ ngày">
          <DatePicker />
        </Form.Item>
        <Form.Item name="endDate" label="Đến ngày">
          <DatePicker />
        </Form.Item>
        <Form.Item name="inNetwork" valuePropName="checked">
          <Checkbox>Đúng tuyến</Checkbox>
        </Form.Item>
        <Form.Item name="poverty" valuePropName="checked">
          <Checkbox>Bệnh hiểm nghèo</Checkbox>
        </Form.Item>
      </div>

      {/* <Form.Item>
        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '16px' }}>
          <Button htmlType="submit" type="primary">
            Thêm
          </Button>
          <Button htmlType="button" danger>
            Hủy
          </Button>
        </div>
      </Form.Item> */}
    </Form>
  )
}

export default CreateInsurancePopup
