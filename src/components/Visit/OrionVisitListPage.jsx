import { Button, Form, Input, Popconfirm, Table, Modal, Tag, Popover, Radio, Upload } from 'antd'
import { useOrionPatientVisit } from './hooks/useOrionPatientVisit'
import { displayDate, displayDateTime, handleError } from '../../common/helpers'
import PropTypes from '../../common/PropTypes'
import dayjs from '../../common/dayjs'
import { FORMAT_DATE } from '../../common/constant'
import { ACTION_VISIT_HISTORY, PROCESSING_STATUS } from './VisitConstant'
import { useEffect, useState } from 'react'
import { useForm, useWatch } from 'antd/es/form/Form'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import SearchAndAddVisitPopup from './SearchAndAddVisitPopup'
import {
  addMultiMergedPatientVisits,
  getHealthInsuranceCards,
  getHealthInsuranceCardsByPatientVisitId,
  getPatientDatasetByFilterEndWithHN,
} from './VisitService'
import _, { reject } from 'lodash'
import { usePatientVisitHistory } from './hooks/usePatientVisitHistory'
import { deleteListItemService, getItemService, getItemsService } from '../../common/services'
import lists from '../../common/lists'
import AsyncButton from '../../common/components/AsyncButton'
import useApp from 'antd/es/app/useApp'
import { useUI } from '../../common/UIProvider'
import { UploadOutlined } from '@ant-design/icons'
import DocumentStore, {
  DOCUMENT_STORE_MODE,
} from '../../common/components/DocumentStore/DocumentStore'
import { addListItemService } from '../../common/services'
import { v4 } from 'uuid'
import { MEDICAL_RECORD_FORM_TYPE } from '../MedicalRecord/MedicalRecordConstant'

const VISIT_REJECT_OPTION = {
  NOT_ELIGIBLE: {
    key: 'NOT_ELIGIBLE',
    nameE: 'Not eligible',
    nameL: 'Không đủ điều kiện hưởng',
  },
  DECLINE: {
    key: 'DECLINE',
    nameE: 'Decline',
    nameL: 'Từ chối không tham gia BHYT',
  },
}

const propsType = {
  filterVisitDate: PropTypes.array,
  filterHN: PropTypes.string,
}

function OrionVisitListPage({
  filterVisitDate,
  filterHN,
  ss_confirmation_flag,
  processing_status,
  selectedVisitTypes,
}) {
  const formatedDate = filterVisitDate?.map((date) => {
    if (date) return date.format(FORMAT_DATE)
    return null
  })

  const { addPatientVisitHistory } = usePatientVisitHistory()
  const app = useApp()
  const ui = useUI()
  const [formModal] = useForm()
  const { currentUser, isDebugMode } = useSelector((state) => state[MODULE_AUTH])

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [selectedPatientVisit, setSelectedPatientVisit] = useState(null)
  const [initialCurrentPatient, setInitialCurrentPatient] = useState(null)
  const {
    orionPatientVisitQuery,
    orionPatientVisits,
    updatePatientVisitMutation,
    createSSPatientVisitMutation,
    refetchOrionPatientVisit,
  } = useOrionPatientVisit(
    formatedDate,
    filterHN,
    ss_confirmation_flag,
    processing_status,
    selectedVisitTypes,
  )

  const [isRejectModalVisible, setIsRejectModalVisible] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState(null)
  const rejectType = useWatch('rejectType', formModal)
  const [attachments, setAttachments] = useState([])
  const [isViewConfirmationModalVisible, setIsViewConfirmationModalVisible] = useState(false)
  const [selectedMedicalRecordFormId, setSelectedMedicalRecordFormId] = useState(null)

  // Hàm hiển thị modal nhập lý do
  const showNoteModal = ({
    title,
    onConfirm,
    placeholder = 'Nhập lý do...',
    defaultValue = '',
    isNoteRequired = false,
  }) => {
    formModal.setFieldsValue({ note: defaultValue })
    app.modal.confirm({
      title,
      content: (
        <Form form={formModal} layout="vertical" style={{ width: '100%' }}>
          <Form.Item
            name="note"
            label="Lý do"
            initialValue={defaultValue}
            rules={[
              {
                required: isNoteRequired,
                message: 'Lý do là bắt buộc!',
              },
            ]}>
            <Input.TextArea autoSize={{ minRows: 2 }} placeholder={placeholder} rows={3} />
          </Form.Item>
        </Form>
      ),
      onOk: async () => {
        try {
          await formModal.validateFields()
          const noteValue = formModal.getFieldValue('note')

          await onConfirm(noteValue)
        } catch (error) {
          handleError(error)
        }
      },
      okText: 'Xác nhận',
      cancelText: 'Hủy',
      maskClosable: true,
    })
  }

  const handleOpenModal = () => {
    setIsModalVisible(true)
  }

  const handleCloseModal = () => {
    setInitialCurrentPatient(null)
    setIsModalVisible(false)
  }

  // return false if notfound treatment_course, else return true and merge visit
  const handleCheckAndMergeVisit = async (patientVisit, note) => {
    const patientVisitIdToMerge = patientVisit?.patient_visit_id
    const patientId = patientVisit?.patient_id

    if (!patientVisitIdToMerge || !patientId) {
      return false
    }

    try {
      let parentPatientVisitId = await getItemsService(lists.patient_visit_mapping_view, {
        filter: `patient_id eq ${patientId} 
          and parent_patient_visit_id eq null 
          and treatment_course_flag eq true 
          and treatment_course_start_date le ${dayjs(patientVisit?.actual_visit_datetime).toISOString()} 
          and treatment_course_end_date ge ${dayjs(patientVisit?.actual_visit_datetime).toISOString()}`,
        top: 1,
        count: false,
      })
      const parentPatientVisit = parentPatientVisitId.value[0]
      parentPatientVisitId = parentPatientVisitId.value[0]?.patient_visit_id

      if (!parentPatientVisitId) {
        return false
      }

      await addMultiMergedPatientVisits(
        [patientVisitIdToMerge],
        parentPatientVisitId,
        currentUser,
        patientId,
      )

      // change parentPatientVisitId processing_status
      await updatePatientVisitMutation.mutateAsync({
        id: parentPatientVisitId,
        data: {
          processing_status: PROCESSING_STATUS.WAITING_BHYT.name_e,
        },
      })

      await updatePatientVisitMutation.mutateAsync({
        id: patientVisit?.patient_visit_id,
        data: {
          ss_confirmation_flag: true,
          ss_confirmation_note: note || null,
        },
      })

      addPatientVisitHistory.mutateAsync({
        historyData: {
          patient_visit_id: parentPatientVisitId,
          merged_patient_visit_id: patientVisitIdToMerge,
          note: 'Auto merge visit during insurance confirmation',
        },
        action: ACTION_VISIT_HISTORY.MERGE_VISIT,
      })

      addPatientVisitHistory.mutateAsync({
        historyData: {
          patient_visit_id: parentPatientVisitId,
          merged_patient_visit_id: patientVisitIdToMerge,
          note: 'Auto change status when new visit is merged',
        },
        action: ACTION_VISIT_HISTORY.SAVE_INFO,
      })

      ui.notiSuccess(
        'Tự động merge lượt khám',
        `Lượt khám đã tự động merge vào đợt điều trị bắt đầu từ ${displayDate(parentPatientVisit?.actual_visit_datetime)}. 
        Lượt khám gốc: ${parentPatientVisit?.visit_code}`,
      )

      return true
    } catch (error) {
      handleError(error, 'handleMergeVisit')
    }
  }

  const checkExistPatientVisitId = async (patientVisitId) => {
    try {
      const result = await getItemService(lists.api_orion_patient_visit_view, patientVisitId)

      if (result?.ss_confirmation_flag !== null) {
        return result
      }
    } catch (error) {
      // ignore error
    }

    return null
  }

  const handleConfirmInsurance = (record) => {
    showNoteModal({
      title: 'Xác nhận tham gia BHYT',
      onConfirm: async (note) => {
        const isAutoMerge = await handleCheckAndMergeVisit(record, note)

        if (isAutoMerge) {
          refetchOrionPatientVisit()
          return
        }

        const existPatientVisit = await checkExistPatientVisitId(record.patient_visit_id)

        if (!existPatientVisit) {
          await createSSPatientVisitMutation.mutateAsync({
            data: {
              card_type_id: record.card_type_id,
              designated_illness_reason: record.designated_illness_reason,
              emergency_case_flag: record.emergency_case_flag,
              ethnicity_id: record.ethnicity_id,
              free_copay_flag: record.free_copay_flag,
              medical_record_code: record.medical_record_code,
              note: record.note,
              occupation_id: record.occupation_id,
              referral_code: record.referral_code,
              referral_date: record.referral_date,
              referral_disposition_rcd: record.referral_disposition_rcd,
              referral_reason: record.referral_reason,
              referral_type_rcd: record.referral_type_rcd,
              free_copay_start_date: record.free_copay_start_date,
              free_copay_end_date: record.free_copay_end_date,
              right_channel_flag: record.right_channel_flag,
              serious_illness_flag: record.serious_illness_flag,
              serious_illness_icd10id: record.serious_illness_icd10id,
              serious_illness_icd_code: record.serious_illness_icd_code,
              ss_confirmation_flag: true,
              ss_confirmation_note: note || null,
              patient_visit_id: record?.patient_visit_id,
              patient_id: record?.patient_id,
              processing_status: PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e,
              lu_updated: dayjs(),
              lu_user_id: currentUser?.User_id,
            },
          })

          await addPatientVisitHistory.mutateAsync({
            historyData: {
              patient_visit_id: record?.patient_visit_id,
            },
            action: ACTION_VISIT_HISTORY.CREATE_VISIT,
          })
        } else {
          await updatePatientVisitMutation.mutateAsync({
            id: record.patient_visit_id,
            data: {
              ss_confirmation_flag: true,
              ss_confirmation_note: note || null,
              processing_status: PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e,
            },
          })
        }
      },
      isNoteRequired: false,
    })
  }

  const handleConfirmAndCreateVisit = (record) => {
    showNoteModal({
      title: 'Xác nhận và tạo lượt khám',
      onConfirm: async (note) => {
        const isAutoMerge = await handleCheckAndMergeVisit(record, note)

        if (isAutoMerge) {
          refetchOrionPatientVisit()
          return
        }

        const existPatientVisit = await checkExistPatientVisitId(record.patient_visit_id)

        if (!existPatientVisit) {
          await createSSPatientVisitMutation.mutateAsync({
            data: {
              card_type_id: record.card_type_id,
              designated_illness_reason: record.designated_illness_reason,
              emergency_case_flag: record.emergency_case_flag,
              ethnicity_id: record.ethnicity_id,
              free_copay_flag: record.free_copay_flag,
              medical_record_code: record.medical_record_code,
              note: record.note,
              occupation_id: record.occupation_id,
              referral_code: record.referral_code,
              referral_date: record.referral_date,
              referral_disposition_rcd: record.referral_disposition_rcd,
              referral_reason: record.referral_reason,
              referral_type_rcd: record.referral_type_rcd,
              free_copay_start_date: record.free_copay_start_date,
              free_copay_end_date: record.free_copay_end_date,
              right_channel_flag: record.right_channel_flag,
              serious_illness_flag: record.serious_illness_flag,
              serious_illness_icd10id: record.serious_illness_icd10id,
              serious_illness_icd_code: record.serious_illness_icd_code,
              ss_confirmation_flag: true,
              ss_confirmation_note: note || null,
              patient_visit_id: record?.patient_visit_id,
              patient_id: record?.patient_id,
              processing_status: PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e,
              lu_updated: dayjs(),
              lu_user_id: currentUser?.User_id,
            },
          })

          await addPatientVisitHistory.mutateAsync({
            historyData: {
              patient_visit_id: record?.patient_visit_id,
            },
            action: ACTION_VISIT_HISTORY.CREATE_VISIT,
          })

          setSelectedPatientVisit(record)

          const patients = await getPatientDatasetByFilterEndWithHN(record.visible_patient_id)
          setInitialCurrentPatient(patients.value[0])
        } else {
          await updatePatientVisitMutation.mutateAsync({
            id: record.patient_visit_id,
            data: {
              ss_confirmation_flag: true,
              ss_confirmation_note: note || null,
              processing_status: PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e,
            },
          })
        }
      },
      isNoteRequired: false,
    })
  }

  const handleCreateVisit = async (record) => {
    setSelectedPatientVisit(record)
    const patients = await getPatientDatasetByFilterEndWithHN(record.visible_patient_id)
    setInitialCurrentPatient(patients.value[0])
  }

  const handleRejectInsurance = async (record) => {
    formModal.setFieldsValue({ note: '', rejectType: VISIT_REJECT_OPTION.NOT_ELIGIBLE.key })

    setSelectedRecord(record)
    setIsRejectModalVisible(true)
  }

  const handleRejectModalOk = async () => {
    if (rejectType === VISIT_REJECT_OPTION.DECLINE.key && attachments.length === 0) {
      app.message.error('Vui lòng tải lên phiếu từ chối!')
      return
    }

    try {
      await formModal.validateFields()
    } catch (error) {
      app.message.error('Vui lòng nhập thông tin bắt buộc!')
      return
    }

    try {
      const values = formModal.getFieldsValue()
      const rejectTypeValue = values.rejectType
      const noteValue = values.note

      const newRecord = {
        card_type_id: selectedRecord?.card_type_id,
        designated_illness_reason: selectedRecord?.designated_illness_reason,
        emergency_case_flag: selectedRecord?.emergency_case_flag,
        ethnicity_id: selectedRecord?.ethnicity_id,
        free_copay_flag: selectedRecord?.free_copay_flag,
        medical_record_code: selectedRecord?.medical_record_code,
        note: selectedRecord?.note,
        occupation_id: selectedRecord?.occupation_id,
        referral_code: selectedRecord?.referral_code,
        referral_date: selectedRecord?.referral_date,
        referral_disposition_rcd: selectedRecord?.referral_disposition_rcd,
        referral_reason: selectedRecord?.referral_reason,
        referral_type_rcd: selectedRecord?.referral_type_rcd,
        free_copay_start_date: selectedRecord?.free_copay_start_date,
        free_copay_end_date: selectedRecord?.free_copay_end_date,
        right_channel_flag: selectedRecord?.right_channel_flag,
        serious_illness_flag: selectedRecord?.serious_illness_flag,
        serious_illness_icd10id: selectedRecord?.serious_illness_icd10id,
        serious_illness_icd_code: selectedRecord?.serious_illness_icd_code,
        patient_visit_id: selectedRecord?.patient_visit_id,
        patient_id: selectedRecord?.patient_id,

        // ss_confirmation
        ss_confirmation_flag: false,
        ss_confirmation_reject_type: rejectTypeValue,
        ss_confirmation_note:
          (VISIT_REJECT_OPTION[rejectTypeValue]?.nameL || '') + (noteValue ? `: ${noteValue}` : ''),
        lu_user_id: currentUser?.User_id,
      }

      await createSSPatientVisitMutation.mutateAsync({ data: newRecord })
      app.message.success('Xác nhận thành công')
      setIsRejectModalVisible(false)
      formModal.resetFields()
      refetchOrionPatientVisit()
    } catch (error) {
      handleError(error, 'handleRejectModalOk')
      app.message.error('Xác nhận thất bại!')
    }
  }

  const handleRejectModalCancel = () => {
    setIsRejectModalVisible(false)
    setSelectedRecord(null)
  }

  const getMedicalRecordFormId = async (record, createIfNotExist = false) => {
    try {
      // auto get medical_record_form_id
      const medicalRecordForms = await getItemsService(lists.medical_record_form, {
        filter: `patient_visit_id eq ${record.patient_visit_id} and medical_record_form_type_rcd eq '${MEDICAL_RECORD_FORM_TYPE.GIAY_TU_CHOI.key}'`,
      })

      let medicalRecordFormId = medicalRecordForms.value?.[0]?.medical_record_form_id || null

      if (!medicalRecordFormId && createIfNotExist) {
        const newMedicalRecordForm = {
          medical_record_form_id: v4(),
          medical_record_form_type_rcd: MEDICAL_RECORD_FORM_TYPE.GIAY_TU_CHOI.key,
          patient_id: selectedRecord.patient_id,
          patient_visit_id: selectedRecord.patient_visit_id,
          form_date_time: dayjs(),
          title: `Giấy từ chối BHYT - ${dayjs().format('YYYY-MM-DD HH:mm')}`,
        }

        const result = await addListItemService(lists.medical_record_form, newMedicalRecordForm)
        medicalRecordFormId = result.medical_record_form_id
      }

      setSelectedMedicalRecordFormId(medicalRecordFormId)
    } catch (error) {
      handleError(error, 'getMedicalRecordFormId')
    }
  }

  // listen rejectType
  useEffect(() => {
    // Tạo MedicalRecordForm nếu là từ chối BHYT
    if (rejectType === VISIT_REJECT_OPTION.DECLINE.key) {
      getMedicalRecordFormId(selectedRecord, true)
    }
  }, [rejectType])

  const handleViewConfirmation = async (record) => {
    try {
      await getMedicalRecordFormId(record)

      setSelectedRecord(record)
      setIsViewConfirmationModalVisible(true)
    } catch (error) {
      handleError(error, 'handleViewConfirmation')
      app.message.error('Không thể lấy thông tin chi tiết!')
    }
  }

  const handleCloseViewConfirmationModal = () => {
    setIsViewConfirmationModalVisible(false)
    setSelectedRecord(null)
    setSelectedMedicalRecordFormId(null)
  }

  const columns = [
    {
      title: 'Loại',
      dataIndex: 'processing_status',
      key: 'processing_status',
      width: 120,
      onCell: (record) => ({ colSpan: record.children ? 2 : 1 }),
      render: (status, record) => (record.isParent ? <b>{status}</b> : record.visit_type_name_e),
    },
    {
      title: 'HN',
      dataIndex: 'visible_patient_id',
      key: 'visible_patient_id',
      width: 100,
      onCell: (record) => ({ colSpan: record.children ? 0 : 1 }),
      render: (text, record) => (record.children ? '' : text),
    },
    {
      title: 'Tên bệnh nhân',
      dataIndex: 'fullname',
      width: 150,
      key: 'fullName',
    },
    {
      title: 'Mã lượt khám',
      dataIndex: 'visit_code',
      width: 110,
      key: 'visitCode',
    },
    {
      title: 'Ngày bắt đầu lượt khám',
      dataIndex: 'actual_visit_datetime',
      key: 'startDate',
      width: 140,
      align: 'right',
      render: (date, record) => (record.children ? '' : displayDateTime(date)),
    },
    {
      title: 'Ngày kết thúc lượt khám',
      dataIndex: 'closure_visit_datetime',
      key: 'endDate',
      width: 140,
      align: 'right',
      render: (date, record) => (record.children ? '' : displayDateTime(date)),
    },
    {
      title: 'Trạng thái xác nhận',
      dataIndex: 'ss_confirmation_flag',
      key: 'ss_confirmation_flag',
      width: 140,
      render: (flag, record) => {
        if (flag === null) return <Tag color="blue">Chưa xác nhận</Tag>

        if (flag === false) {
          return (
            <div>
              <Tag color="red">
                Không tham gia BHYT
                {record.ss_confirmation_note && (
                  <div style={{ marginTop: 4, textWrap: 'wrap' }}>
                    <span className="text-red-700">Lý do: {record.ss_confirmation_note}</span>
                  </div>
                )}
                <span
                  className="text-blue-500 cursor-pointer"
                  onClick={() => handleViewConfirmation(record)}>
                  Xem thêm
                </span>
              </Tag>
            </div>
          )
        }

        return <Tag color="green">Có tham gia BHYT</Tag>
      },
    },
    {
      title: 'Hành động',
      key: 'action',
      fixed: 'right',
      width: 110,
      render: (_, record) =>
        record.children ? null : (
          <div className="d-flex gap-1 flex-column py-1">
            {record.processing_status === PROCESSING_STATUS.WAITING_FULLFILL_INFO.name_e &&
            record.ss_confirmation_flag === true ? (
              <>
                <AsyncButton
                  type="primary"
                  size="small"
                  className="btn-wrap"
                  ghost
                  onClick={async () => await handleCreateVisit(record)}>
                  Nhập thông tin
                </AsyncButton>
              </>
            ) : (
              record.ss_confirmation_flag === null && (
                <Popconfirm
                  title="Xác nhận BHYT"
                  description={
                    <div style={{ display: 'flex', gap: 8 }}>
                      <AsyncButton
                        ghost
                        type="primary"
                        size="small"
                        onClick={async () => await handleConfirmInsurance(record)}>
                        Xác nhận BHYT
                      </AsyncButton>
                      <AsyncButton
                        size="small"
                        type="primary"
                        onClick={async () => await handleConfirmAndCreateVisit(record)}>
                        Xác nhận và Tạo lượt khám
                      </AsyncButton>
                    </div>
                  }
                  okButtonProps={{ style: { display: 'none' } }}
                  cancelButtonProps={{ style: { display: 'none' } }}>
                  <Popover placement="left" content="Có tham gia BHYT">
                    <Button type="primary" icon={<i className="fa fa-check" />} size="small">
                      BHYT
                    </Button>
                  </Popover>
                </Popconfirm>
              )
            )}

            {record.ss_confirmation_flag === null && (
              <Popconfirm
                title="Bạn chắc chắn không tham gia BHYT?"
                description={
                  <div style={{ display: 'flex', gap: 8 }}>
                    <AsyncButton
                      size="small"
                      ghost
                      danger
                      onClick={() => handleRejectInsurance(record)}>
                      Xác nhận không tham gia
                    </AsyncButton>
                  </div>
                }
                okButtonProps={{ style: { display: 'none' } }}
                cancelButtonProps={{ style: { display: 'none' } }}>
                <Popover placement="left" content="Không tham gia BHYT">
                  <Button type="default" danger icon={<i className="fa fa-times" />} size="small">
                    BHYT
                  </Button>
                </Popover>
              </Popconfirm>
            )}

            {record.ss_confirmation_flag !== null && (
              <Popconfirm
                title="Xác nhận lại"
                onConfirm={async () => {
                  // get cards of visit and delete all cards
                  const cards = await getHealthInsuranceCardsByPatientVisitId(
                    record.patient_visit_id,
                    '',
                    1000,
                  )
                  await Promise.all(
                    cards.value.map((card) =>
                      deleteListItemService(
                        lists.health_insurance_card,
                        card.health_insurance_card_id,
                      ),
                    ),
                  )

                  await deleteListItemService(lists.patient_visit, record.patient_visit_id)
                  refetchOrionPatientVisit()
                }}>
                <AsyncButton danger size="small" icon={<i className="fa fa-trash ms-1" />}>
                  Xác nhận lại
                </AsyncButton>
              </Popconfirm>
            )}
          </div>
        ),
    },
  ]

  useEffect(() => {
    if (initialCurrentPatient) {
      handleOpenModal()
    }
  }, [initialCurrentPatient])

  return (
    <div>
      <div className="row mt-2"></div>
      <Table
        dataSource={orionPatientVisits}
        className="custom-table"
        size="small"
        scroll={{ x: columns.map((c) => c.width).reduce((a, b) => a + b) + 100 }}
        columns={columns}
        rowKey="patient_visit_id"
        loading={orionPatientVisitQuery.isLoading || orionPatientVisitQuery.isFetching}
        pagination={{ defaultPageSize: 20 }}
      />

      <Modal
        className="custom-modal"
        title="TẠO LƯỢT KHÁM DÙNG BHYT"
        width={2000}
        open={isModalVisible}
        onCancel={handleCloseModal}
        destroyOnClose
        maskClosable={false}
        keyboard={false}
        footer={null}>
        <SearchAndAddVisitPopup
          onSave={() => {
            setIsModalVisible(false)
            setInitialCurrentPatient(null)
            refetchOrionPatientVisit()
          }}
          initialSelectedPatientVisitId={selectedPatientVisit?.patient_visit_id || null}
          initialCurrentPatient={initialCurrentPatient}
          onBack={handleCloseModal}
          formMode="edit"
        />
      </Modal>

      <Modal
        title="Xác nhận không tham gia BHYT"
        open={isRejectModalVisible}
        onCancel={handleRejectModalCancel}
        footer={[
          <Button key="cancel" onClick={handleRejectModalCancel}>
            Hủy
          </Button>,
          <AsyncButton key="submit" type="primary" onClick={handleRejectModalOk}>
            Xác nhận
          </AsyncButton>,
        ]}>
        <Form form={formModal} layout="vertical">
          <Form.Item
            name="rejectType"
            label="Lý do không tham gia BHYT"
            rules={[{ required: true, message: 'Vui lòng chọn lý do' }]}>
            <Radio.Group>
              <Radio value={VISIT_REJECT_OPTION.NOT_ELIGIBLE.key}>
                {VISIT_REJECT_OPTION.NOT_ELIGIBLE.nameL}
              </Radio>
              <Radio value={VISIT_REJECT_OPTION.DECLINE.key}>
                {VISIT_REJECT_OPTION.DECLINE.nameL}
              </Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            name="note"
            label="Ghi chú"
            rules={[{ required: true, message: 'Vui lòng nhập ghi chú' }]}>
            <Input.TextArea rows={4} />
          </Form.Item>

          <AsyncButton
            hidden={!isDebugMode}
            onClick={async () => {
              await deleteListItemService(lists.medical_record_form, selectedMedicalRecordFormId)
              setSelectedMedicalRecordFormId(null)
            }}>
            Remove medical record
          </AsyncButton>

          {rejectType === VISIT_REJECT_OPTION.DECLINE.key && (
            <>
              <Button
                type="link"
                onClick={() =>
                  window.open(
                    'https://femvn.sharepoint.com/:w:/s/SBT/Ef2ZuhZ4TklJr4DGOZF_jMkB21iayr6kYgJtkRXDj770hg?e=B0Yx9R',
                    '_blank',
                  )
                }
                style={{ marginBottom: 8 }}>
                Tải xuống phiếu từ chối
              </Button>
              <div style={{ marginBottom: 16 }}>
                {!!selectedMedicalRecordFormId && (
                  <DocumentStore
                    dataSource={lists.medical_record_form.listName}
                    parentID={4}
                    storeID={selectedMedicalRecordFormId}
                    mode={DOCUMENT_STORE_MODE.EDIT}
                    setAttachments={setAttachments}
                  />
                )}
              </div>
            </>
          )}
        </Form>
      </Modal>

      <Modal
        title="Chi tiết xác nhận BHYT"
        open={isViewConfirmationModalVisible}
        onCancel={handleCloseViewConfirmationModal}
        footer={[
          <Button key="close" onClick={handleCloseViewConfirmationModal}>
            Đóng
          </Button>,
        ]}
        width={600}>
        {selectedRecord && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <strong>Trạng thái:</strong>{' '}
              <Tag color={selectedRecord.ss_confirmation_flag ? 'green' : 'red'}>
                {selectedRecord.ss_confirmation_flag ? 'Có tham gia BHYT' : 'Không tham gia BHYT'}
              </Tag>
            </div>

            {selectedRecord.ss_confirmation_note && (
              <div style={{ marginBottom: 16 }}>
                <strong>Ghi chú:</strong>
                <div style={{ marginTop: 8 }}>{selectedRecord.ss_confirmation_note}</div>
              </div>
            )}

            {selectedRecord.ss_confirmation_reject_type === VISIT_REJECT_OPTION.DECLINE.key &&
              !!selectedMedicalRecordFormId && (
                <div>
                  <strong>File từ chối:</strong>
                  <DocumentStore
                    dataSource={lists.medical_record_form.listName}
                    parentID={4}
                    storeID={selectedMedicalRecordFormId}
                    mode={DOCUMENT_STORE_MODE.VIEW}
                  />
                </div>
              )}
          </div>
        )}
      </Modal>
    </div>
  )
}

OrionVisitListPage.propTypes = propsType

export default OrionVisitListPage
