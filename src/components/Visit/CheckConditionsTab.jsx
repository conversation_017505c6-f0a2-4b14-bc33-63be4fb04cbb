import React, { useEffect, useState } from 'react'
import {
  Form,
  Input,
  DatePicker,
  Select,
  Table,
  Checkbox,
  Radio,
  Button,
  Image,
  Popconfirm,
} from 'antd'
import COLOR from '../../common/color'
import PropTypes from '../../common/PropTypes'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useUI } from '../../common/UIProvider'
import useDeepCompareEffect from 'use-deep-compare-effect'
import imgNote from '../../assets/note.png'
import stopIcon from '../../assets/stop-icon.png'
import warningIcon from '../../assets/warning-icon.png'
import {
  getAllRuleRefs,
  getRuleVisitItemDetailsByPatientVisitIds,
  updateManualCheckFlag,
  validateVisitRule,
  validateXmlRule,
} from '../../services/ruleService'
import { usePatientVisit } from './hooks/usePatientVisit'
import { useVisitChargeDetails, VISIT_CHARGE_QUERY_KEYS } from './hooks/useVisitChargeDetails'
import { MODULE_AUTH } from '../../store/auth'
import { useSelector } from 'react-redux'
import { MODE_VIEW_DATA } from '../../common/constant'
import AsyncButton from '../../common/components/AsyncButton'
import NotePopup from './NotePopup'
import { generateXMLTableByInvoices } from '../Tool/XmlToolService'
import { handleError } from '../../common/helpers'
import { ACTION_VISIT_HISTORY, PROCESSING_STATUS } from './VisitConstant'
import { updateListItemService } from '../../common/services'
import lists from '../../common/lists'
import { usePatientVisitHistory } from './hooks/usePatientVisitHistory'
import useSSXmlTable from './hooks/useSSXmlTable'
import { useRuleManagement } from './hooks/useRuleManagement'
import { RULE_WARNING_STATUS, RULE_WARNING_TYPE } from './RuleManagement/RuleManagementConstant'
import { usePatientMedicalCoding } from './hooks/usePatientMedicalCoding'

// Keys for React Query

const propTypes = {
  mainVisit: PropTypes.object,
  selectedTabKey: PropTypes.string,
  currentPatientVisitMappingViews: PropTypes.array,
  selectedPatientVisitMappingViews: PropTypes.array,
  onSave: PropTypes.func,
  setSelectedTabKey: PropTypes.func,
}

const CheckConditionsTab = ({
  mainVisit,
  selectedTabKey,
  selectedPatientVisitMappingViews,
  onSave,
  setSelectedTabKey,
}) => {
  //hooks
  const ui = useUI()
  const queryClient = useQueryClient()
  const [checkConditionsForm] = Form.useForm()
  //constant
  const RULE_CHECK_RESULT = [
    { key: 1, nameL: 'Đã kiểm tra', checkedFlag: true },
    { key: 2, nameL: 'Chưa kiểm tra', checkedFlag: false },
  ]

  //state
  const { currentUser, modeViewData, isDebugMode } = useSelector((state) => state[MODULE_AUTH])
  const { RangePicker } = DatePicker
  const [isNoteVisible, setIsNoteVisible] = useState(false)
  const [entityType, setEntityType] = useState('')
  const [entity, setEntity] = useState(null)
  //state-react-query

  const {
    data: { currentPatientVisit },
    refetchPatientVisit,
  } = usePatientVisit(mainVisit?.patient_visit_id)
  const { addPatientVisitHistory } = usePatientVisitHistory()
  const { patientMedicalCodingList } = usePatientMedicalCoding(mainVisit?.patient_visit_id)

  const {
    ssTable1,
    allTables: allSSXmlTables,
    refetchAllSsXmlTables,
  } = useSSXmlTable(currentPatientVisit?.patient_visit_id)
  const {
    validatedRuleVisitDetailNlViews, // thay thế cho validatedRules
    ruleVisitItemDetailNlViewsData,
    isLoadingRuleVisitItemDetailNlViews: isLoading,
    patchRulesMutation,
    refetchRuleVisitItemDetailsData,
    saveRulesMutation,
    updateManualCheckedFlagMutation,
    deleteRulesMutation,
    updatePatientVisitWarningStatusMutation,
    updateAllSSXmlTablesWarningStatusMutation,
    syncRulesAndOthersMutation,
  } = useRuleManagement(
    mainVisit?.patient_visit_id,
    selectedPatientVisitMappingViews[0]?.patient_visit_id,
    currentPatientVisit,
    allSSXmlTables,
    patientMedicalCodingList,
  )

  // // Mutation to update manual_checked_flag
  // const updateManualCheckedFlagMutation = useMutation({
  //   mutationFn: async ({ ruleVisitItemDetailId, manualCheckedFlag, lu_user_id }) => {
  //     await updateManualCheckFlag(ruleVisitItemDetailId, manualCheckedFlag, lu_user_id)
  //   },
  //   onSuccess: (_, variables) => {
  //     // variables contains what was passed to the mutation
  //     const { ruleVisitItemDetailId, manualCheckedFlag } = variables

  //     // Create updated records
  //     const updatedRecord = ruleVisitItemDetailsDatasource?.map((item) =>
  //       item.rule_visit_item_detail_id === ruleVisitItemDetailId
  //         ? { ...item, key: item.key, manual_checked_flag: manualCheckedFlag }
  //         : item,
  //     )

  //     // Update ruleVisitItemDetailNlViewsData in cache
  //     queryClient.setQueryData(
  //       [
  //         VISIT_CHECK_CONDITIONS_QUERY_KEYS.RULE_VISIT_ITEM_DETAIL_NL_VIEW,
  //         mainVisit?.patient_visit_id,
  //         selectedPatientVisitMappingViews[0]?.patient_visit_id,
  //       ],
  //       updatedRecord,
  //     )

  //     // Update local component state
  //     setRuleVisitItemDetailsDatasource(updatedRecord)
  //   },
  //   onError: (error) => ui.notiError('Cập nhật thất bại'),
  // })

  const handleManualCheckChange = async (record, isChecked) => {
    try {
      await updateManualCheckedFlagMutation.mutateAsync({
        ruleVisitItemDetailId: record.rule_visit_item_detail_id,
        manualCheckedFlag: isChecked,
        lu_user_id: currentUser?.user_id,
      })

      // Cập nhật state local component
      const updatedRecord = validatedRuleVisitDetailNlViewsDatasource?.map((item) =>
        item.rule_visit_item_detail_id === record.rule_visit_item_detail_id
          ? { ...item, key: item.key, manual_checked_flag: isChecked }
          : item,
      )
      setValidatedRuleVisitDetailNlViewsDatasource(updatedRecord)
    } catch (error) {
      ui.notiError('Cập nhật trạng thái thất bại')
    }
  }

  const [selectedRuleCheckResult, setSelectedRuleCheckResult] = useState(null)
  const [groupedRuleVisitItemDetails, setGroupedRuleVisitItemDetails] = useState([])
  const [validatedRuleVisitDetailNlViewsDatasource, setValidatedRuleVisitDetailNlViewsDatasource] =
    useState([]) // thay thế cho const [ruleVisitItemDetailsDatasource, setRuleVisitItemDetailsDatasource] = useState([])

  useEffect(() => {
    if (validatedRuleVisitDetailNlViews && validatedRuleVisitDetailNlViews.length > 0) {
      let filteredValidatedRuleVisitItemDetails = validatedRuleVisitDetailNlViews.filter(
        (item) => !!item?.rule_visit_item_detail_id,
      )
      if (selectedRuleCheckResult?.key) {
        const filteredData = filteredValidatedRuleVisitItemDetails.filter(
          (item) => item?.manual_checked_flag == selectedRuleCheckResult?.checkedFlag,
        )
        filteredValidatedRuleVisitItemDetails = filteredData
      }

      handleSetRuleVisitItemDetailNlViewsData(filteredValidatedRuleVisitItemDetails)
    }
  }, [validatedRuleVisitDetailNlViews, selectedRuleCheckResult])
  const handleSetRuleVisitItemDetailNlViewsData = (data) => {
    const newData = data.map((item, index) => ({
      ...item,
      key: index,
    }))
    setValidatedRuleVisitDetailNlViewsDatasource(newData)
  }

  //functions : not finish yet !! Need to improve , rule has item need to group , rule non item don't need to group
  // const groupRulesVisitItemDetailsByRuleName = (ruleVisitItemDetailNlViews) => {
  //   const groupData = {}

  //   ruleVisitItemDetailNlViews.forEach((item, index) => {
  //     const ruleName = item.rule_name
  //     if (!groupData[ruleName]) {
  //       groupData[ruleName] = []
  //     }
  //     // Add the item to the group, including a `key` field based on the index
  //     groupData[ruleName].push({
  //       ...item,
  //       key: index,
  //       parent_with_children_rule_name: ruleName,
  //     })
  //   })

  //   return Object.keys(groupData).map((ruleName, index) => {
  //     const children = groupData[ruleName]
  //     return {
  //       key: `${index} - ${ruleName}`,
  //       parent_with_children_rule_name: ruleName,
  //       children: children,
  //     }
  //   })
  // }

  function getUniqueInvoiceNos(ruleItems) {
    //step 1: get unique invoice_no
    const uniqueInInvoices_ = [...new Set(ruleItems.map((item) => item.invoice_no_))]
    return uniqueInInvoices_
  }
  const handleGenerateXMLTableForEachInvoice = async (ruleItems) => {
    const uiniqueInvoiceNos = getUniqueInvoiceNos(ruleItems)
    try {
      // Process all invoices concurrently
      await Promise.all(
        uiniqueInvoiceNos.map(async (invoice) => {
          const invoiceArray = invoice.includes(';')
            ? invoice.split(';').map((i) => i.trim())
            : [invoice]

          const params = {
            filterInvoiceNo: invoiceArray,
            filterHN: currentPatientVisit?.visible_patient_id,
            patientVisitId: currentPatientVisit?.patient_visit_id,
          }

          // Call generateXMLTableByInvoices for this invoice number
          await generateXMLTableByInvoices(params, ui)
        }),
      )
    } catch (error) {
      handleError(error)
    }
  }

  const handleCheckedConfirmation = async () => {
    // Step 1: Check if there are any unchecked items in ruleVisitItemDetailsDatasource
    // Lấy các item XML
    const filteredItems = validatedRuleVisitDetailNlViewsDatasource.filter(
      (item) => item.rule_object === 'XML',
    )

    if (filteredItems.length == 0) {
      ui.notiError('Không có điều kiện XML nào được chọn để kiểm tra')
      return
    }

    // Kiểm tra nếu có bất kỳ XML item chưa được manual check
    const unchecked = filteredItems.filter((item) => item.manual_checked_flag === 0)
    if (filteredItems.length > 0 && unchecked.length > 0) {
      ui.notiError(
        `Có ${unchecked.length} điều kiện XML chưa được kiểm tra: ` +
          unchecked.map((i) => i.rule_name).join(', '),
      )
      return
    }

    try {
      // Step 2: Generate XML table
      await handleGenerateXMLTableForEachInvoice(filteredItems)
      // Step 3: Update processing status

      await updateListItemService(lists.patient_visit, mainVisit.patient_visit_id, {
        processing_status: PROCESSING_STATUS.WAITING_XML_CREATION.name_e,
        warning_status: RULE_WARNING_STATUS.NEW.key,
      })
      // Step 4: Add visit history
      const historyData = {
        patient_visit_id: selectedPatientVisitMappingViews[0]?.patient_visit_id,
        note: selectedPatientVisitMappingViews[0]?.note,
      }

      await addPatientVisitHistory.mutateAsync({
        historyData,
        action: ACTION_VISIT_HISTORY.CREATE_XML,
      })
      // Step 5: Trigger onSave and update tab key
      onSave()
      setSelectedTabKey('5')
    } catch (error) {
      handleError(error)
    }
  }

  const hasUncheckedXMLItems =
    validatedRuleVisitDetailNlViewsDatasource?.some(
      (item) => item.rule_object === 'XML' && item.manual_checked_flag == false,
    ) ||
    !validatedRuleVisitDetailNlViewsDatasource.filter((item) => item.rule_object === 'XML').length

  const getButtonCyanStyle = (disabled) => ({
    backgroundColor: disabled ? COLOR.disabled : COLOR.cyan,
    color: disabled ? 'black' : 'white',
  })

  const handleSyncRulesAndOthersData = async () => {
    try {
      ui.setLoading(true)
      await syncRulesAndOthersMutation.mutateAsync()
      await refetchPatientVisit()
      await refetchAllSsXmlTables()
    } catch (error) {
      ui.notiError('Đồng bộ rules thất bại: ' + (error.message || 'Lỗi không xác định'))
    } finally {
      ui.notiInfo('Đã hoàn tất đồng bộ rules')
    }
    ui.setLoading(false)
  }

  const handleDeleteRules = async () => {
    try {
      const idsToDelete = ruleVisitItemDetailNlViewsData.map(
        (rule) => rule.rule_visit_item_detail_id,
      )
      await deleteRulesMutation.mutateAsync(idsToDelete)
    } catch (error) {
      handleError(error)
    }
  }

  return (
    <div className="container-fluid">
      <div className="row mt-2 ">
        <div className="d-flex justify-content-md-end align-items-baseline gap-2">
          <span>Tổng rules: {validatedRuleVisitDetailNlViewsDatasource.length}</span>
          <AsyncButton
            disabled={hasUncheckedXMLItems}
            style={getButtonCyanStyle(hasUncheckedXMLItems)}
            onClick={handleCheckedConfirmation}>
            Xác nhận đã kiểm tra
          </AsyncButton>
          <Form form={checkConditionsForm}>
            <Form.Item label="Kết quả kiểm tra của người dùng" name="filterRuleCheckResult">
              <Select
                // optionLabelProp="label"
                allowClear
                className="flex-1 min-w-[250px]"
                placeholder="Chọn kết quả kiểm tra"
                onChange={(_, option) => {
                  setSelectedRuleCheckResult(option?.item)
                }}>
                {RULE_CHECK_RESULT.map((item, index) => (
                  <Select.Option key={item.key} value={item.nameL} item={item}>
                    {item?.nameL}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Form>
        </div>
        {validatedRuleVisitDetailNlViewsDatasource.length > 0 && (
          <Table
            size="small"
            bordered
            className="custom-table mt-2"
            dataSource={validatedRuleVisitDetailNlViewsDatasource}
            pagination={false}
            expandable={{
              rowExpandable: (record) => !!record.children?.length, // Only groups with children are expandable
              defaultExpandAllRows: true, // Automatically expand all rows
            }}
            columns={[
              {
                title: ' ',
                dataIndex: '',
                key: '',
                fixed: 'left',
                width: 0,
              },
              {
                title: 'Rule_rcd',
                dataIndex: 'rule_rcd',
                key: 'rule_rcd',
                fixed: 'left',
                width: 0,
              },
              {
                title: 'Tên điều kiện',
                dataIndex: 'rule_name',
                key: 'rule_name',
                fixed: 'left',
                width: 0,
              },
              {
                title: 'Loại điều kiện',
                dataIndex: 'warning_type',
                key: 'warning_type',
                fixed: 'center',
                align: 'center',
                render: (text, record) => {
                  return record.children ? (
                    <span style={{ fontStyle: 'italic' }}>{text || 'N/A'}</span> // Italic for group rows
                  ) : (
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        pointerEvents: 'none', // Prevents clicking
                      }}>
                      <Button
                        size="small"
                        icon={
                          <img
                            style={{ height: '18px' }}
                            src={RULE_WARNING_TYPE[text]?.img}
                            alt="warning"
                          />
                        }
                        style={{ border: 'none' }} // Removes the border color
                      ></Button>
                    </div>
                  )
                },
              },
              {
                title: 'Kết quả kiểm tra của hệ thống',
                dataIndex: 'system_checked_flag',
                key: 'system_checked_flag',
                fixed: 'center',
                align: 'center',
                render: (text, record) => {
                  // Show checkbox at group if no children, otherwise show at children

                  if (!record.children) {
                    return <Checkbox disabled={true} checked={record?.system_checked_flag} />
                  }
                  return null
                },
              },
              {
                title: 'Kết quả kiểm tra của người dùng',
                dataIndex: 'manual_checked_flag',
                key: 'manual_checked_flag',
                fixed: 'center',
                align: 'center',
                render: (_text, record) => {
                  // Show checkbox at group if no children, otherwise show at children
                  if (!record.children) {
                    return (
                      <Checkbox
                        onChange={(e) => handleManualCheckChange(record, e.target.checked)}
                        checked={record?.manual_checked_flag}
                      />
                    )
                  }
                  return null
                },
              },
              // {
              //   title: 'MALK',
              //   dataIndex: 'MA_LK',
              //   key: 'MA_LK',
              //   fixed: 'center',
              //   align: 'center',
              // },
              {
                title: 'RULE_OBJECT',
                dataIndex: 'rule_object',
                key: 'rule_object',
                fixed: 'center',
                align: 'center',
              },
              {
                title: 'Ghi chú',
                dataIndex: 'rule_name',
                key: 'rule_name',
                fixed: 'center',
                align: 'center',
                render: (text, record) => {
                  // Show button at group if no children, otherwise show at children
                  if (!record.children) {
                    return (
                      <div className="d-flex justify-content-center align-items-center">
                        <Button
                          style={{ border: 'none' }} // Removes the border color
                          onClick={() => {
                            setEntityType('rule')
                            setEntity(record)
                            setIsNoteVisible(true)
                          }}
                          type="default"
                          size="small"
                          icon={
                            <img style={{ height: '18px' }} src={imgNote} alt="Ghi chú" />
                          }></Button>
                      </div>
                    )
                  }
                  return null
                },
              },
            ]}></Table>
        )}
      </div>
      <div className="d-flex justify-content-end mt-2 gap-2">
        {isDebugMode && (
          <AsyncButton
            onClick={async () => {
              console.log('validatedRuleVisitDetailNlViews', validatedRuleVisitDetailNlViews)
            }}>
            Console log validatedRuleVisitDetailNlViews
          </AsyncButton>
        )}

        <Popconfirm title="Sure to cancel?" onConfirm={handleDeleteRules}>
          <AsyncButton icon={<i className="fas fa-trash"></i>}>Xóa Rules</AsyncButton>
        </Popconfirm>

        <AsyncButton
          icon={<i className="fas fa-sync-alt"></i>}
          onClick={async () => {
            try {
              await refetchAllSsXmlTables()
              await refetchPatientVisit()
              ui.notiSuccess('Tải lại bảng XML và visit thành công')
            } catch (error) {
              handleError(error)
            }
          }}>
          Tải lại bảng xml và visit
        </AsyncButton>

        <AsyncButton
          icon={<i className="fas fa-sync-alt"></i>}
          onClick={handleSyncRulesAndOthersData}
          type="primary"
          loading={patchRulesMutation.isLoading}>
          Kiểm tra và tạo Rules
        </AsyncButton>
      </div>
      <hr style={{ color: COLOR.yellow }}></hr>
      {/* // Render ở cuối: */}
      {isNoteVisible && (
        <NotePopup
          visible={isNoteVisible}
          onClose={() => setIsNoteVisible(false)}
          entityType={entityType}
          entity={entity}
        />
      )}
    </div>
  )
}

CheckConditionsTab.propTypes = propTypes

export default CheckConditionsTab
