import React, { useEffect, useMemo, useRef, useState } from 'react'
import {
  Button,
  Checkbox,
  Form,
  Col,
  Row,
  Typography,
  DatePicker,
  Table,
  Tabs,
  Breadcrumb,
  Modal,
  Popconfirm,
  Collapse,
  Popover,
  Tag,
  Dropdown,
  Image,
  Input,
  Select,
} from 'antd'
const { RangePicker } = DatePicker
import { HistoryOutlined, MenuOutlined } from '@ant-design/icons'
import { useLocation, useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { useUI } from '../../common/UIProvider'
import { displayDate, displayDateTime, handleError, handleSetDateValue } from '../../common/helpers'
import dayjs from '../../common/dayjs'
import {
  addMultiMergedPatientVisits,
  clearProcessingPersonForUser,
  getApiPatientVisitMedicalCodingViewListByPatientVisitId,
  getSystemReferralDispositionRefs,
} from './VisitService'
import COLOR from '../../common/color'
import CheckConditionsTab from './CheckConditionsTab'
import TaskListTab from './TaskListTab'
import ChargeDetailsTab from './ChargeDetailsTab'
import InsuranceInvoicesTab from './InsuranceInvoicesTab'
import XMLTablesListTab from './XMLTablesListTab'
import imgPatient from '../../assets/patient-icon.png'
import { TABLE_MERGE_VISIT_COLUMNS, PROCESSING_STATUS, ACTION_VISIT_HISTORY } from './VisitConstant'
import { useDispatch, useSelector } from 'react-redux'
import { MODULE_VISIT, visitActions } from '../../store/Visit'
import { FORM_MODE, FORMAT_DATE, MODE_VIEW_DATA } from '../../common/constant'
import PolicySubscriptionPopup from './PolicySubscriptionPopup'
import SearchAndAddVisitPopup from './SearchAndAddVisitPopup'
import MergeVisitDatasetTable from './MergeVisitDatasetTable'
import { MODULE_AUTH } from '../../store/auth'
import {
  deleteListItemService,
  getItemsService,
  updateListItemService,
} from '../../common/services'
import lists from '../../common/lists'
import VisitInformationPopup from './VisitInformationPopup'
import OTMSListTab from './OTMSListTab'
import { usePatientVisit } from './hooks/usePatientVisit'
import AsyncButton from '../../common/components/AsyncButton'
import { usePatientVisitHistory } from './hooks/usePatientVisitHistory'
import VisitTrackingHistoryPopup from './VisitTrackingHistoryPopup'
import endpoints from '../../common/endpoints'
import PatientMedicalCodingForm from './PatientMedicalCodingForm'
import { isEqual } from 'lodash'
import useDeepCompareEffect from 'use-deep-compare-effect'
import useApp from 'antd/es/app/useApp'
import MedicalRecordList from '../MedicalRecord/MedicalRecordList'
import { useAuth } from '../../common/AuthProvider'
import { PERMISSION } from '../Auth/AuthConstant'
import { getDisabledVisitDetailPage, validateKETLUAN } from './VisitHelpers'
import NotePopup from './NotePopup'
import config from '../../common/config'

const { Text } = Typography

const VisitDetailPage = () => {
  // hooks
  const ui = useUI()
  const { id } = useParams()
  const navigate = useNavigate()
  const location = useLocation()
  const [searchParams, setSearchParams] = useSearchParams()
  const dispatch = useDispatch()
  const app = useApp()
  const { checkPermission } = useAuth()

  // state
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])
  const { modeViewData } = useSelector((state) => state[MODULE_AUTH])

  const { visitDetailMode } = useSelector((state) => state[MODULE_VISIT])
  // const [bhytInfo] = useState(BHYT_INFO) // Commented out as it's not used

  const [selectedTabKey, setSelectedTabKey] = useState(searchParams.get('selectedTabKey'))

  const [isCheckedTreatCourse, setIsCheckedTreatCourse] = useState(false)
  const [isVisiblePolicySubscriptionPopup, setIsVisiblePolicySubscriptionPopup] = useState(true)
  const [selectedPatientVisitMappingViews, setSelectedPatientVisitMappingViews] = useState([
    // {
    //   patient_visit_id: searchParams.get('selectedPatientVisitId'),
    // },
  ])
  const [currentVisitDatasets, setCurrentVisitDatasets] = useState([])
  // Add this state to track previously merged visits
  const [hasMergedVisits, setHasMergedVisits] = useState(false)
  const [isShowMedicalCodingPopup, setIsShowMedicalCodingPopup] = useState(false)

  // MS confirmation states
  const [isVisibleMSConfirmModal, setIsVisibleMSConfirmModal] = useState(false)
  const [isVisibleMSConfirmHSBAModal, setIsVisibleMSConfirmHSBAModal] = useState(false)
  const [msConfirmNote, setMsConfirmNote] = useState('')
  const [msConfirmHSBANote, setMsConfirmHSBANote] = useState('')
  const [msConfirmForm] = Form.useForm()
  const [msConfirmHSBAForm] = Form.useForm()

  // Processing status change states
  const [isVisibleProcessingStatusModal, setIsVisibleProcessingStatusModal] = useState(false)
  const [selectedProcessingStatus, setSelectedProcessingStatus] = useState(null)

  // Use React Query hook for patient visit data
  const {
    data: { currentPatientVisit, currentPatientDataset, healthInsuranceCards },
    combinedPatientVisitMappingViews: currentPatientVisitMappingViews,
    refetchPatientVisit,
    refetchMergedPatientVisitMappingViews,
    refetchAll,
  } = usePatientVisit(id)

  // Rename for consistency
  const mainVisit = currentPatientVisit
  const firstSelectedVisit = selectedPatientVisitMappingViews?.[0]

  const { addPatientVisitHistory } = usePatientVisitHistory()

  const [isVisibleSearchAndAddVisitPopup, setIsVisibleSearchAndAddVisitPopup] = useState(false)
  const [isVisibleMergeVisitsPopup, setIsVisibleMergeVisitsPopup] = useState(false)
  const [isVisibleVisitInformationPopup, setIsVisibleVisitInformationPopup] = useState(false)
  const [isVisibleVisitTrackingHistoryPopup, setIsVisibleVisitTrackingHistoryPopup] =
    useState(false)
  const [selectedMergedPatientVisitIds, setSelectedMergedPatientVisitIds] = useState([])
  const [filterTreatCourseDate, setFilterTreatCourseDate] = useState([null, null])
  //state invoice
  const [isRefreshInvoice, setIsRefreshInvoice] = useState(true)
  //state note popup
  const [isNoteVisible, setIsNoteVisible] = useState(false)
  const [noteEntityType, setNoteEntityType] = useState('')
  const [noteEntity, setNoteEntity] = useState(null)
  // store
  const bottomPageRef = useRef(null)

  // direct const
  const { viewOnly, isNormalMode, defaultDisabled, isMSMode, disabledForSIO, disabledForMS } =
    getDisabledVisitDetailPage({
      visitDetailMode,
      modeViewData,
      processing_status: currentPatientVisit?.processing_status,
      checkPermission,
    })

  useEffect(() => {
    const oldData = {
      selectedTabKey: searchParams.get('selectedTabKey'),
      modeViewData: searchParams.get('modeViewData'),
      visitDetailMode: searchParams.get('visitDetailMode'),
      selectedPatientVisitId: searchParams.get('selectedPatientVisitId'),
    }

    const newData = {
      selectedTabKey: selectedTabKey || '3',
      modeViewData: modeViewData || MODE_VIEW_DATA.NORMAL,
      visitDetailMode: visitDetailMode || FORM_MODE.edit,
      selectedPatientVisitId:
        selectedPatientVisitMappingViews[0]?.patient_visit_id ||
        searchParams.get('selectedPatientVisitId'),
    }

    if (!isEqual(oldData, newData)) {
      setSearchParams(newData, {
        state: location.state,
      })
    }
  }, [selectedTabKey, modeViewData, visitDetailMode, selectedPatientVisitMappingViews[0]])

  // make sure selected items are still up to date
  useEffect(() => {
    let newSelected = [...selectedPatientVisitMappingViews].filter((r) => !!r.patient_visit_id)
    if (newSelected.length === 0) {
      newSelected = [{ patient_visit_id: searchParams.get('selectedPatientVisitId') }]
    }

    setSelectedPatientVisitMappingViews([
      ...newSelected.map((item) => ({
        ...currentPatientVisitMappingViews.find(
          (view) => view.patient_visit_id === item.patient_visit_id,
        ),
      })),
    ])
  }, [currentPatientVisitMappingViews])

  const handleSetProcessingPersonOff = async () => {
    try {
      // Clear processing_person for all visits associated with the current user
      if (currentUser?.User_id) {
        await clearProcessingPersonForUser(currentUser.User_id)
      } else {
        // Fallback to just clearing the current visit if user ID is not available
        // Get the selected patient visit ID from the URL parameters
        const selectedPatientVisitId = searchParams.get('selectedPatientVisitId')

        // If there's a selected patient visit ID, clear the processing person for that visit as well
        if (selectedPatientVisitId && selectedPatientVisitId !== id) {
          await updateListItemService(lists.patient_visit, selectedPatientVisitId, {
            processing_person_id: null,
            processing_person_name: null,
          })
        }

        // Clear the processing person for the main visit
        await updateListItemService(lists.patient_visit, id, {
          processing_person_id: null,
          processing_person_name: null,
        })
      }

      dispatch(visitActions.setVisitDetailMode(FORM_MODE.edit))
    } catch (error) {
      handleError(error)
    }
  }

  useEffect(() => {
    if (bottomPageRef.current) {
      bottomPageRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' })
    }

    const handleBeforeUnload = (event) => {
      // Nếu tab đóng (không phải reload hoặc cancel popup)
      if (!event.defaultPrevented) {
        handleSetProcessingPersonOff()
      }
    }

    const handleUnload = () => {
      handleSetProcessingPersonOff() // Đảm bảo chạy ngay trước khi tab đóng
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('unload', handleUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('unload', handleUnload)
      handleSetProcessingPersonOff()
    }
  }, [])
  // Hàm thay đổi trạng thái checkbox ngoài bảng
  const handleCheckboxChange = async (e) => {
    const isChecked = e.target.checked

    if (!isChecked && hasMergedVisits) {
      app.modal.confirm({
        title: 'Xác nhận',
        content: 'Bạn có muốn hủy các lượt khám đã merge trước đó?',
        okText: 'Đồng ý',
        cancelText: 'Hủy',
        onOk: async () => {
          // Remove merged visits logic here
          try {
            await Promise.all(
              currentPatientVisitMappingViews
                .filter((visit) => visit.parent_patient_visit_id)
                .map(async (visit) => {
                  await deleteListItemService(lists.patient_visit, visit.patient_visit_id)

                  // tracking history
                  addPatientVisitHistory.mutateAsync({
                    historyData: {
                      patient_visit_id: currentPatientVisit?.patient_visit_id,
                      note: `Bỏ lượt khám ${visit.visit_code} ra khỏi đợt`,
                    },
                    action: ACTION_VISIT_HISTORY.EJECT_MERGED_VISIT,
                  })
                }),
            )

            await refetchAll()
            setIsCheckedTreatCourse(false)
            setHasMergedVisits(false)
            setSelectedPatientVisitMappingViews([currentPatientVisit])
            ui.notiSuccess('Đã hủy các lượt khám merge thành công')
          } catch (error) {
            handleError(error)
          }
        },
        onCancel: () => {
          setIsCheckedTreatCourse(true) // Keep checkbox checked if user cancels
        },
      })
    } else {
      setIsCheckedTreatCourse(isChecked)

      // If checkbox is checked but no dates are set, default them to start and end of the visit month
      if (isChecked && (!filterTreatCourseDate[0] || !filterTreatCourseDate[1])) {
        if (currentPatientVisit?.actual_visit_datetime) {
          // Get visit date's month start and end
          const visitDate = dayjs(currentPatientVisit.actual_visit_datetime)
          const startOfMonth = visitDate.startOf('month')
          const endOfMonth = visitDate.endOf('month')

          setFilterTreatCourseDate([startOfMonth, endOfMonth])
        }
      }
    }
  }

  //Get ReferralDisposition
  const handleGetSystemReferralDispositionRefs = async () => {
    const data = await getSystemReferralDispositionRefs()
    dispatch(visitActions.setSystemReferralDispositionRefList(data.value))

    return data
  }

  //Get api_patient_visit_medical_coding_view
  const handleGetApiPatientVisitMedicalCodingViewListByPatientVisitId = async (id) => {
    const data = await getApiPatientVisitMedicalCodingViewListByPatientVisitId(id)
    dispatch(visitActions.setCurrentApiPatientVisitMedicalCodingViewList(data.value))

    return data
  }

  // Hàm đóng modal
  const handleCloseModal = () => {
    setIsVisibleSearchAndAddVisitPopup(false)
  }

  const tabItems = [
    {
      key: '1',
      label: 'Kiểm tra điều kiện',
      children: (
        <CheckConditionsTab
          selectedTabKey={selectedTabKey}
          mainVisit={currentPatientVisit}
          selectedPatientVisitMappingViews={selectedPatientVisitMappingViews} // Pass the prop
          currentPatientVisitMappingViews={currentPatientVisitMappingViews} // Pass the prop
          onSave={() => refetchPatientVisit()}
          setSelectedTabKey={setSelectedTabKey}></CheckConditionsTab>
      ),
      visible: modeViewData === MODE_VIEW_DATA.NORMAL,
    },
    {
      key: '2',
      label: 'Danh sách Task',
      children: <TaskListTab></TaskListTab>,
      visible: modeViewData === MODE_VIEW_DATA.NORMAL,
    },
    {
      key: '3',
      label: 'Danh sách chi tiết charge',
      children: (
        <ChargeDetailsTab
          selectedTabKey={selectedTabKey}
          mainVisit={currentPatientVisit}
          selectedPatientVisitMappingViews={selectedPatientVisitMappingViews} // Pass the prop
          currentPatientVisitMappingViews={currentPatientVisitMappingViews} // Pass the prop
          onSave={() => {}} // do nothing
          setIsVisiblePolicySubscriptionPopup={setIsVisiblePolicySubscriptionPopup}
        />
      ),
      visible: modeViewData === MODE_VIEW_DATA.NORMAL || modeViewData === MODE_VIEW_DATA.CASHIER,
    },
    {
      key: '4',
      label: 'Danh sách hóa đơn do BHYT chi trả',
      children: (
        <InsuranceInvoicesTab
          selectedTabKey={selectedTabKey}
          currentPatientVisitMappingViews={currentPatientVisitMappingViews}
          mainVisit={currentPatientVisit}
          selectedPatientVisitMappingViews={
            selectedPatientVisitMappingViews.length === 0
              ? [currentPatientVisit]
              : selectedPatientVisitMappingViews
          } // Pass the prop
          isRefreshOnline={isRefreshInvoice}
          setIsRefreshOnline={setIsRefreshInvoice}
          onSave={() => refetchPatientVisit()}
          setSelectedTabKey={setSelectedTabKey}
          // Pass the prop
        ></InsuranceInvoicesTab>
      ),
      visible: modeViewData === MODE_VIEW_DATA.NORMAL || modeViewData === MODE_VIEW_DATA.CASHIER,
    },
    {
      key: '5',
      label: 'Danh sách các Bảng',
      children: (
        <XMLTablesListTab
          selectedTabKey={selectedTabKey}
          selectedPatientVisitMappingViews={selectedPatientVisitMappingViews} // Pass the prop
          currentPatientVisit={currentPatientVisit}
          refetchPatientVisit={() => refetchPatientVisit()}
          onSave={() => refetchPatientVisit()}
          // Pass the prop
        ></XMLTablesListTab>
      ),
      visible: modeViewData === MODE_VIEW_DATA.NORMAL,
    },
    {
      key: '6',
      label: 'OT-MS Xử lý',
      children: (
        <OTMSListTab
          selectedVisit={firstSelectedVisit}
          currentPatientDataset={currentPatientDataset}
          mainVisit={mainVisit}></OTMSListTab>
      ),
      visible: [MODE_VIEW_DATA.OT, MODE_VIEW_DATA.MS, MODE_VIEW_DATA.NORMAL].includes(modeViewData),
    },
    {
      key: 'medicalRecords',
      label: 'Hồ sơ bệnh án',
      visible: [MODE_VIEW_DATA.MS, MODE_VIEW_DATA.NORMAL].includes(modeViewData),
      children: useMemo(() => {
        if (
          !selectedPatientVisitMappingViews[0]?.patient_visit_id ||
          !currentPatientVisit?.patient_visit_id
        ) {
          return null
        }

        return (
          <MedicalRecordList
            selectedVisit={firstSelectedVisit}
            mainVisit={currentPatientVisit}
            patientId={currentPatientVisit?.patient_id}
          />
        )
      }, [
        selectedPatientVisitMappingViews[0]?.patient_visit_id,
        currentPatientVisit?.patient_visit_id,
      ]),
    },
  ]

  const menuItems = [
    {
      key: '1',
      label: 'Lịch sử',
      visible: true,
      icon: <HistoryOutlined />,
      onClick: () => {
        setIsVisibleVisitTrackingHistoryPopup(true)
      },
    },
    {
      key: '2',
      label: 'Xem mã bệnh chính',
      visible: [MODE_VIEW_DATA.NORMAL, MODE_VIEW_DATA.MS].includes(modeViewData),
      icon: <i className="fa-solid fa-stethoscope"></i>,
      onClick: () => {
        setIsShowMedicalCodingPopup(true)
      },
    },
    {
      key: '3',
      label: 'Ghi chú theo lượt khám',
      visible: [MODE_VIEW_DATA.NORMAL, MODE_VIEW_DATA.MS].includes(modeViewData),
      icon: <i className="fa-solid fa-notes-medical"></i>,
      onClick: () => {
        setIsNoteVisible(true)
        setNoteEntity(
          currentPatientVisitMappingViews.filter((visit) => visit.patient_visit_id === id)[0],
        )
        setNoteEntityType('patient_visit')
      },
    },
  ]

  const handleCheckProcessingPerson = async (id) => {
    const processingPerson = await getItemsService(lists.patient_visit_mapping_view, {
      filter: `patient_visit_id eq ${id}`,
    }).then((res) => res?.value[0])
    if (
      processingPerson?.processing_person_id &&
      processingPerson?.processing_person_name &&
      processingPerson?.processing_person_id !== currentUser?.User_id
    ) {
      //hiển thị popup ghi đè hay ko
      app.modal.confirm({
        title: 'Thông báo',
        content: `Thông tin đang được xử lý bởi ${processingPerson.processing_person_name}. Bạn có muốn ghi đè không?`,
        okText: 'Ghi đè',
        cancelText: 'Chỉ xem',
        onOk: async () => {
          await updateListItemService(lists.patient_visit, processingPerson.patient_visit_id, {
            processing_person_id: currentUser?.User_id,
            processing_person_name: currentUser?.User_name,
          })
        },
        onCancel: () => {
          dispatch(visitActions.setVisitDetailMode(FORM_MODE.view))
        },
      })
    } else {
      await updateListItemService(lists.patient_visit, processingPerson.patient_visit_id, {
        processing_person_id: currentUser?.User_id,
        processing_person_name: currentUser?.User_name,
      })
    }
  }

  // listen currentPatientVisit
  useDeepCompareEffect(() => {
    if (currentPatientVisit) {
      setIsCheckedTreatCourse(currentPatientVisit?.treatment_course_flag)

      // Default treatment course dates if they're not set
      let startDate, endDate

      if (currentPatientVisit?.treatment_course_start_date) {
        startDate = handleSetDateValue(currentPatientVisit.treatment_course_start_date)
      } else if (currentPatientVisit?.actual_visit_datetime) {
        // Default to start of visit month
        const visitDate = dayjs(currentPatientVisit.actual_visit_datetime)
        startDate = visitDate.startOf('month')
      } else {
        startDate = null
      }

      if (currentPatientVisit?.treatment_course_end_date) {
        endDate = handleSetDateValue(currentPatientVisit.treatment_course_end_date)
      } else if (currentPatientVisit?.actual_visit_datetime) {
        // Default to end of visit month
        const visitDate = dayjs(currentPatientVisit.actual_visit_datetime)
        endDate = visitDate.endOf('month')
      } else {
        endDate = null
      }

      setFilterTreatCourseDate([startDate, endDate])
    }
  }, [currentPatientVisit])

  const handleGetCurrentVisitDataset = async () => {
    let filter = []
    if (currentPatientDataset?.Patient_id) {
      filter.push(`patient_id eq ${currentPatientDataset?.Patient_id}`)
    }

    // Ensure we have valid dates for filtering
    let startDate = filterTreatCourseDate?.[0]
    let endDate = filterTreatCourseDate?.[1]

    // If dates are not set, use the visit date's month
    if (!startDate || !endDate) {
      if (currentPatientVisit?.actual_visit_datetime) {
        const visitDate = dayjs(currentPatientVisit.actual_visit_datetime)
        startDate = startDate || visitDate.startOf('month')
        endDate = endDate || visitDate.endOf('month')
      }
    }

    if (startDate && endDate) {
      filter.push(
        `visit_start ge ${startDate.format(FORMAT_DATE)} and visit_end le ${endDate.format(FORMAT_DATE)}`,
      )
    }

    try {
      const data = await getItemsService(lists.visit_dataset, {
        filter: filter.join(' and '),
        top: 2000,
        count: false,
        orderBy: 'visit_start desc',
      })
      setCurrentVisitDatasets(data.value)
      return data
    } catch (error) {
      handleError(error)
    }
  }

  const handleUpdateTreamentCourse = async (ignoreNoti = false) => {
    try {
      // Ensure we have valid dates before updating
      let startDate = filterTreatCourseDate[0]
      let endDate = filterTreatCourseDate[1]

      // If dates are not set, use the visit date's month
      if (!startDate || !endDate) {
        if (currentPatientVisit?.actual_visit_datetime) {
          const visitDate = dayjs(currentPatientVisit.actual_visit_datetime)
          startDate = startDate || visitDate.startOf('month')
          endDate = endDate || visitDate.endOf('month')
        }
      }

      await updateListItemService(lists.patient_visit, currentPatientVisit.patient_visit_id, {
        treatment_course_flag: isCheckedTreatCourse,
        treatment_course_start_date: dayjs(startDate).format(FORMAT_DATE),
        treatment_course_end_date: dayjs(endDate).endOf('day').add(-1, 'minute'),
      })

      // Update local state to reflect the changes
      if (!filterTreatCourseDate[0] || !filterTreatCourseDate[1]) {
        setFilterTreatCourseDate([startDate, endDate])
      }

      !ignoreNoti && ui.notiSuccess('Cập nhật thông tin điều trị thành công.')
    } catch (error) {
      handleError(error)
    }
  }

  // MS confirmation handlers
  const handleMSConfirm = async () => {
    try {
      let currentChargeList = await getItemsService(lists.visit_charge_detail_view, {
        filter: `patient_visit_id eq ${selectedPatientVisitMappingViews[0]?.patient_visit_id} and manual_ss_cover_flag eq true`,
      })
      currentChargeList = currentChargeList.value || []

      const isValidKETLUAN = validateKETLUAN(currentChargeList, ui)
      if (!isValidKETLUAN) {
        return
      }

      // Update the processing status
      await updateListItemService(lists.patient_visit, currentPatientVisit.patient_visit_id, {
        processing_status: PROCESSING_STATUS.WAITING_TO_CREATE_XML.name_e,
      })

      // Add to patient visit history with note
      await addPatientVisitHistory.mutateAsync({
        historyData: {
          patient_visit_id: currentPatientVisit?.patient_visit_id,
          merged_patient_visit_id: selectedPatientVisitMappingViews[0]?.patient_visit_id,
          note: msConfirmNote,
        },
        action: ACTION_VISIT_HISTORY.MS_CONFIRM,
      })

      // Close modal and reset form
      setIsVisibleMSConfirmModal(false)
      msConfirmForm.resetFields()
      setMsConfirmNote('')

      // Refresh data
      await refetchPatientVisit()

      // Show success notification
      ui.notiSuccess('MS đã xác nhận thành công')
    } catch (error) {
      handleError(error)
      ui.notiError('Có lỗi xảy ra khi xác nhận')
    }
  }

  const handleMSConfirmHSBA = async () => {
    try {
      // Update the processing status to WAITING_HSBA
      await updateListItemService(lists.patient_visit, currentPatientVisit.patient_visit_id, {
        processing_status: PROCESSING_STATUS.WAITING_HSBA.name_e,
      })

      // Add to patient visit history with note
      await addPatientVisitHistory.mutateAsync({
        historyData: {
          patient_visit_id: currentPatientVisit?.patient_visit_id,
          merged_patient_visit_id: selectedPatientVisitMappingViews[0]?.patient_visit_id,
          note: msConfirmHSBANote,
        },
        action: ACTION_VISIT_HISTORY.MS_CONFIRM_HSBA,
      })

      // Close modal and reset form
      setIsVisibleMSConfirmHSBAModal(false)
      msConfirmHSBAForm.resetFields()
      setMsConfirmHSBANote('')

      // Refresh data
      await refetchPatientVisit()

      // Show success notification
      ui.notiSuccess('MS xác nhận đang hoàn thành HSBA thành công')
    } catch (error) {
      handleError(error)
      ui.notiError('Có lỗi xảy ra khi xác nhận')
    }
  }

  const handleCancelMSConfirm = () => {
    setIsVisibleMSConfirmModal(false)
    msConfirmForm.resetFields()
    setMsConfirmNote('')
  }

  const handleCancelMSConfirmHSBA = () => {
    setIsVisibleMSConfirmHSBAModal(false)
    msConfirmHSBAForm.resetFields()
    setMsConfirmHSBANote('')
  }

  // Handle processing status change
  const handleChangeProcessingStatus = () => {
    // Set initial status and show modal
    setSelectedProcessingStatus(currentPatientVisit?.processing_status)
    setIsVisibleProcessingStatusModal(true)
  }

  const handleUpdateProcessingStatus = async () => {
    try {
      if (!selectedProcessingStatus) {
        ui.notiError('Vui lòng chọn trạng thái')
        return
      }

      // Update the processing status
      await updateListItemService(lists.patient_visit, currentPatientVisit.patient_visit_id, {
        processing_status: selectedProcessingStatus,
      })

      // Add to patient visit history with note
      await addPatientVisitHistory.mutateAsync({
        historyData: {
          patient_visit_id: currentPatientVisit?.patient_visit_id,
          merged_patient_visit_id: selectedPatientVisitMappingViews[0]?.patient_visit_id,
          note: `Thay đổi trạng thái thành ${PROCESSING_STATUS[selectedProcessingStatus]?.name_l}`,
        },
        action: ACTION_VISIT_HISTORY.SAVE_INFO,
      })

      // Close modal
      setIsVisibleProcessingStatusModal(false)

      // Refresh data
      await refetchPatientVisit()

      // Show success notification
      ui.notiSuccess('Cập nhật trạng thái thành công')
    } catch (error) {
      handleError(error)
      ui.notiError('Có lỗi xảy ra khi cập nhật trạng thái')
    }
  }

  const handleCancelProcessingStatus = () => {
    setIsVisibleProcessingStatusModal(false)
  }

  useEffect(() => {
    handleGetSystemReferralDispositionRefs()
    handleCheckProcessingPerson(id)
    handleGetApiPatientVisitMedicalCodingViewListByPatientVisitId(id)
  }, [])

  const handleSelectMergedVisit = (selectedRowKeys) => {
    const updatedViews = currentPatientVisitMappingViews.map((view) => ({
      ...view,
      checked_flag: selectedRowKeys.includes(view.patient_visit_id),
    }))

    // Always set the state with the filtered views, even if it's an empty array
    // This ensures that when visit_charge_detail_view returns empty data, we don't show old data
    const filteredViews = updatedViews.filter((view) => view.checked_flag)

    // If no views are selected, default to the current patient visit
    if (filteredViews.length === 0 && currentPatientVisit) {
      setSelectedPatientVisitMappingViews([currentPatientVisit])
    } else {
      setSelectedPatientVisitMappingViews(filteredViews)
    }
  }

  // Add useEffect to check for merged visits
  useEffect(() => {
    const mergedVisitsExist = currentPatientVisitMappingViews.some(
      (visit) => visit.parent_patient_visit_id,
    )
    setHasMergedVisits(mergedVisitsExist)
  }, [currentPatientVisitMappingViews])

  return (
    <div className="container-fluid">
      <div>
        <div className="sticky-top z-10">
          <div className="d-flex align-items-center justify-content-between py-2">
            <Breadcrumb
              items={[
                {
                  title: (
                    <div
                      className="cursor-pointer"
                      onClick={() => {
                        navigate(location?.state?.backUrl || '/his/visit')
                      }}>
                      Danh sách lượt khám
                    </div>
                  ),
                },
                {
                  title: 'Chi tiết lượt khám',
                },
              ]}
            />
            <div className="d-flex gap-2">
              <Popconfirm
                title="Bạn có chắc chắn muốn xóa?"
                onConfirm={async () => {
                  if (selectedPatientVisitMappingViews.length > 1) {
                    ui.notiError('Có lượt khám được merge, không thể xóa')
                    return
                  }

                  await Promise.all(
                    healthInsuranceCards.map((card) =>
                      deleteListItemService(
                        lists.health_insurance_card,
                        card.health_insurance_card_id,
                      ),
                    ),
                  )

                  await deleteListItemService(
                    lists.patient_visit,
                    currentPatientVisit.patient_visit_id,
                  )
                  navigate(location?.state?.backUrl || '/his/visit')
                  ui.notiSuccess('Xóa lượt khám thành công')
                }}>
                <AsyncButton
                  disabled={
                    (disabledForMS && disabledForSIO) || currentPatientVisitMappingViews.length > 1
                  }
                  icon={<i className="fa fa-trash" />}
                  danger>
                  Xóa lượt khám
                </AsyncButton>
              </Popconfirm>

              <Button
                hidden={!isNormalMode}
                disabled={disabledForSIO}
                variant="solid"
                color="green"
                icon={<i className="fa fa-info-circle" />}
                type="primary"
                onClick={() => setIsVisibleVisitInformationPopup(true)}>
                Thông tin lượt khám
              </Button>

              <Button
                hidden={!isNormalMode && !isMSMode}
                disabled={disabledForSIO && disabledForMS}
                icon={<i className="fa-solid fa-address-card"></i>}
                variant="solid"
                color="cyan"
                onClick={() => setIsVisibleSearchAndAddVisitPopup(true)}>
                Thông tin đăng kí và thẻ
              </Button>

              <Popover content="Sau khi đã điền thông tin, MS xác nhận để chuyển sang bước tiếp theo">
                <AsyncButton
                  type="primary"
                  hidden={
                    modeViewData !== MODE_VIEW_DATA.MS ||
                    (currentPatientVisit?.processing_status !==
                      PROCESSING_STATUS.WAITING_MS.name_e &&
                      currentPatientVisit?.processing_status !==
                        PROCESSING_STATUS.WAITING_HSBA.name_e)
                  }
                  onClick={() => setIsVisibleMSConfirmModal(true)}
                  icon={<i className="fa-solid fa-check-circle"></i>}
                  variant="solid"
                  color="cyan">
                  MS Xác nhận
                </AsyncButton>
              </Popover>

              <Popover content="Xác nhận đang hoàn thành HSBA">
                <AsyncButton
                  type="primary"
                  hidden={
                    modeViewData !== MODE_VIEW_DATA.MS ||
                    currentPatientVisit?.processing_status !== PROCESSING_STATUS.WAITING_MS.name_e
                  }
                  onClick={() => setIsVisibleMSConfirmHSBAModal(true)}
                  icon={<i className="fa-solid fa-file-medical"></i>}
                  variant="solid"
                  color="green">
                  Xác nhận đang hoàn thành HSBA
                </AsyncButton>
              </Popover>

              <Dropdown
                disabled={viewOnly}
                menu={{
                  items: menuItems
                    .filter((item) => item.visible)
                    .map((item) => ({ ...item, visible: '' })),
                }}
                trigger={['click']}>
                <Button icon={<MenuOutlined />} />
              </Dropdown>
            </div>
          </div>
          <div className="d-flex gap-2 align-items-center">
            {/* Icon Avatar */}
            <div>
              <Image
                src={
                  currentPatientDataset?.photoID
                    ? endpoints.patientPhoto(currentPatientDataset?.photoID)
                    : undefined
                }
                fallback={imgPatient}
                width={50}
                className="profile-avatar"
                style={{
                  imageOrientation: 'none',
                  objectFit: 'cover',
                }}
              />
            </div>

            {/* Patient Info */}
            <div className="d-flex flex-wrap">
              <Text className="text-2xl font-semibold">
                {currentPatientVisit?.fullname} (
                {currentPatientVisit?.sex === 'Male' ? 'Mr.' : 'Mrs.'})
              </Text>
              <div className="d-flex align-items-center">
                <Text className="text-md mx-2">|</Text>
                <Text type="secondary" className="text-md">
                  Mã bệnh nhân:
                </Text>
                <Text className="text-md ml-1">{currentPatientVisit?.visible_patient_id}</Text>
              </div>
              <div className="d-flex align-items-center">
                <Text className="text-md mx-2">|</Text>
                <Text type="secondary" className="text-md">
                  Ngày sinh:
                </Text>
                <Text className="text-md ml-1">{displayDate(currentPatientVisit?.dob)}</Text>
              </div>
              <div className="d-flex align-items-center">
                <Text className="text-md mx-2">|</Text>
                <Text type="secondary" className="text-md">
                  Giới tính:
                </Text>
                <Text className="text-md ml-1"> {currentPatientVisit?.sex}</Text>
              </div>
              <div className="d-flex align-items-center">
                <Text className="text-md mx-2">|</Text>
                <Text type="secondary" className="text-md">
                  Visit:
                </Text>
                <Text className="text-md ml-1">
                  {currentPatientVisit?.visit_code} -{' '}
                  {displayDateTime(currentPatientVisit?.actual_visit_datetime)}
                </Text>
              </div>
              <div className="d-flex align-items-center">
                <Text className="text-md mx-2">|</Text>
                <Text type="secondary" className="text-md">
                  Trạng thái:
                </Text>
                <Tag
                  className="text-lg px-2 py-1 ms-1 fw-bold cursor-pointer hover:opacity-80"
                  color="blue"
                  onClick={
                    checkPermission(PERMISSION.ADMIN) || config.IN_TEST
                      ? handleChangeProcessingStatus
                      : null
                  }>
                  {PROCESSING_STATUS[currentPatientVisit?.processing_status]?.name_l}
                </Tag>
              </div>
            </div>
          </div>
          <Row className=" flex-1 flex items-center justify-end">
            <Col className="flex gap-2 items-center">
              {/* <Tag className="text-sm font-semibold items-center flex gap-2" color="cyan">
                {PROCESSING_STATUS[currentPatientVisit?.processing_status]?.name_l}
              </Tag> */}
            </Col>
          </Row>
          <div className="border-t mt-1"></div>
        </div>

        <Row>
          <Collapse
            size="small"
            defaultActiveKey={
              [MODE_VIEW_DATA.CASHIER, MODE_VIEW_DATA.OT, MODE_VIEW_DATA.MS].includes(modeViewData)
                ? []
                : [1]
            }
            expandIconPosition="end"
            className="w-full mt-2"
            items={[
              {
                key: 1,
                label: (
                  <label className="font-semibold">
                    Danh sách lượt khám -{' '}
                    {!selectedPatientVisitMappingViews[0] && (
                      <Tag color="orange">Vui lòng chọn lượt khám</Tag>
                    )}
                    {selectedPatientVisitMappingViews[0] && (
                      <Tag>
                        {selectedPatientVisitMappingViews[0]?.visit_code +
                          ' - ' +
                          displayDateTime(
                            selectedPatientVisitMappingViews[0]?.actual_visit_datetime,
                          )}
                      </Tag>
                    )}
                    {selectedPatientVisitMappingViews.length > 1 && (
                      <Tag color="blue">
                        {selectedPatientVisitMappingViews.length} lượt khám được chọn
                      </Tag>
                    )}
                  </label>
                ),
                children: (
                  <div className="flex flex-col gap-3 ">
                    <div className="flex items-center gap-2">
                      <div>
                        <Checkbox
                          disabled={disabledForSIO}
                          checked={isCheckedTreatCourse}
                          className="text-md"
                          onChange={handleCheckboxChange}>
                          THEO ĐỢT ĐIỀU TRỊ
                        </Checkbox>
                      </div>
                      {isCheckedTreatCourse && (
                        <div>
                          <RangePicker
                            disabled={disabledForSIO}
                            size="small"
                            value={filterTreatCourseDate}
                            onChange={(value) => setFilterTreatCourseDate(value)}
                            style={{ width: '100%' }}
                            format={FORMAT_DATE}
                            allowEmpty={[false, false]}
                          />
                        </div>
                      )}

                      <AsyncButton
                        hidden={!isNormalMode}
                        disabled={disabledForSIO}
                        onClick={async () => {
                          const historyData = {
                            patient_visit_id: currentPatientVisit?.patient_visit_id,
                            note: currentPatientVisit?.note,
                          }

                          addPatientVisitHistory.mutateAsync({
                            historyData,
                            action: ACTION_VISIT_HISTORY.SAVE_INFO,
                          })
                          await handleUpdateTreamentCourse()
                        }}
                        size="small">
                        Lưu
                      </AsyncButton>

                      <AsyncButton
                        hidden={!isNormalMode}
                        disabled={disabledForSIO}
                        onClick={async () => {
                          await handleUpdateTreamentCourse(true)
                          await handleGetCurrentVisitDataset()
                          setIsVisibleMergeVisitsPopup(true)
                        }}
                        type="primary"
                        size="small">
                        Merge
                      </AsyncButton>
                    </div>

                    <Col>
                      <Table
                        size="small"
                        rowKey={'patient_visit_id'}
                        scroll={{ y: 200 }}
                        pagination={false}
                        rowSelection={{
                          type: ['3', '6', 'medicalRecords'].includes(selectedTabKey)
                            ? 'radio'
                            : 'checkbox',
                          onChange: handleSelectMergedVisit,
                          selectedRowKeys: selectedPatientVisitMappingViews.map(
                            (item) => item.patient_visit_id,
                          ),
                          // getCheckboxProps: (record) => ({
                          //   disabled: record.parent_patient_visit_id == null
                          // })
                        }}
                        dataSource={currentPatientVisitMappingViews.map((item) => ({
                          key: item.patient_visit_id, // Make sure each row has a unique key
                          visit: `${item.visit_code} - ${displayDateTime(
                            item.actual_visit_datetime,
                          )}`,
                          visit_type: `${item.visit_type_name_e}`,
                          checked_flag: item.checked_flag,
                          patient_visit_id: item.patient_visit_id,
                          ...item,
                        }))}
                        className="custom-table"
                        columns={[
                          ...TABLE_MERGE_VISIT_COLUMNS,
                          {
                            dataIndex: 'action',
                            key: 'action',
                            render: (_, record) => (
                              <div className="d-flex gap-1">
                                <div>
                                  {record.patient_visit_id ===
                                    currentPatientVisit.patient_visit_id &&
                                    !currentPatientVisit?.parent_patient_visit_id && (
                                      <Popover content="Lượt khám gốc">
                                        <Tag color="blue">P</Tag>
                                      </Popover>
                                    )}
                                  {record.patient_visit_id !==
                                    currentPatientVisit.patient_visit_id && (
                                    <Popover content="Lượt khám trong đợt">
                                      <Tag>S</Tag>
                                    </Popover>
                                  )}
                                </div>
                                <Popconfirm
                                  title="Bạn có chắc chắn muốn bỏ lượt khám này ra khỏi đợt?"
                                  okText="Yes"
                                  cancelText="No"
                                  onConfirm={async () => {
                                    try {
                                      await deleteListItemService(
                                        lists.patient_visit,
                                        record.patient_visit_id,
                                      )

                                      // tracking history
                                      addPatientVisitHistory.mutateAsync({
                                        historyData: {
                                          patient_visit_id: currentPatientVisit?.patient_visit_id,
                                          merged_patient_visit_id: record.patient_visit_id,
                                          note: `Bỏ lượt khám ${record.visit_code} ra khỏi đợt`,
                                        },
                                        action: ACTION_VISIT_HISTORY.EJECT_MERGED_VISIT,
                                      })

                                      // not sure to delete it or clear parent
                                      // await updateListItemService(
                                      //   lists.patient_visit,
                                      //   record.patient_visit_id,
                                      //   {
                                      //     parent_patient_visit_id: null,
                                      //   },
                                      // )

                                      refetchMergedPatientVisitMappingViews()
                                      setIsRefreshInvoice(true)
                                    } catch (error) {
                                      handleError(error)
                                    }
                                  }}>
                                  <Popover content="Bỏ lượt khám ra khỏi đợt">
                                    <Button
                                      disabled={
                                        (disabledForMS && disabledForSIO) ||
                                        mainVisit.patient_visit_id === record.patient_visit_id
                                      }
                                      size="small"
                                      hidden={record.parent_patient_visit_id == null}
                                      danger
                                      icon={<i className="fa-solid fa-eject"></i>}></Button>
                                  </Popover>
                                </Popconfirm>
                              </div>
                            ),
                            width: '20%',
                          },
                        ]}
                        rowClassName="odd:bg-gray-50 even:bg-white"
                      />
                    </Col>
                  </div>
                ),
              },
            ]}
          />
        </Row>

        <div className="border-t mt-2" style={{ border: `1px solid ${COLOR.yellow}` }}></div>

        {/* No need */}
        {/*
        <div
          className="mt-2 d-flex justify-content-center fw-bold"
          style={{ color: COLOR.red, fontSize: '1.5rem' }}>
          BỆNH NHÂN NÀY ĐÃ ĐƯỢC THAM GIA BHYT
        </div>
        <div className="border-t mt-2" style={{ border: `1px solid ${COLOR.red}` }}></div> */}

        <div className="row mt-2 tab-items">
          <Tabs
            size="small"
            activeKey={selectedTabKey}
            onChange={(key) => {
              setSelectedTabKey(key)

              refetchPatientVisit()

              setTimeout(() => {
                if (bottomPageRef.current) {
                  bottomPageRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' })
                }
              }, 100)
            }}
            items={
              !currentPatientVisit?.patient_visit_id ? [] : tabItems.filter((item) => item?.visible)
            }></Tabs>
        </div>
        <div className="row mt-2"> </div>
      </div>
      <div ref={bottomPageRef}></div>
      {currentPatientVisit?.patient_visit_id &&
        selectedPatientVisitMappingViews.length > 0 &&
        isNormalMode &&
        !defaultDisabled && (
          <Modal
            title="Policy subscription"
            width={1000}
            // title={null} // Remove the title
            // closable={false} // Hide the close icon
            open={isVisiblePolicySubscriptionPopup}
            footer={false}
            onCancel={() => setIsVisiblePolicySubscriptionPopup(false)}>
            <PolicySubscriptionPopup
              mainVisit={currentPatientVisit}
              healthInsuranceCards={healthInsuranceCards}
              onOk={() => {
                setIsVisiblePolicySubscriptionPopup(false)
                if (bottomPageRef.current) {
                  bottomPageRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' })
                }
              }}
              selectedPatientVisitMappingViews={selectedPatientVisitMappingViews} // Pass the prop
            />
          </Modal>
        )}
      {isVisibleSearchAndAddVisitPopup && (
        <Modal
          className="custom-modal"
          title="THÔNG TIN ĐĂNG KÍ VÀ THẺ"
          width={2000}
          open={isVisibleSearchAndAddVisitPopup}
          onCancel={handleCloseModal}
          destroyOnClose
          footer={null}>
          <SearchAndAddVisitPopup
            initialCurrentPatient={currentPatientDataset}
            onSave={() => {
              const historyData = {
                patient_visit_id: currentPatientVisit?.patient_visit_id,
                note: currentPatientVisit?.note,
              }

              addPatientVisitHistory.mutateAsync({
                historyData,
                action: ACTION_VISIT_HISTORY.SAVE_INFO,
              })
              setIsVisibleSearchAndAddVisitPopup(false)
              // handleGetData()
            }}
            initialSelectedPatientVisitId={currentPatientVisit?.patient_visit_id}
            onBack={handleCloseModal}
            formMode="edit"
          />
        </Modal>
      )}
      <Modal
        className="custom-modal"
        width={2000}
        title="Merge lượt khám"
        open={isVisibleMergeVisitsPopup}
        destroyOnClose
        onCancel={() => {
          setIsVisibleMergeVisitsPopup(false)
          setSelectedMergedPatientVisitIds([])
        }}
        onClose={() => {
          setIsVisibleMergeVisitsPopup(false)
          setSelectedMergedPatientVisitIds([])
        }}
        onOk={async () => {
          try {
            await addMultiMergedPatientVisits(
              selectedMergedPatientVisitIds,
              currentPatientVisit?.patient_visit_id,
              currentUser,
              currentPatientVisit?.patient_id,
            )
            // await addMultiVisitChargeDetails(selectedMergedPatientVisitIds, currentUser)
            const historyData = {
              patient_visit_id: currentPatientVisit?.patient_visit_id,
              note: currentPatientVisit?.note,
            }

            addPatientVisitHistory.mutateAsync({
              historyData,
              action: ACTION_VISIT_HISTORY.MERGE_VISIT,
            })
            await handleUpdateTreamentCourse()
            setIsVisibleMergeVisitsPopup(false)
            setIsRefreshInvoice(true)
            await refetchAll()
          } catch (error) {
            handleError(error)
          }
        }}>
        <>
          <MergeVisitDatasetTable
            currentVisitDatasets={currentVisitDatasets}
            currentPatient={currentPatientDataset}
            filterVisitDate={filterTreatCourseDate}
            isSelectMultiple={true}
            selectedMergedPatientVisitIds={selectedMergedPatientVisitIds}
            setSelectedMergedPatientVisitIds={
              setSelectedMergedPatientVisitIds
            }></MergeVisitDatasetTable>
          <div className="d-flex justify-content-end gap-2"></div>
        </>
      </Modal>
      {/* Thông tin lượt khám */}
      {currentPatientVisit && (
        <VisitInformationPopup
          isVisibleVisitInformationPopup={isVisibleVisitInformationPopup}
          setIsVisibleVisitInformationPopup={setIsVisibleVisitInformationPopup}
          refetchPatientVisit={() => refetchPatientVisit()}
          mainVisit={currentPatientVisit}
          currentPatientVisitMappingViews={currentPatientVisitMappingViews}
        />
      )}
      {isVisibleVisitTrackingHistoryPopup && (
        <VisitTrackingHistoryPopup
          isVisibleVisitTrackingHistoryPopup={isVisibleVisitTrackingHistoryPopup}
          setIsVisibleVisitTrackingHistoryPopup={setIsVisibleVisitTrackingHistoryPopup}
          currentPatientVisit={currentPatientVisit}
        />
      )}
      {isShowMedicalCodingPopup && (
        <Modal
          title={'Danh sách mã bệnh - ' + firstSelectedVisit?.visit_code}
          className="custom-modal"
          open={isShowMedicalCodingPopup}
          onCancel={() => setIsShowMedicalCodingPopup(false)}
          width={800}
          footer={[]}>
          <PatientMedicalCodingForm
            onClose={() => setIsShowMedicalCodingPopup(false)}
            patientVisitId={firstSelectedVisit?.patient_visit_id}
            mainVisit={currentPatientVisit}
          />
        </Modal>
      )}
      {/* MS Confirmation Modal */}
      <Modal
        title="Xác nhận của MS"
        open={isVisibleMSConfirmModal}
        onCancel={handleCancelMSConfirm}
        destroyOnClose
        footer={[
          <AsyncButton key="submit" type="primary" onClick={handleMSConfirm}>
            Xác nhận
          </AsyncButton>,
        ]}>
        <Form form={msConfirmForm}>
          <Form.Item label="Ghi chú" name="note">
            <Input.TextArea
              autoSize={{ minRows: 2 }}
              value={msConfirmNote}
              onChange={(e) => setMsConfirmNote(e.target.value)}
              placeholder="Nhập ghi chú (không bắt buộc)"
            />
          </Form.Item>
        </Form>
      </Modal>
      {/* MS Confirm HSBA Modal */}
      <Modal
        title="Xác nhận đang hoàn thành HSBA"
        open={isVisibleMSConfirmHSBAModal}
        onOk={handleMSConfirmHSBA}
        onCancel={handleCancelMSConfirmHSBA}
        okText="Xác nhận"
        cancelText="Hủy">
        <Form form={msConfirmHSBAForm}>
          <Form.Item label="Ghi chú" name="note">
            <Input.TextArea
              autoSize={{ minRows: 2 }}
              value={msConfirmHSBANote}
              onChange={(e) => setMsConfirmHSBANote(e.target.value)}
              placeholder="Nhập ghi chú (không bắt buộc)"
            />
          </Form.Item>
        </Form>
      </Modal>
      {/* Processing Status Change Modal */}
      <Modal
        title="Thay đổi trạng thái"
        open={isVisibleProcessingStatusModal}
        onOk={handleUpdateProcessingStatus}
        onCancel={handleCancelProcessingStatus}
        okText="Cập nhật"
        cancelText="Hủy">
        <div className="mb-3">
          <div className="mb-2 font-bold">Trạng thái:</div>
          <Select
            placeholder="Chọn trạng thái"
            value={selectedProcessingStatus}
            onChange={(value) => setSelectedProcessingStatus(value)}
            style={{ width: '100%' }}>
            {Object.keys(PROCESSING_STATUS).map((key) => (
              <Select.Option key={key} value={key}>
                {PROCESSING_STATUS[key].name_l}
              </Select.Option>
            ))}
          </Select>
        </div>
      </Modal>
      {isNoteVisible && (
        <NotePopup
          visible={isNoteVisible}
          onClose={() => setIsNoteVisible(false)}
          entityType={noteEntityType}
          entity={noteEntity}
        />
      )}
    </div>
  )
}

export default VisitDetailPage
