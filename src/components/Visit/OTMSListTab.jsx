import { useState } from 'react'
import { Layout, Menu } from 'antd'
import OTMSItemChargePage from './OTMSItemChargePage'
import PropTypes from '../../common/PropTypes'
import OTMSMedicalConditionPage from './OTMSMedicalConditionPage'

const { Sider, Content } = Layout

const propTypes = {
  selectedVisit: PropTypes.object,
  currentPatientDataset: PropTypes.object,
  mainVisit: PropTypes.object,
}

const OTMSListTab = ({ selectedVisit, currentPatientDataset, mainVisit }) => {
  const [selectedKey, setSelectedKey] = useState('1')

  const tabs = [
    {
      key: '1',
      label: '1. Thông tin item Charge',
      content: <OTMSItemChargePage mainVisit={mainVisit} selectedVisit={selectedVisit} />,
    },
    {
      key: '2',
      label: '2. Dữ liệu bảng XML5',
      content: (
        <OTMSMedicalConditionPage
          currentPatientDataset={currentPatientDataset}
          mainVisit={mainVisit}
          selectedPatientVisit={selectedVisit || mainVisit}
        />
      ),
    },
    { key: '3', label: '3. Giấy ra viện', content: <span>Nội dung Hồ sơ</span> },
  ]

  return (
    <Layout style={{ minHeight: '50vh' }}>
      <Sider width={240} className="p-1 bg-white">
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          onClick={(e) => setSelectedKey(e.key)}
          className="h-full border-r-0"
          items={tabs.map((tab) => ({
            key: tab.key,
            label: tab.label,
            icon: tab.icon || null,
          }))}
        />
      </Sider>
      <Layout>
        <Content className="ml-2 rounded-lg">
          {tabs.find((tab) => tab.key === selectedKey)?.content}
        </Content>
      </Layout>
    </Layout>
  )
}

OTMSListTab.propTypes = propTypes

export default OTMSListTab
