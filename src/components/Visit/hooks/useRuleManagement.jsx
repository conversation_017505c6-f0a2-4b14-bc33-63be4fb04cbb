import React, { useEffect, useState } from 'react'
import {
  getAllRuleRefs,
  getRuleVisitItemDetailNlViewsByPatientVisitIds,
  getValidatedRules,
  mapRuleRefsWithDetails,
  prepareRulesData,
  updateManualCheckFlag,
  getUpdatedPatientVisitWarningStatus,
  validateVisitRule,
  validateXmlRule,
  getUpdatedAllSSXmlTablesWarningStatus,
} from '../../../services/ruleService'
import { handleError } from '../../../common/helpers'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { use } from 'react'
import useDeepCompareEffect from 'use-deep-compare-effect'
import {
  deleteListItemService,
  getItemService,
  getItemsService,
  patchMultiRecordDetails,
  updateListItemService,
} from '../../../common/services'
import lists from '../../../common/lists'
import { MODULE_AUTH } from '../../../store/auth'
import { useSelector } from 'react-redux'
import { XML_LIST } from '../../../SI/constant'
import { useUI } from '../../../common/UIProvider'
import { RULE_WARNING_STATUS } from '../RuleManagement/RuleManagementConstant'

export const VISIT_CHECK_CONDITIONS_QUERY_KEYS = {
  RULE_VISIT_ITEM_DETAIL_NL_VIEW: 'ruleVisitItemDetailNlViews',
}

export const useRuleManagement = (
  mainPatientVisitId,
  selectedPatientVisitId,
  currentPatientVisit,
  allSSXmlTables,
  patientMedicalCodingList,
) => {
  const ui = useUI()
  const [mappedRules, setMappedRules] = useState([])
  const [validatedRuleVisitDetailNlViews, setValidatedRuleVisitDetailNlViews] = useState([])
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])
  const queryClient = useQueryClient()

  // Giai đoạn 1: Chuẩn bị dữ liệu
  /**
   * Query để lấy rule_ref
   */
  const { data: ruleRefsData, isLoading: isLoadingRuleRefs } = useQuery({
    queryKey: ['ruleRefs'],
    queryFn: async () => {
      const data = await getAllRuleRefs()
      return data.value || [] // Đảm bảo luôn trả về một mảng
    },
    staleTime: 5 * 60 * 1000, // 5 phút cache
  })

  /**
   * Query để lấy rule_visit_item_detail_nl_view
   */ const {
    data: ruleVisitItemDetailNlViewsData,
    isLoading: isLoadingRuleVisitItemDetailNlViews,
  } = useQuery({
    queryKey: [
      VISIT_CHECK_CONDITIONS_QUERY_KEYS.RULE_VISIT_ITEM_DETAIL_NL_VIEW,
      mainPatientVisitId,
      selectedPatientVisitId,
    ],
    queryFn: async () => {
      const data = await getRuleVisitItemDetailNlViewsByPatientVisitIds(
        mainPatientVisitId,
        selectedPatientVisitId,
      )
      return data.value || []
    },
    enabled: !!mainPatientVisitId && !!selectedPatientVisitId,
  })
  // Giai đoạn 2: Xử lý dữ liệu

  // Combined useEffect
  useDeepCompareEffect(() => {
    // Console.log đầu tiên để debug
    // console.log('useEffect running with dependencies:', {
    //   hasRuleRefsData: !!ruleRefsData,
    //   hasCurrentPatientVisit: !!currentPatientVisit,
    //   hasAllSSTables: !!allSSXmlTables,
    // })

    if (
      ruleRefsData &&
      ((currentPatientVisit && Object.keys(currentPatientVisit).length > 0) ||
        (allSSXmlTables && Object.keys(allSSXmlTables).length > 0))
    ) {
      // Giữ nguyên logic kiểm tra điều kiện xử lý
      const isProcessVisitRule = !!currentPatientVisit?.patient_visit_id
      const isProcressXMLRule =
        !!allSSXmlTables?.ssTable1 && Object.keys(allSSXmlTables?.ssTable1).length > 0

      // Sử dụng tham chiếu mới cho mảng để tránh tham chiếu thay đổi
      let filteredRuleRefs = []

      // TH 1. filter theo rule_object la 'VISIT'
      if (isProcessVisitRule) {
        const visitRules = ruleRefsData.filter((ruleRef) => ruleRef.rule_object === 'VISIT')
        filteredRuleRefs = [...filteredRuleRefs, ...visitRules]
      }

      // TH 2. filter theo rule_object la 'XML'
      if (isProcressXMLRule) {
        const xmlRules = ruleRefsData.filter((ruleRef) => ruleRef.rule_object === 'XML')
        filteredRuleRefs = [...filteredRuleRefs, ...xmlRules]
      }

      // Ngăn việc tính toán lại nếu không có rules để xử lý
      if (filteredRuleRefs.length > 0) {
        // Tạo một bản sao của các tham số để tránh tham chiếu thay đổi
        const validateParams = {
          patientVisit: { ...currentPatientVisit },
          allSSXmlTables: { ...allSSXmlTables },
          medicalCodings: patientMedicalCodingList ? [...patientMedicalCodingList] : [],
        }

        // Bỏ qua phần map và set mappedRules, chỉ thực hiện validate
        const validatedRuleVisitDetailNlViews = getValidatedRules(
          filteredRuleRefs,
          validateParams,
          ruleVisitItemDetailNlViewsData,
        )

        setValidatedRuleVisitDetailNlViews((prevRules) => {
          // Nếu mảng rỗng hoặc nếu có sự thay đổi thực sự, thì cập nhật
          if (
            !prevRules ||
            prevRules.length === 0 ||
            JSON.stringify(prevRules) !== JSON.stringify(validatedRuleVisitDetailNlViews)
          ) {
            return validatedRuleVisitDetailNlViews
          }
          return prevRules // Trả về state cũ nếu không có thay đổi
        })
      }
    }
  }, [
    ruleRefsData,
    currentPatientVisit,
    allSSXmlTables,
    patientMedicalCodingList,
    ruleVisitItemDetailNlViewsData,
  ])
  // Giai đoạn 3: Save dữ liệu
  /**
   * Mutation để xử lý cả create và update rules qua patchMultiRecordDetails
   */
  const patchRulesMutation = useMutation({
    mutationFn: async (items) => {
      return patchMultiRecordDetails(lists.rule_visit_item_detail, items)
    },
  })

  /**
   * Mutation để lưu các rule được validate
   */

  const syncRulesAndOthersMutation = useMutation({
    mutationFn: async () => {
      await saveRulesMutation.mutateAsync()
      await updatePatientVisitWarningStatusMutation.mutateAsync()
      await updateAllSSXmlTablesWarningStatusMutation.mutateAsync()

      // Kiểm tra xem có rule nào cần validate không
    },
    onSuccess: () => {
      refetchRuleVisitItemDetailsData()
    },
  })

  const saveRulesMutation = useMutation({
    mutationFn: async () => {
      const { itemsToCreate, itemsToUpdate } = prepareRulesData(
        validatedRuleVisitDetailNlViews,
        mainPatientVisitId,
        currentUser,
        { patientVisit: currentPatientVisit, ssTable1: [] },
      )
      const allItems = [...itemsToCreate, ...itemsToUpdate]

      if (allItems.length > 0) {
        return patchMultiRecordDetails(lists.rule_visit_item_detail, allItems)
      }
    },
    onSuccess: () => {},
    onError: (error) => {
      handleError(error)
    },
  })

  //manual refetchData

  const refetchRuleVisitItemDetailsData = async () => {
    try {
      await queryClient.invalidateQueries({
        queryKey: [
          VISIT_CHECK_CONDITIONS_QUERY_KEYS.RULE_VISIT_ITEM_DETAIL_NL_VIEW,
          mainPatientVisitId,
          selectedPatientVisitId,
        ],
      })
    } catch (error) {
      handleError(error, 'refetchData')
    }
  }

  /**
   * Mutation để cập nhật manual_checked_flag cho rule
   */
  const updateManualCheckedFlagMutation = useMutation({
    mutationFn: async ({ ruleVisitItemDetailId, manualCheckedFlag, lu_user_id }) => {
      await updateManualCheckFlag(ruleVisitItemDetailId, manualCheckedFlag, lu_user_id)
    },
    onSuccess: (_, variables) => {
      // variables contains what was passed to the mutation
      const { ruleVisitItemDetailId, manualCheckedFlag, lu_user_id } = variables

      // // Cập nhật cache sử dụng queryClient
      // queryClient.setQueryData(
      //   [
      //     VISIT_CHECK_CONDITIONS_QUERY_KEYS.RULE_VISIT_ITEM_DETAIL_NL_VIEW,
      //     mainPatientVisitId,
      //     selectedPatientVisitId,
      //   ],
      //   (oldData) => {
      //     if (!oldData) return oldData

      //     // Tạo dữ liệu đã cập nhật
      //     return oldData.map((item) =>
      //       item.rule_visit_item_detail_id === ruleVisitItemDetailId
      //         ? { ...item, manual_checked_flag: manualCheckedFlag }
      //         : item,
      //     )
      //   },
      // )

      setValidatedRuleVisitDetailNlViews((prevRules) => {
        // Cập nhật validatedRuleVisitDetailNlViews
        return prevRules.map((item) =>
          item.rule_visit_item_detail_id === ruleVisitItemDetailId
            ? { ...item, manual_checked_flag: manualCheckedFlag, lu_user_id }
            : item,
        )
      })
      // Cập nhật warning_status của currentPatientVisit
    },
    onError: (error) => {
      handleError(error)
    },
  })

  /**
   * Mutation để xóa nhiều rule_visit_item_detail cùng lúc
   */
  const deleteRulesMutation = useMutation({
    mutationFn: async (ruleVisitItemDetailIds) => {
      // Tạo array các promise xóa rules
      const deleteRulesPromises = await ruleVisitItemDetailIds.map(async (id) =>
        deleteListItemService(lists.rule_visit_item_detail, id),
      )
      // Thực hiện xóa tất cả các rules bản ghi đồng thời
      await Promise.all(deleteRulesPromises)
      // Tạo array các promise xóa notes liên quan đến các rule
      const deleteRulesNotesPromises = []
      for (let i = 0; i < ruleVisitItemDetailIds.length; i++) {
        const ruleVisitItemDetailId = ruleVisitItemDetailIds[i]
        const ruleVisitItemDetailNotes = await getItemsService(lists.rule_visit_item_detail_note, {
          filter: `rule_visit_item_detail_id eq ${ruleVisitItemDetailId}`,
        })
        if (!ruleVisitItemDetailNotes.value || ruleVisitItemDetailNotes.value.length === 0) {
          continue // Nếu không có notes thì bỏ qua
        }
        deleteRulesNotesPromises.push(
          await ruleVisitItemDetailNotes.value.map(async (item) =>
            deleteListItemService(
              lists.rule_visit_item_detail_note,
              item.rule_visit_item_detail_note_id,
            ),
          ),
        )
      }

      await Promise.all(deleteRulesNotesPromises)

      /* cập nhật patient_visit */
      await updateListItemService(lists.patient_visit, currentPatientVisit.patient_visit_id, {
        warning_status: RULE_WARNING_STATUS.NEW.key,
      })
      /* cập nhật tất cả các bảng XML */
      const updatedAllSSXmlTables = getUpdatedAllSSXmlTablesWarningStatus(
        allSSXmlTables,
        validatedRuleVisitDetailNlViews,
        true,
      )
      // Cập nhật tất cả các bảng SS
      for (let i = 0; i < XML_LIST.length; i++) {
        const xmlTable = XML_LIST[i]
        const updatedTable = updatedAllSSXmlTables[xmlTable.table_name]

        // Nếu không có data thì bỏ qua
        if (!updatedTable) continue

        if (xmlTable.type === 'object') {
          // Dữ liệu là object
          if (!updatedTable[xmlTable.primary_key_name]) continue
          await updateListItemService(
            lists[xmlTable.list_name], // hoặc lists[table.list_name] nếu đúng
            updatedTable[xmlTable.primary_key_name],
            updatedTable,
          )
        } else if (xmlTable.type === 'array') {
          // Dữ liệu là array
          if (!updatedTable.length) continue
          await patchMultiRecordDetails(lists[xmlTable.list_name], updatedTable)
        }
      }
    },
    onSuccess: () => {
      // Xử lý thành công
      refetchRuleVisitItemDetailsData()
      ui.notiSuccess('Xóa thành công các điều kiện kiểm tra')
    },
    onError: (error) => {
      // Xử lý lỗi
      handleError(error)
    },
  })

  /**
   * Mutation để update warining_status của currentPatientVisit
   */
  const updatePatientVisitWarningStatusMutation = useMutation({
    mutationFn: async () => {
      //tạo mảng với warning_status mới
      const updatedItem = getUpdatedPatientVisitWarningStatus(
        currentPatientVisit,
        validatedRuleVisitDetailNlViews,
      )
      const updatedPatientVisit = await updateListItemService(
        lists.patient_visit,
        updatedItem.patient_visit_id,
        updatedItem,
      )

      //updateListItemService(lists.patient_visit, updatedItem.patient_visit_id, updatedItem)
    },
    onSuccess: () => {
      // Xử lý thành công
    },
    onError: (error) => {
      // Xử lý lỗi
      handleError(error)
    },
  })

  /**
   * Mutation để update warining_status của allSSXmlTables
   */
  const updateAllSSXmlTablesWarningStatusMutation = useMutation({
    mutationFn: async () => {
      //tạo mảng với warning_status mới

      const updatedAllSSXmlTables = getUpdatedAllSSXmlTablesWarningStatus(
        allSSXmlTables,
        validatedRuleVisitDetailNlViews,
        false,
      )
      console.log(updatedAllSSXmlTables)
      // Cập nhật tất cả các bảng SS
      for (let i = 0; i < XML_LIST.length; i++) {
        const xmlTable = XML_LIST[i]
        const updatedTable = updatedAllSSXmlTables[xmlTable.table_name]

        // Nếu không có data thì bỏ qua
        if (!updatedTable) continue

        if (xmlTable.type === 'object') {
          // Dữ liệu là object
          if (!updatedTable[xmlTable.primary_key_name]) continue
          await updateListItemService(
            lists[xmlTable.list_name], // hoặc lists[table.list_name] nếu đúng
            updatedTable[xmlTable.primary_key_name],
            updatedTable,
          )
        } else if (xmlTable.type === 'array') {
          // Dữ liệu là array
          if (!updatedTable.length) continue
          await patchMultiRecordDetails(lists[xmlTable.list_name], updatedTable)
        }
      }
      //updateListItemService(lists.patient_visit, updatedItem.patient_visit_id, updatedItem)
    },
    onSuccess: () => {
      // Xử lý thành công
    },
    onError: (error) => {
      // Xử lý lỗi
      handleError(error)
    },
  })

  return {
    ruleRefsData,
    isLoadingRuleRefs,
    validatedRuleVisitDetailNlViews,
    ruleVisitItemDetailNlViewsData,
    isLoadingRuleVisitItemDetailNlViews,
    // Các giá trị và hàm từ implementation mới
    saveRulesMutation,
    isLoading:
      isLoadingRuleRefs || isLoadingRuleVisitItemDetailNlViews || saveRulesMutation.isLoading,
    patchRulesMutation,

    refetchRuleVisitItemDetailsData,
    // Trả ra mutation mới
    updateManualCheckedFlagMutation,
    deleteRulesMutation,
    updatePatientVisitWarningStatusMutation,
    updateAllSSXmlTablesWarningStatusMutation,
    syncRulesAndOthersMutation
  }
}
