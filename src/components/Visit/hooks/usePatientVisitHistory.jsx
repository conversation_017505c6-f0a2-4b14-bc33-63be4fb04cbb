import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { handleError } from '../../../common/helpers'
import { addListItemService, getItemsService } from '../../../common/services'
import lists from '../../../common/lists'
import dayjs from '../../../common/dayjs'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../../store/auth'
import { FORMAT_DATE_UTC } from '../../../common/constant'

export const VISIT_HISTORY_KEYS = {
  PATIENT_VISIT_HISTORY: 'patientVisitHistory',
  EMPLOYEE_DATASET: 'employeeDataset',
}

export const usePatientVisitHistory = (patientVisitId) => {
  const queryClient = useQueryClient()
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  const visitHistoryKey = [VISIT_HISTORY_KEYS.PATIENT_VISIT_HISTORY, patientVisitId]

  // 1️⃣ Fetch lịch sử lượt khám theo `patient_visit_id`
  const {
    data: visitHistoryData,
    isLoading: isLoadingVisitHistory,
    isError: isErrorVisitHistory,
  } = useQuery({
    queryKey: visitHistoryKey, // Cache theo ID
    queryFn: async () => {
      const result = await getItemsService(lists.patient_visit_history, {
        filter: `patient_visit_id eq ${patientVisitId}`,
        orderBy: 'performed_date_time desc',
      }).then((res) => res.value)
      return result
    },
    enabled: !!patientVisitId, // Chỉ fetch khi có `patientVisitId`
    onError: (error) => handleError(error, 'getVisitHistory'),
    initialData: [],
  })

  // 2️⃣ Lấy danh sách GUIDs (performed_by) từ lịch sử lượt khám
  const performedByIds = visitHistoryData?.map((item) => item.performed_by) || []
  const uniqueUserIds = [...new Set(performedByIds)].filter(Boolean) // Loại bỏ trùng lặp

  // 3️⃣ Fetch danh sách nhân viên từ `employee_dataset`
  const {
    data: users,
    isLoading: isLoadingUsers,
    isError: isErrorUsers,
  } = useQuery({
    queryKey: [VISIT_HISTORY_KEYS.EMPLOYEE_DATASET, uniqueUserIds],
    queryFn: async () => {
      if (uniqueUserIds.length === 0) return []
      const guidList = uniqueUserIds.map((id) => `'${id}'`).join(',')
      const result = await getItemsService(lists.employee_dataset, {
        filter: `user_id in (${guidList})`,
        top: 2000,
      }).then((res) => res.value)
      return result
    },
    enabled: uniqueUserIds.length > 0, // Chỉ fetch khi có `userIds`
    onError: (error) => handleError(error, 'getUsersByIds'),
  })

  // 4️⃣ Mutation để thêm lịch sử lượt khám
  const addPatientVisitHistory = useMutation({
    mutationFn: async ({ historyData, action }) => {
      await addListItemService(lists.patient_visit_history, {
        ...historyData,
        action: action,
        performed_date_time: dayjs().format(FORMAT_DATE_UTC),
        performed_by: currentUser?.User_id,
      })
    },
    onMutate: async ({ historyData, action }) => {
      // Cập nhật cache tạm thời trước khi có phản hồi từ API
      const previousHistory = queryClient.getQueryData([
        VISIT_HISTORY_KEYS.PATIENT_VISIT_HISTORY,
        patientVisitId,
      ])
      const newHistory = {
        ...historyData,
        action: action,
        performed_date_time: dayjs().format(FORMAT_DATE_UTC),
        performed_by: currentUser?.User_id,
      }
      queryClient.setQueryData(visitHistoryKey, (old) => [...(old || []), newHistory])
      return { previousHistory }
    },
    onSuccess: () => {
      // it not effect to charge :)
      queryClient.invalidateQueries({ queryKey: visitHistoryKey })

      // it some how effect to charge :(
      // queryClient.invalidateQueries(visitHistoryKey)
    },
    onError: (error) => {
      handleError(error, 'addPatientVisitHistory')
      throw error
    },
  })

  const refetchHistory = () => {
    queryClient.invalidateQueries({ queryKey: visitHistoryKey })
  }

  // 5️⃣ Function to check patient visit history from API (prioritize latest record)
  const checkPatientVisitHistory = async ({
    patient_visit_id,
    merged_patient_visit_id,
    action,
  }) => {
    try {
      const result = await getItemsService(lists.patient_visit_history, {
        filter: `patient_visit_id eq ${patient_visit_id} 
          and (merged_patient_visit_id eq ${merged_patient_visit_id} or merged_patient_visit_id eq null)
          and action eq '${action}'`,
        orderBy: 'performed_date_time desc',
        top: 1, // Get only the latest record
      })

      return result?.value?.[0] || null // Return the latest history record or null if not found
    } catch (error) {
      handleError(error, 'checkPatientVisitHistory')
      return null
    }
  }

  // Kiểm tra trạng thái loading & error
  const isLoading = isLoadingVisitHistory || isLoadingUsers || addPatientVisitHistory.isLoading
  const isError = isErrorUsers || isErrorVisitHistory || addPatientVisitHistory.isError

  return {
    visitHistoryData,
    refetchHistory,
    users,
    addPatientVisitHistory,
    checkPatientVisitHistory,
    isError,
    isLoading,
  }
}
