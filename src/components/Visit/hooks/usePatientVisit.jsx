import { useQuery } from '@tanstack/react-query'
import { useEffect, useMemo, useState } from 'react'
import {
  getPatientDatasetByPatientId,
  getAllMergedPatientVisitMappingViewsByParentPatientVisitId,
  getHealthInsuranceCardsByPatientVisitId,
} from '../../../components/Visit/VisitService'
import { visitActions } from '../../../store/Visit'
import { useDispatch } from 'react-redux'
import { handleError } from '../../../common/helpers'
import { useUI } from '../../../common/UIProvider'
import nProgress from 'nprogress'
import { getItemService } from '../../../common/services'
import lists from '../../../common/lists'
import { useQueryClient } from '@tanstack/react-query'

// Keys for React Query
export const VISIT_QUERY_KEYS = {
  PATIENT_VISIT: 'patientVisit',
  PATIENT_VISIT_MAPPING_VIEW: 'patientVisitMappingView',
  PATIENT_DATASET: 'patientDataset',
  MERGED_PATIENT_VISIT_MAPPING_VIEWS: 'mergedPatientVisitMappingViews',
  HEALTH_INSURANCE_CARDS: 'healthInsuranceCards',
}

/**
 * Custom hook to fetch patient visit data with automatic refetching
 * @param {string} patientVisitId - The ID of the patient visit
 * @param {boolean} enabled - Whether the query should run
 */
export const usePatientVisit = (patientVisitId, enabled = true) => {
  const ui = useUI()
  const queryClient = useQueryClient()
  const [isRefetchingFast, setIsRefetchingFast] = useState(false)

  // Fetch patient visit data
  const patientVisitQuery = useQuery({
    queryKey: [VISIT_QUERY_KEYS.PATIENT_VISIT, patientVisitId],
    queryFn: async () => {
      const data = await getItemService(lists.patient_visit_mapping_view, patientVisitId)

      return data
    },
    enabled: !!patientVisitId && enabled,
    refetchOnWindowFocus: true,
    refetchInterval: (data) => {
      const isSyncing = data?.sync_visit_charge_status === 'SYNCING'
      const needToFast = isRefetchingFast && isSyncing

      return needToFast ? 2000 : 5 * 60 * 1000
    },
  })

  useEffect(() => {
    if (patientVisitQuery.data) {
      if (patientVisitQuery.data?.sync_visit_charge_status !== 'SYNCING') {
        setIsRefetchingFast(false)
      }
    }
  }, [patientVisitQuery.data])

  // Function to trigger fast refetching
  const startFastRefetch = () => {
    setIsRefetchingFast(true)
    return queryClient.invalidateQueries({
      queryKey: [VISIT_QUERY_KEYS.PATIENT_VISIT, patientVisitId],
    })
  }

  // Fetch patient dataset
  const patientDatasetQuery = useQuery({
    queryKey: [VISIT_QUERY_KEYS.PATIENT_DATASET, patientVisitQuery.data?.patient_id],
    queryFn: async () => {
      const data = await getPatientDatasetByPatientId(patientVisitQuery.data?.patient_id)
      return data.value[0]
    },
    enabled: !!patientVisitQuery.data?.patient_id && enabled,
    refetchInterval: 5 * 60 * 1000,
  })

  // Fetch merged patient visit mapping views
  const mergedPatientVisitMappingViewsQuery = useQuery({
    queryKey: [VISIT_QUERY_KEYS.MERGED_PATIENT_VISIT_MAPPING_VIEWS, patientVisitId],
    queryFn: async () => {
      const data = await getAllMergedPatientVisitMappingViewsByParentPatientVisitId(patientVisitId)
      return data.value
    },
    enabled: !!patientVisitId && enabled,
    refetchInterval: 5 * 60 * 1000,
  })

  // Fetch health insurance cards
  const healthInsuranceCardsQuery = useQuery({
    queryKey: [VISIT_QUERY_KEYS.HEALTH_INSURANCE_CARDS, patientVisitId],
    queryFn: async () => {
      try {
        const data = await getHealthInsuranceCardsByPatientVisitId(patientVisitId)
        return data.value
      } catch (error) {
        handleError(error, 'healthInsuranceCardsQuery')
        throw error
      }
    },
    enabled: !!patientVisitId && enabled,
    refetchInterval: 5 * 60 * 1000,
  })

  // Combine all data for easy access
  const combinedData = {
    currentPatientVisit: {
      ...patientVisitQuery.data,
      sync_visit_charge_status: undefined, // separate sync_visit_charge_status for performace, moved to syncVisitChargeStatus
      lu_updated: undefined,
    },
    syncVisitChargeStatus: patientVisitQuery.data?.sync_visit_charge_status,
    currentPatientDataset: patientDatasetQuery.data,
    mergedPatientVisitMappingViews: mergedPatientVisitMappingViewsQuery.data?.map((item) => ({
      ...item,
      sync_visit_charge_status: undefined,
      lu_updated: undefined,
    })),
    healthInsuranceCards: healthInsuranceCardsQuery.data,
  }

  // Prepare combined patient visit mapping views
  const combinedPatientVisitMappingViews = useMemo(() => {
    if (patientVisitQuery.data && mergedPatientVisitMappingViewsQuery.data) {
      return [
        {
          ...patientVisitQuery.data,
          checked_flag: true,
        },
        ...mergedPatientVisitMappingViewsQuery.data.map((view) => ({
          ...view,
          checked_flag: false,
        })),
      ]
        .map((item) => ({ ...item, sync_visit_charge_status: undefined, lu_updated: undefined }))
        .sort((a, b) => {
          const dateA = new Date(a.actual_visit_datetime)
          const dateB = new Date(b.actual_visit_datetime)
          return dateB - dateA // For descending order (newest first)
        }) // default active newest visit
    }

    return []
  }, [patientVisitQuery.data, mergedPatientVisitMappingViewsQuery.data])

  // Check if any query is loading
  const isLoading =
    patientVisitQuery.isLoading ||
    patientDatasetQuery.isLoading ||
    mergedPatientVisitMappingViewsQuery.isLoading ||
    healthInsuranceCardsQuery.isLoading

  const isFetching =
    patientVisitQuery.isFetching ||
    patientDatasetQuery.isFetching ||
    mergedPatientVisitMappingViewsQuery.isFetching ||
    healthInsuranceCardsQuery.isFetching

  useEffect(() => {
    if (isFetching) {
      nProgress.start()
    } else {
      nProgress.done()
    }

    return () => {
      nProgress.done()
    }
  }, [isFetching])

  // Check if any query has an error
  const isError =
    patientVisitQuery.isError ||
    patientDatasetQuery.isError ||
    mergedPatientVisitMappingViewsQuery.isError ||
    healthInsuranceCardsQuery.isError

  // Manually refetch all data
  const refetchAll = async () => {
    queryClient.invalidateQueries({
      queryKey: [VISIT_QUERY_KEYS.PATIENT_VISIT, patientVisitId],
    })
    queryClient.invalidateQueries({
      queryKey: [VISIT_QUERY_KEYS.PATIENT_DATASET, patientVisitQuery.data?.patient_id],
    })
    queryClient.invalidateQueries({
      queryKey: [VISIT_QUERY_KEYS.MERGED_PATIENT_VISIT_MAPPING_VIEWS, patientVisitId],
    })
    queryClient.invalidateQueries({
      queryKey: [VISIT_QUERY_KEYS.HEALTH_INSURANCE_CARDS, patientVisitId],
    })
  }

  const refetchPatientVisit = () => {
    queryClient.invalidateQueries({
      queryKey: [VISIT_QUERY_KEYS.PATIENT_VISIT, patientVisitId],
    })
  }

  const refetchMergedPatientVisitMappingViews = () => {
    queryClient.invalidateQueries({
      queryKey: [VISIT_QUERY_KEYS.MERGED_PATIENT_VISIT_MAPPING_VIEWS, patientVisitId],
    })
  }

  return {
    // Individual query results
    patientVisitQuery,
    patientDatasetQuery,
    mergedPatientVisitMappingViewsQuery,
    healthInsuranceCardsQuery,

    refetchPatientVisit,
    refetchMergedPatientVisitMappingViews,

    // Combined data
    data: combinedData,
    combinedPatientVisitMappingViews,

    // Status
    isLoading,
    isError,

    // Actions
    refetchAll,
    startFastRefetch,
  }
}
