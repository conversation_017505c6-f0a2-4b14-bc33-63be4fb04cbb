import { useQuery, useQueryClient } from '@tanstack/react-query'
import {
  getAllSsXmlTablesByPatientVisitId,
  getSsXmlTable1NlViewByPatientVisitId,
  getSsXmlTable2NlViewsByTable1Id,
  getSsXmlTable3NlViewsByTable1Id,
  getSsXmlTable4NlViewsByTable1Id,
  getSsXmlTable5NlViewsByTable1Id,
  getSsXmlTable7NlViewsByTable1Id,
} from '../../../services/ssXmlTableService'
import nProgress from 'nprogress'
import { useEffect } from 'react'

export const SS_XML_TABLE_QUERY_KEYS = {
  SS_TABLE_1_NL_VIEW: 'ssTable1NlView',
  SS_TABLE_2_NL_VIEW: 'ssTable2NlView',
  SS_TABLE_3_NL_VIEW: 'ssTable3NlView',
  SS_TABLE_4_NL_VIEW: 'ssTable4NlView',
  SS_TABLE_5_NL_VIEW: 'ssTable5NlView',

  SS_TABLE_7_NL_VIEW: 'ssTable7NlView',
  ALL_SS_TABLES: 'allSSXmlTables',
}

const useSSXmlTable = (patientVisitId, options = {}) => {
  const queryClient = useQueryClient()

  // const { enableAllTablesQuery = false, onSuccess } = options
  // const table1Query = useQuery({
  //   queryKey: [SS_XML_TABLE_QUERY_KEYS.SS_TABLE_1_NL_VIEW, patientVisitId],
  //   queryFn: async () => {
  //     const data = await getSsXmlTable1NlViewByPatientVisitId(patientVisitId)
  //     console.log(data)
  //     return data.value[0] || {}
  //   },
  //   enabled: !!patientVisitId,
  // })

  // const { data: table1Data = {} } = table1Query
  // const table1Id = table1Data?.table_1_id

  // // Các Query cho bảng 2 -7 , phu thuoc vao table1Id

  // const table2Query = useQuery({
  //   queryKey: [SS_XML_TABLE_QUERY_KEYS.SS_TABLE_2_NL_VIEW, table1Id],
  //   queryFn: async () => {
  //     console.log('start table 2')
  //     const data = await getSsXmlTable2NlViewsByTable1Id(table1Id)
  //     console.log(table1Id)
  //     console.log(data)
  //     return data.value || []
  //   },
  //   enabled: !!table1Id,
  // })

  // const table3Query = useQuery({
  //   queryKey: [SS_XML_TABLE_QUERY_KEYS.SS_TABLE_3_NL_VIEW, table1Id],
  //   queryFn: async () => {
  //     const data = await getSsXmlTable3NlViewsByTable1Id(table1Id)
  //     return data.value || []
  //   },
  //   enabled: !!table1Id,
  // })

  // const table4Query = useQuery({
  //   queryKey: [SS_XML_TABLE_QUERY_KEYS.SS_TABLE_4_NL_VIEW, table1Id],
  //   queryFn: async () => {
  //     const data = getSsXmlTable4NlViewsByTable1Id(table1Id)
  //     return data.value || []
  //   },
  //   enabled: !!table1Id,
  // })

  // const table5Query = useQuery({
  //   queryKey: [SS_XML_TABLE_QUERY_KEYS.SS_TABLE_5_NL_VIEW, table1Id],
  //   queryFn: async () => {
  //     const data = await getSsXmlTable5NlViewsByTable1Id(table1Id)
  //     return data.value || []
  //   },
  //   enabled: !!table1Id,
  // })

  // const table7Query = useQuery({
  //   queryKey: [SS_XML_TABLE_QUERY_KEYS.SS_TABLE_7_NL_VIEW, table1Id],
  //   queryFn: async () => {
  //     const data = await getSsXmlTable7NlViewsByTable1Id(table1Id)
  //     return data.value || []
  //   },
  //   enabled: !!table1Id,
  // })
  const allTablesQuery = useQuery({
    queryKey: [SS_XML_TABLE_QUERY_KEYS.ALL_SS_TABLES, patientVisitId],
    queryFn: () => getAllSsXmlTablesByPatientVisitId(patientVisitId),
    enabled: !!patientVisitId,
  })
  const isLoading =
    // table1Query.isLoading ||
    // table2Query.isLoading ||
    // table3Query.isLoading ||
    // table4Query.isLoading ||
    // table5Query.isLoading ||
    // table7Query.isLoading ||
    allTablesQuery.isLoading

  const isFetching =
    // table1Query.isFetching ||
    // table2Query.isFetching ||
    // table3Query.isFetching ||
    // table4Query.isFetching ||
    // table5Query.isFetching ||
    // table7Query.isFetching ||
    allTablesQuery.isFetching

  const allTables = allTablesQuery.data

  useEffect(() => {
    if (isFetching) {
      nProgress.start()
    } else {
      nProgress.done()
    }
  }, [isFetching])

  const refetchAllSsXmlTables = async () => {
    queryClient.invalidateQueries({
      queryKey: [SS_XML_TABLE_QUERY_KEYS.ALL_SS_TABLES, patientVisitId],
    })
  }

  return {
    // Option 0: Query alll bảng
    allTables,
    // Option 1: Truy cập trực tiếp từng bảng
    // ssTable1: table1Query.data,
    // ssTable2: table2Query.data,
    // ssTable3: table3Query.data,
    // ssTable4: table4Query.data,
    // ssTable5: table5Query.data,
    // ssTable7: table7Query.data,
    // Trạng thái loading
    isLoading,
    loadingState: {
      // table1: table1Query.isLoading,
      // table2: table2Query.isLoading,
      // table3: table3Query.isLoading,
      // table4: table4Query.isLoading,
      // table5: table5Query.isLoading,
      // table7: table7Query.isLoading,
    },
    // Các queries để truy cập metadata khác
    queries: {
      // table1Query,
      // table2Query,
      // table3Query,
      // table4Query,
      // table5Query,
      // table7Query,
    },
    // ...etc
    refetchAllSsXmlTables,
  }
}

export default useSSXmlTable
