import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'

import { handleError } from '../../../common/helpers'
import { useUI } from '../../../common/UIProvider'
import nProgress from 'nprogress'
import { getItemsService } from '../../../common/services'
import lists from '../../../common/lists'

// Keys for React Query
export const PATIENT_MEDICAL_CODING_QUERY_KEYS = {
  PATIENT_MEDICAL_CODING: 'patientMedicalCoding',
  PATIENT_MEDICAL_CODING_MAPPING: 'patientMedicalCodingMapping',
}

export const usePatientMedicalCoding = (patientVisitId, enabled = true) => {
  const ui = useUI()
  const queryClient = useQueryClient()

  const patientMedicalCodingQuery = useQuery({
    queryKey: [PATIENT_MEDICAL_CODING_QUERY_KEYS.PATIENT_MEDICAL_CODING, patientVisitId],
    queryFn: async () => {
      const data = await getItemsService(lists.api_patient_visit_medical_coding_view, {
        filter: `patient_visit_id eq ${patientVisitId}`,
      })

      return data.value
    },
    enabled: !!patientVisitId && enabled,
    refetchInterval: 5 * 60 * 1000,
  })

  const patientMedicalCodingMappingQuery = useQuery({
    queryKey: [PATIENT_MEDICAL_CODING_QUERY_KEYS.PATIENT_MEDICAL_CODING_MAPPING, patientVisitId],
    queryFn: async () => {
      const data = await getItemsService(lists.patient_medical_coding_mapping, {
        filter: `patient_visit_id eq ${patientVisitId}`,
      })

      return data.value
    },
    enabled: !!patientVisitId && enabled,
    refetchInterval: 5 * 60 * 1000,
  })

  // Check if any query is loading
  const isLoading =
    patientMedicalCodingQuery.isLoading || patientMedicalCodingMappingQuery.isLoading

  const isFetching =
    patientMedicalCodingQuery.isFetching || patientMedicalCodingMappingQuery.isFetching

  useEffect(() => {
    if (isFetching) {
      nProgress.start()
    } else {
      nProgress.done()
    }

    return () => {
      nProgress.done()
    }
  }, [isFetching])

  // Check if any query has an error
  const isError = patientMedicalCodingQuery.isError || patientMedicalCodingMappingQuery.isError

  // Manually refetch all data
  const refetchAll = async () => {
    try {
      queryClient.invalidateQueries({
        queryKey: [PATIENT_MEDICAL_CODING_QUERY_KEYS.PATIENT_MEDICAL_CODING, patientVisitId],
      })
      queryClient.invalidateQueries({
        queryKey: [
          PATIENT_MEDICAL_CODING_QUERY_KEYS.PATIENT_MEDICAL_CODING_MAPPING,
          patientVisitId,
        ],
      })
    } catch (error) {
      handleError(error, 'refetchAll')
      ui.notiError('An error occurred while refreshing data.')
    }
  }

  return {
    patientMedicalCodingList: patientMedicalCodingQuery.data || [],
    patientMedicalCodingMappingList: patientMedicalCodingMappingQuery.data || [],
    patientMedicalCodingQuery,
    patientMedicalCodingMappingQuery,
    isLoading,
    isError,
    refetchAll,
  }
}
