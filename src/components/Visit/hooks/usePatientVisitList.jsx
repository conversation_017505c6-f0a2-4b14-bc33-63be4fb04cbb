import { useQuery, useQueryClient } from '@tanstack/react-query'
import { getPatientVisitList } from '../VisitService'
import { handleError } from '../../../common/helpers'
import { useUI } from '../../../common/UIProvider'
import { useEffect } from 'react'
import nProgress from 'nprogress'

export const PATIENT_VISIT_LIST_KEYS = {
  PATIENT_VISIT: 'patient_visit',
}

export const usePatientVisitList = ({ filter, enabled = true }) => {
  const queryClient = useQueryClient()

  // keys
  const patientVisitListQueryKey = [PATIENT_VISIT_LIST_KEYS.PATIENT_VISIT, filter]

  const patientVisitListQuery = useQuery({
    queryKey: patientVisitListQueryKey,
    queryFn: () => getPatientVisitList({ filter }),
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    enabled: enabled,
    refetchOnWindowFocus: true,
  })

  const refetchPatientVisitList = () => {
    queryClient.invalidateQueries({ queryKey: patientVisitListQueryKey })
  }

  const patientVisitList = patientVisitListQuery.data || []

  const isLoading = patientVisitListQuery.isLoading
  const isError = patientVisitListQuery.isError
  const isSuccess = patientVisitListQuery.isSuccess
  const isFetching = patientVisitListQuery.isFetching

  useEffect(() => {
    if (isFetching) {
      nProgress.start()
    } else {
      nProgress.done()
    }
  }, [isFetching])

  return {
    patientVisitListQuery,
    patientVisitList,
    refetchPatientVisitList,
    isLoading,
    isError,
    isSuccess,
    isFetching,
  }
}
