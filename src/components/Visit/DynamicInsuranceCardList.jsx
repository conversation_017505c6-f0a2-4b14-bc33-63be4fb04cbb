import React, { useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import { Input, DatePicker, Button, Select, Form, Popconfirm, Tag } from 'antd'
import { PlusOutlined, MinusOutlined } from '@ant-design/icons'
import COLOR from '../../common/color'
import { getItemsService } from '../../common/services'
import dayjs from 'dayjs'
import {
  deleteHealthInsuranceCardById,
  getAllHealthInsuranceCardsByPatientId,
  getHealthInsuranceCardsByPatientVisitId,
  getPortalInsuranceCards,
  getSubregionNlViewBySubRegionId,
  getSubregionNlViewBySubRegionNameAndRegionId,
  getSubregionNlViewsByRegionId,
  getWardSubregionNlViewByWardNameAndSubRegionCode,
  getWardSubregionNlViewsBySubRegionCode,
} from './VisitService'
import { handleError } from '../../common/helpers'
import AsyncButton from '../../common/components/AsyncButton'
import {
  getRegionByCardCode,
  addressExtractor,
  mapPortalInsuranceCardsToHealthInsuranceCards,
  setFormListInsuranceCards,
  updateRawHealthInsuranceCards,
  mapFormInsuranceCardToHealthInsuranceCard,
} from './VisitHelpers'
import { useUI } from '../../common/UIProvider'
import lists from '../../common/lists'
import { useSelector } from 'react-redux'
import { MODULE_VISIT } from '../../store/Visit'
import { useWatch } from 'antd/es/form/Form'
import { VALID_INSURANCE_VALID_CODES } from './VisitConstant'

const { RangePicker } = DatePicker

const DynamicInsuranceCardList = ({
  currentPatient,
  form,

  formMode,
  // onCheckboxChange,
  setSelectedFormListInsuranceCards,
  // eslint-disable-next-line no-unused-vars
  selectedFormListInsuranceCards,
  selectedPatientVisitId,
  //healthInsuranceCards,
  //setHealthInsuranceCards,
  regionNlViews,
}) => {
  //state
  const [expandedKeys, setExpandedKeys] = useState({}) // Manage expanded state for each row
  const ui = useUI()
  const [filteredSubRegions, setFilteredSubRegions] = useState([])
  const [filteredWards, setFilteredWards] = useState([])
  const [availableHealthCards, setAvailableHealthCards] = useState([])

  const insuranceCards = useWatch(['insuranceCards'], form)
  //store
  const { systemReferralDispositionRefList } = useSelector((state) => state[MODULE_VISIT])

  const getInitData = async (skip = '', top = 50) => {
    const data = await getHealthInsuranceCardsByPatientVisitId(selectedPatientVisitId, skip, top)

    // const healInsuranceCards = data.value.map((item, index) => ({ ...item, key: index }))
    // if (data.value.length === 0) {
    //   // Initialize with an empty insurance card if none are fetched
    //   const initInsuranceCards = [{ key: 0, card_code: null }]
    //   setFormListInsuranceCards(form, initInsuranceCards)
    //   setExpandedKeys(initializeExpandedKeys(initInsuranceCards))
    //   setSelectedFormListInsuranceCards(initInsuranceCards)
    // } else {
    //   // Otherwise, set the form list with the fetched insurance cards and initialize expanded keys
    //   setFormListInsuranceCards(form, healInsuranceCards)
    //   setExpandedKeys(initializeExpandedKeys(healInsuranceCards))
    //   setSelectedFormListInsuranceCards(healInsuranceCards)
    // }

    // Example usage:
    const distinctCards = data.value //getDistinctCardCodes(data)
    const newFormListInsuranceCards = setFormListInsuranceCards(form, distinctCards)

    const availableCards = await getAllHealthInsuranceCardsByPatientId(currentPatient?.Patient_id)
    const distinctAvailabelCards = getDistinctCardCodes(availableCards.value)
    setAvailableHealthCards(distinctAvailabelCards)

    setSelectedFormListInsuranceCards([])
    setFilteredSubRegions(
      await Promise.all(
        newFormListInsuranceCards.map(async (item, index) => {
          let subRegions = []
          if (item?.region) {
            subRegions = (await getSubregionNlViewsByRegionId(item?.region)) || []
          }
          if (subRegions.value && subRegions.value.length > 0) {
            return { key: index, value: subRegions.value }
          }
          return { key: index, value: [] }
        }),
      ),
    )
    setFilteredWards(
      await Promise.all(
        distinctCards.map(async (item, index) => {
          if (item?.subregion_id == null) {
            return { key: index, value: [] }
          }
          const subRegion = await getSubregionNlViewBySubRegionId(item?.subregion_id)
          const wardSubRegions =
            (await getWardSubregionNlViewsBySubRegionCode(subRegion.display_code)) || []
          if (wardSubRegions.value && wardSubRegions.value.length > 0) {
            return { key: index, value: wardSubRegions.value }
          }
          return { key: index, value: [] }
        }),
      ),
    )

    return distinctCards
  }

  useEffect(() => {
    if (selectedPatientVisitId && currentPatient?.Patient_id) {
      getInitData()
    }
  }, [selectedPatientVisitId, currentPatient?.Patient_id])

  // useEffect(() => {
  //  console.log(currentPatient)
  // })

  useEffect(() => {
    // Initialize expanded state for each row
    // console.log('form:'  , form.getFieldValue('insuranceCards'))
    // console.log(expandedKeys)
    // setExpandedKeys(initializeExpandedKeys(form.getFieldValue('insuranceCards')))

    // Initialize expanded state for each row
    const initExpandedKeys = {}
    form.getFieldValue('insuranceCards')?.forEach((_, index) => {
      initExpandedKeys[index] = true // Set to false to not show the first row by default
    })
    setExpandedKeys(initExpandedKeys)
  }, [form])

  useEffect(() => {
    // Initialize expanded state for each row
    // console.log(expandedKeys)
  }, [expandedKeys])

  const toggleExpand = (key) => {
    setExpandedKeys((prev) => ({
      ...prev,
      [key]: !prev[key], // Toggle the expand/collapse state
    }))
  }

  // const handleCheckboxInsuranceCardChange = (isChecked, name) => {
  //  console.log('Checkbox state:', isChecked)
  //  console.log('key:', name)

  //   const record = { ...form.getFieldValue(['insuranceCards', name]), key: name }
  //  console.log(record)
  //   setSelectedFormListInsuranceCards((prev) => {
  //     if (isChecked) {
  //       return [...prev, record]
  //     } else {
  //       return prev.filter((r) => r.key !== name)
  //     }
  //   })
  //   // const updatedInsuranceCards = form.getFieldValue('insuranceCards').map((card) => {
  //   //   if (card.key === name) {
  //   //     return { ...card, isChecked }
  //   //   }
  //   //   return card
  //   // })
  //   // form.setFieldsValue({ insuranceCards: updatedInsuranceCards })
  // }

  const kiemTraThe = async (requestedCheckFormInsuranceCards = [], isCheckByNationId = false) => {
    const formListInsuranceCards = form.getFieldValue('insuranceCards')

    //console.log('formListInsuranceCards:', formListInsuranceCards)
    const patient = {
      fullName: currentPatient?.Fullname,
      dob: currentPatient?.DOB ? dayjs(currentPatient?.DOB).format('DD/MM/YYYY') : '',
      Nation_id: currentPatient?.Nation_id || currentPatient?.Passport_id,
    }
    if (formListInsuranceCards.length === 0) {
      ui.notiError('Vui lòng chọn ít nhất một thẻ BHYT để kiểm tra')
      return
    }
    if (patient.fullName == null) {
      ui.notiError('Bệnh nhân không có họ tên')
      return
    }
    if (patient.dob == null) {
      ui.notiError('Bệnh nhân không có ngày sinh')
      return
    }
    if (!patient.Nation_id) {
      ui.notiError('Bệnh nhân không có số CCCD')
      return
    }
    if (
      isCheckByNationId == false &&
      requestedCheckFormInsuranceCards.find((item) => item.cardCode == null)
    ) {
      ui.notiError('Vui lòng nhập mã thẻ BHYT để kiểm tra')
      return
    }

    try {
      // Check if any card has code 1 or 5 (at position 3)
      const hasSpecialCards = requestedCheckFormInsuranceCards.some(
        (card) =>
          card.cardCode && (card.cardCode.charAt(2) === '1' || card.cardCode.charAt(2) === '5'),
      )

      const responsePortalInsuranceCards = await getPortalInsuranceCards({
        checkedFormListInsuranceCards: requestedCheckFormInsuranceCards,
        patient,
        isCheckByNationId,
      })

      // Process the response
      const mappedCheckedHealthInsuranceCards = mapPortalInsuranceCardsToHealthInsuranceCards(
        responsePortalInsuranceCards,
        systemReferralDispositionRefList,
      )

      // For cards with code 1 or 5 that weren't found in the portal response,
      // add them back to the mapped cards with a special note
      if (hasSpecialCards) {
        const processedCardCodes = mappedCheckedHealthInsuranceCards.map((card) => card.card_code)

        requestedCheckFormInsuranceCards.forEach((card) => {
          if (
            card.cardCode &&
            (card.cardCode.charAt(2) === '1' || card.cardCode.charAt(2) === '5') &&
            !processedCardCodes.includes(card.cardCode)
          ) {
            // Add the card back with a note that it wasn't found in the portal
            mappedCheckedHealthInsuranceCards.push({
              card_code: card.cardCode,
              key: card.key,
              insurance_card_check_details:
                'Thẻ không tồn tại trên cổng BHXH nhưng vẫn được sử dụng do là mã thẻ 1 hoặc 5',
              maKetQua: 'NOT_FOUND', // Special code to indicate it wasn't found
            })
          }
        })
      }

      // Kiểm tra toàn diện các thẻ BHYT
      const invalidCards = mappedCheckedHealthInsuranceCards.filter(
        (card) => !!card?.maKetQua && !VALID_INSURANCE_VALID_CODES.includes(card?.maKetQua),
      )

      if (invalidCards.length > 0) {
        const invalidCardDetails = invalidCards
          .map(
            (card) =>
              `${card?.maKetQua} - ${card.card_code || ''}: ${card.insurance_card_check_details}`,
          )
          .join('\n')

        ui.notiWarning('Lỗi thẻ BHYT', `${invalidCardDetails}`)
      }

      const updatedRawHealthInsuranceCards = updateRawHealthInsuranceCards(
        formListInsuranceCards,
        mappedCheckedHealthInsuranceCards,
      )

      const newFormListInsuranceCards = setFormListInsuranceCards(
        form,
        updatedRawHealthInsuranceCards,
        regionNlViews,
      )

      setFilteredSubRegions(
        await Promise.all(
          newFormListInsuranceCards.map(async (item, index) => {
            let subRegions = []
            if (item?.region) {
              subRegions = (await getSubregionNlViewsByRegionId(item?.region)) || []
            }
            if (subRegions.value && subRegions.value.length > 0) {
              return { key: index, value: subRegions.value }
            }
            return { key: index, value: [] }
          }),
        ),
      )
    } catch (error) {
      ui.notiError(handleError(error))
    }
  }

  const handleOnCardCodeBlur = async (value, name) => {
    if (value) {
      const region = getRegionByCardCode(value, regionNlViews)

      if (region?.region_id) {
        const subRegions = await getSubregionNlViewsByRegionId(region.region_id)
        form.setFieldsValue({
          insuranceCards: {
            ...insuranceCards,
            [name]: {
              ...insuranceCards[name],
              region: region.region_id,
            },
          },
        })

        setFilteredSubRegions((prev) => {
          const index = prev.findIndex((item) => item.key === name)
          if (index !== -1) {
            return prev.map((item) =>
              item.key === name ? { ...item, value: subRegions.value } : item,
            )
          }
          return [...prev, { key: name, value: subRegions.value }]
        })
      }
    }
  }
  // Utility function to update filtered data
  const updateFilteredData = (prev, name, newValue) => {
    const index = prev.findIndex((item) => item.key === name)
    if (index !== -1) {
      // If item exists, update its value
      return prev.map((item) => (item.key === name ? { ...item, value: newValue } : item))
    } else {
      // If item does not exist, add new entry
      return [...prev, { key: name, value: newValue }]
    }
  }

  // Utility function to fetch and update sub-regions
  const fetchAndUpdateSubRegions = async (regionId, name, setFilteredSubRegions) => {
    try {
      const data = await getItemsService(lists.subregion_nl_view, {
        filter: `region_id eq ${regionId} and active_flag eq true`,
      })
      const subRegions = data.value
      setFilteredSubRegions((prev) => updateFilteredData(prev, name, subRegions))
    } catch (error) {
      handleError(error)
    }
  }

  // Utility function to fetch and update wards
  const fetchAndUpdateWards = async (subRegionDisplayCode, name, setFilteredWards) => {
    try {
      const data = await getItemsService(lists.ward_subregion_nl_view, {
        filter: `subregion_display_code eq '${subRegionDisplayCode}' and active_flag eq true`,
      })
      const wards = data.value
      setFilteredWards((prev) => updateFilteredData(prev, name, wards))
    } catch (error) {
      handleError(error)
    }
  }

  // Example usage in handleRegionChange
  const handleRegionChange = async (value, name) => {
    await fetchAndUpdateSubRegions(value, name, setFilteredSubRegions)
    //setFilteredWards([]) // Reset wards when changing region
  }

  // Example usage in handleSubRegionChange
  const handleSubRegionChange = async (value, name) => {
    const currentSubRegion = filteredSubRegions
      .find((sub) => sub.key === name)
      ?.value.find((sub) => sub.subregion_id === value)

    if (currentSubRegion) {
      await fetchAndUpdateWards(currentSubRegion.display_code, name, setFilteredWards)
    }
  }

  const getDistinctCardCodes = (data) => {
    const distinctCards = data.reduce((acc, current) => {
      acc[current.card_code] = current
      return acc
    }, {})

    return Object.values(distinctCards)
  }
  const handleAutoFillAddress = async (
    card = {
      cardCode: 'GD4919122078020',
      address: 'Tổ 4, Ấp Xẻo Tràm, Xã Sơn Bình, Huyện Hòn Đất, Tỉnh Kiên Giang',
    },
    name,
  ) => {
    try {
      // Step 1: Get region info from card code
      const regionInfo = getRegionByCardCode(card.cardCode, regionNlViews)
      if (!regionInfo) {
        // ui.notiError('Không tìm thấy thông tin tỉnh/thành phố từ mã thẻ.')
        return
      }

      // Step 2: Extract address details
      const addressDetails = addressExtractor(card.address)
      if (!addressDetails) {
        // ui.notiError('Không thể trích xuất thông tin địa chỉ từ thẻ.')
        return
      }

      // Step 3: Get subRegion and wardSubRegion info
      const subRegionInfo = await getSubregionNlViewBySubRegionNameAndRegionId(
        addressDetails.subRegionName,
        regionInfo?.region_id,
      ).then((res) => res.value[0])
      const wardSubRegionInfo = await getWardSubregionNlViewByWardNameAndSubRegionCode(
        addressDetails.wardName,
        subRegionInfo?.display_code,
      ).then((res) => res.value[0])

      // Sử dụng đoạn code này trong trường hợp thẻ gọi api kiểm tra hợp lệ, nhưng query data của HIS không có thì vẫn có thể thao tác chọn lại, trường dropdown hong bị disabed.
      if (!subRegionInfo || !wardSubRegionInfo) {
        // ui.notiError('Không tìm thấy thông tin quận/huyện hoặc phường/xã.')
        return
      }

      // Step 4: Update the insuranceCards array in the form
      const currentInsuranceCards = form.getFieldValue('insuranceCards') || []
      currentInsuranceCards[name] = {
        ...currentInsuranceCards[name],
        region: regionInfo?.region_id,
        subRegion: subRegionInfo?.subregion_id,
        wardSubRegion: wardSubRegionInfo?.ward_display_code,
      }

      // Step 5: Determine disabledFields
      let disabledFields = []

      if (VALID_INSURANCE_VALID_CODES.includes(currentInsuranceCards[name]?.maKetQua)) {
        disabledFields = Object.keys(
          mapFormInsuranceCardToHealthInsuranceCard(currentInsuranceCards[name]),
        ) //.filter((key) => currentInsuranceCards[name][key])
      }

      // Add disabledFields to the card
      currentInsuranceCards[name].disabledFields = disabledFields

      // Update the form with the modified insuranceCards
      await form.setFieldValue('insuranceCards', currentInsuranceCards)

      // Step 6: Update filteredSubRegions and filteredWards
      await fetchAndUpdateSubRegions(regionInfo.region_id, name, setFilteredSubRegions)
      await fetchAndUpdateWards(subRegionInfo.display_code, name, setFilteredWards)

      // ui.notiSuccess('Tự động điền địa chỉ thành công.')
    } catch (error) {
      // ui.notiError('Đã xảy ra lỗi khi tự động điền địa chỉ.')
    }
  }

  return (
    <>
      {/* Add a dynamic list of buttons */}

      {/* <Button
        onClick={() => {
      //  console.log(form.getFieldValue('insuranceCards'))
      //  console.log(selectedFormListInsuranceCards)
        }}></Button>

      <Button
        onClick={() => {
          const data = '68370'
          const filteredData = .find((itemsystemReferralDispositionRefList) =>
            item.name_e.includes(`[${data}]`)
          )

      //  console.log(filteredData)
        }}></Button> */}

      <Form.List
        name="insuranceCards"
        rules={[{ required: true, message: 'Vui lòng bổ sung thông tin thẻ BHYT' }]}>
        {(fields, { add, remove }, { errors }) => (
          <div>
            {/* <Button
              onClick={() => {
            //  console.log(dayjs('2025-02-17T17:00:00+07:00'))

            //  console.log(formatDateString('27/02/2025'))

            //  console.log(handleSetDateValue(formatDateString('27/02/2025')))

            //  console.log(dayjs('2025-02-17T10:00:00.000Z'))

            //  console.log(dayjs('2025-02-17T10:00:00.000Z').format())

            //  console.log(handleSetDateValue('27/02/2025'))
            //  console.log(convertUTCToLocalTime('2025-02-04T17:00:00.000Z'))
              }}></Button> */}
            {/* <Button
              onClick={() => {
            //  console.log(form.getFieldValue('insuranceCards'))
              }}></Button> */}
            {/* <Button
              onClick={() => {
                updateItemCard(0)
              }}></Button> */}
            <Button
              disabled={!selectedPatientVisitId}
              variant="solid"
              color="cyan"
              className="mt-2 mb-2 me-2"
              onClick={() => {
                const newKey = fields.length
                add({
                  key: newKey,
                  healthInsuranceCardId: null,
                })
                setExpandedKeys((prev) => ({
                  ...prev,
                  [newKey]: true,
                }))
              }}>
              Thêm thẻ
            </Button>
            <div>
              {errors?.[0] && (
                <Tag className="w-100" color="red">
                  {errors?.[0]}
                </Tag>
              )}
            </div>
            {form?.getFieldValue('insuranceCards')?.length > 0 && (
              <>
                <AsyncButton
                  disabled={!selectedPatientVisitId}
                  style={{
                    backgroundColor: !selectedPatientVisitId ? COLOR.disabled : COLOR.yellowGreen,
                    color: !selectedPatientVisitId ? 'black' : 'white',
                  }}
                  className="mt-2 mb-2 me-2"
                  onClick={async () => {
                    const insuranceCards = form.getFieldValue('insuranceCards') || []
                    await kiemTraThe(insuranceCards, false)
                    const newInsuranceCards = form.getFieldValue('insuranceCards') || []

                    await Promise.all(
                      newInsuranceCards.map((card, index) => handleAutoFillAddress(card, index)),
                    )
                  }}>
                  Kiểm tra thẻ
                </AsyncButton>
                <AsyncButton
                  disabled={!selectedPatientVisitId}
                  variant="solid"
                  color="blue"
                  onClick={async () => {
                    const insuranceCards = form.getFieldValue('insuranceCards') || []
                    await kiemTraThe(insuranceCards, true)
                    const newInsuranceCards = form.getFieldValue('insuranceCards') || []

                    await Promise.all(
                      newInsuranceCards.map((card, index) => handleAutoFillAddress(card, index)),
                    )
                  }}
                  className="mt-2 mb-2">
                  Kiểm tra thẻ bằng cccd
                </AsyncButton>
              </>
            )}

            {/* <AsyncButton
              style={{
                backgroundColor:
                  selectedFormListInsuranceCards.length <= 0 ? COLOR.disabled : COLOR.yellowGreen,
                color: selectedFormListInsuranceCards.length <= 0 ? 'black' : 'white'
              }}
              //form.setFieldsValue({ insuranceCards: currentInsuranceCards })
              //kiemTraThe
              className="mt-2 mb-2">
              Kiểm tra thẻ bằng cccd
            </AsyncButton> */}
            {fields.map(({ key, name, ...restField }) => (
              <div
                key={key}
                style={{
                  border: '1px solid #d9d9d9',
                  padding: '16px 16px 0 16px',
                  marginBottom: '16px',
                  borderRadius: '8px',
                  backgroundColor: '#f9f9f9',
                }}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}>
                  {/* <Form.Item {...restField} name={[name, 'isChecked']} valuePropName="checked">
                    <Checkbox
                      onChange={(e) => {
                        handleCheckboxInsuranceCardChange(e.target.checked, name)
                      }}></Checkbox>
                  </Form.Item> */}
                  <Form.Item
                    {...restField}
                    label="Mã thẻ BHYT"
                    className="w-100"
                    name={[name, 'cardCode']}
                    rules={[{ required: true, message: 'Mã thẻ BHYT không được để trống' }]}>
                    <Input
                      placeholder="Nhập mã thẻ BHYT"
                      onBlur={(e) => handleOnCardCodeBlur(e.target.value, name)}
                    />
                  </Form.Item>
                  <Form.Item>
                    <Button
                      type="text"
                      icon={expandedKeys[key] ? <MinusOutlined /> : <PlusOutlined />}
                      onClick={() => toggleExpand(key)}
                    />
                  </Form.Item>
                </div>
                {!form.getFieldValue('insuranceCards')[key]?.healthInsuranceCardId && (
                  <div className="mb-3 d-flex items-center gap-2">
                    <div>Các thẻ sử dụng gần đây:</div>
                    <div>
                      {availableHealthCards
                        .filter((item) => item.health_insurance_card_id !== null)
                        .map((card, index) => (
                          <AsyncButton
                            size="small"
                            type="primary"
                            ghost
                            className="me-2 fw-bold"
                            key={index}
                            onClick={async () => {
                              const currentInsuranceCards =
                                form.getFieldValue('insuranceCards') || []

                              // Ensure the index exists before updating
                              if (currentInsuranceCards[name]) {
                                currentInsuranceCards[name] = {
                                  //...currentInsuranceCards[name], // Preserve existing fields
                                  ...{
                                    healthInsuranceCardId: null,
                                    cardCode: card?.card_code,
                                    key: name,
                                  }, // Update with new data
                                }

                                // Set the updated values back to the form
                                form.setFieldValue('insuranceCards', currentInsuranceCards)
                                // form.setFieldsValue({ insuranceCards: currentInsuranceCards })
                              }
                              await kiemTraThe(
                                [
                                  {
                                    cardCode: card?.card_code,
                                    key: name,
                                  },
                                  // Update with new data
                                ],
                                false,
                              )
                              const newCurrentInsuranceCards = form.getFieldValue([
                                'insuranceCards',
                                name,
                              ])
                              await handleAutoFillAddress(newCurrentInsuranceCards, name)
                            }}>
                            {card.card_code}
                            {/* <span style={{ color: COLOR.red, textDecoration: 'underline' }}>

                          </span> */}
                          </AsyncButton>
                        ))}
                    </div>
                  </div>
                )}

                {/* Collapsible content */}
                {expandedKeys[key] && (
                  <div>
                    <Form.Item {...restField} name={[name, 'address']} label="Địa chỉ thẻ">
                      <Input
                        placeholder="Nhập địa chỉ thẻ"
                        disabled={insuranceCards?.[name]?.disabledFields?.includes('card_address')}
                      />
                    </Form.Item>
                    <div className="flex justify-between">
                      <Form.Item
                        style={{ width: '100%' }}
                        {...restField}
                        name={[name, 'region']}
                        label="Tỉnh/Thành phố"
                        rules={[{ required: true, message: 'Tỉnh/Thành phố không được để trống' }]}>
                        <Select
                          allowClear
                          showSearch
                          placeholder="Chọn tỉnh/thành phố"
                          disabled={insuranceCards?.[name]?.disabledFields?.includes('region_id')}
                          options={regionNlViews.map((item) => ({
                            value: item?.region_id,
                            label: <span>{item?.name_l}</span>,
                          }))}
                          onChange={(value) => {
                            handleRegionChange(value, name)
                          }}
                          filterOption={(input, option) =>
                            option?.label?.props?.children
                              ?.toLowerCase()
                              .includes(input.toLowerCase())
                          }
                        />
                      </Form.Item>
                      <Form.Item
                        style={{ width: '100%' }}
                        {...restField}
                        name={[name, 'subRegion']}
                        label="Quận/Huyện"
                        rules={[{ required: true, message: 'Quận/Huyện không được để trống' }]}>
                        <Select
                          allowClear
                          showSearch
                          placeholder="Chọn quận/huyện"
                          disabled={insuranceCards?.[name]?.disabledFields?.includes(
                            'subregion_id',
                          )}
                          options={
                            Array.isArray(filteredSubRegions)
                              ? filteredSubRegions
                                  .find((item) => item.key == name)
                                  ?.value.map((item) => ({
                                    value: item?.subregion_id,
                                    label: <span>{item?.name_l}</span>,
                                  }))
                              : []
                          }
                          onChange={(value) => {
                            handleSubRegionChange(value, name)
                          }}
                          filterOption={(input, option) =>
                            option?.label?.props?.children
                              ?.toLowerCase()
                              .includes(input.toLowerCase())
                          }
                        />
                      </Form.Item>
                      <Form.Item
                        style={{ width: '100%' }}
                        {...restField}
                        name={[name, 'wardSubRegion']}
                        label="Phường/Xã"
                        rules={[{ required: true, message: 'Phường/Xã không được để trống' }]}>
                        <Select
                          allowClear
                          showSearch
                          placeholder="Chọn phường/xã"
                          disabled={insuranceCards?.[name]?.disabledFields?.includes(
                            'ward_display_code',
                          )}
                          options={
                            Array.isArray(filteredWards)
                              ? filteredWards
                                  .find((item) => item.key == name)
                                  ?.value.map((item) => ({
                                    value: item?.ward_display_code,
                                    label: <span>{item?.name_l}</span>,
                                  }))
                              : []
                          }
                          filterOption={(input, option) =>
                            option?.label?.props?.children
                              ?.toLowerCase()
                              .includes(input.toLowerCase())
                          }
                        />
                      </Form.Item>
                    </div>
                    {/* <Form.Item {...restField} name={[name, 'issueDate']} label="Ngày phát hành">
                      <DatePicker
                        placeholder="Chọn ngày phát hành"
                        format="DD/MM/YYYY"
                        style={{ width: '100%' }}
                      />
                    </Form.Item> */}
                    <Form.Item
                      {...restField}
                      name={[name, 'effectiveDate']}
                      label="Hiệu lực"
                      rules={[{ required: true, message: 'Ngày hiệu lực không được để trống' }]}>
                      <RangePicker
                        format="DD/MM/YYYY"
                        style={{ width: '100%' }}
                        disabled={
                          insuranceCards?.[name]?.disabledFields?.includes('effective_date') &&
                          insuranceCards?.[name]?.disabledFields?.includes('expiration_date')
                        }
                      />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'continuousYears']}
                      label="Thời điểm năm năm liên tục"
                      rules={[
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            const cardCode = getFieldValue(['insuranceCards', name, 'cardCode'])
                            const isSpecialCard =
                              cardCode && (cardCode.charAt(2) === '1' || cardCode.charAt(2) === '5')

                            if (isSpecialCard || value) {
                              return Promise.resolve()
                            }
                            return Promise.reject(
                              new Error('Thời điểm năm năm liên tục không được để trống'),
                            )
                          },
                        }),
                      ]}>
                      <DatePicker
                        placeholder="Chọn ngày liên tục"
                        format="DD/MM/YYYY"
                        style={{ width: '100%' }}
                        disabled={insuranceCards?.[name]?.disabledFields?.includes(
                          'five_consecutive_years',
                        )}
                      />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'registerPlace']}
                      label="Nơi đăng ký ban đầu"
                      rules={[
                        { required: true, message: 'Nơi đăng ký ban đầu không được để trống' },
                      ]}>
                      <Select
                        allowClear
                        showSearch
                        placeholder="Chọn nơi đăng ký"
                        disabled={insuranceCards?.[name]?.disabledFields?.includes(
                          'referral_disposition_rcd',
                        )}
                        filterOption={(input, option) =>
                          option?.label?.props?.children
                            ?.toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        options={systemReferralDispositionRefList.map((disposition) => ({
                          value: disposition.referral_disposition_rcd,
                          label: <span>{disposition.name_l}</span>,
                        }))}></Select>
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'cardCheckDetails']}
                      label="Ghi nhận tra cứu thẻ">
                      <Input.TextArea
                        autoSize={{ minRows: 2 }}
                        disabled
                        placeholder="Ghi nhận tra cứu thẻ"
                        rows={3}
                        style={
                          form.getFieldValue(['insuranceCards', name])?.maKetQua &&
                          !VALID_INSURANCE_VALID_CODES.includes(
                            form.getFieldValue(['insuranceCards', name])?.maKetQua,
                          )
                            ? {
                                borderColor: 'red',
                              }
                            : {}
                        }
                      />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'note']} label="Ghi chú">
                      <Input.TextArea
                        autoSize={{ minRows: 2 }}
                        placeholder="Nhập ghi chú thêm chi tiết nếu thẻ có vấn đề"
                      />
                    </Form.Item>
                    <Popconfirm
                      title="Bạn có chắc chắn muốn xóa thẻ này không?"
                      onConfirm={
                        async () => {
                          const currentCard = form.getFieldValue(['insuranceCards', name])

                          if (currentCard?.healthInsuranceCardId) {
                            try {
                              deleteHealthInsuranceCardById(currentCard?.healthInsuranceCardId)
                              ui.notiSuccess('Xóa thẻ thành công')
                            } catch (error) {
                              ui.notiError(handleError(error))
                            }
                          }
                          const updatedInsuranceCards = form
                            .getFieldValue('insuranceCards')
                            .filter((card) => card?.key !== name)
                          // console.log(updatedInsuranceCards)
                          form.setFieldValue(
                            'insuranceCards',
                            updatedInsuranceCards.map((item, index) => ({
                              ...item,
                              key: index,
                            })),
                          )
                          // setExpandedKeys(initializeExpandedKeys(updatedInsuranceCards))
                          // setSelectedFormListInsuranceCards((prev) =>
                          //   prev.filter((card) => card?.key !== name)
                          // )
                          // remove(name)

                          // remove(name)

                          setSelectedFormListInsuranceCards((prev) =>
                            prev.filter((card) => card?.key !== name),
                          )
                        }
                        //handleDeleteCard(name, remove)
                      }
                      okText="Yes"
                      cancelText="No">
                      <Button
                        hidden={
                          !!form.getFieldValue(['insuranceCards', name])?.healthInsuranceCardId &&
                          formMode !== 'add'
                        }
                        type="danger"
                        className="mb-2">
                        Xóa thẻ
                      </Button>
                    </Popconfirm>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </Form.List>
    </>
  )
}

DynamicInsuranceCardList.propTypes = {
  currentPatient: PropTypes.object.isRequired,
  form: PropTypes.object.isRequired,
  formMode: PropTypes.oneOf(['add', 'edit']).isRequired,
  // onCheckboxChange: PropTypes.func.isRequired,
  setSelectedFormListInsuranceCards: PropTypes.func,
  selectedFormListInsuranceCards: PropTypes.array,
  selectedPatientVisitId: PropTypes.string,

  regionNlViews: PropTypes.array,
  subRegionNlViews: PropTypes.array,
}

export default DynamicInsuranceCardList
