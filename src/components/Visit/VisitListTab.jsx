import React, { useState } from 'react'
import COLOR from '../../common/color'
import { Button, Modal, Popover, Table } from 'antd'
import AsyncButton from '../../common/components/AsyncButton'
import { usePatientVisitList } from './hooks/usePatientVisitList'
import {
  PROCESSING_STATUS,
  STATUS_ORDER,
  TABLE_PATIENT_VISIT_MAPPING_VIEW_COLUMN,
} from './VisitConstant'
import { useAuth } from '../../common/AuthProvider'
import { PERMISSION } from '../Auth/AuthConstant'
import { Link, useNavigate } from 'react-router-dom'
import { useUI } from '../../common/UIProvider'
import { getAllXmlData } from './VisitHelpers'
import { FORM_MODE, FORMAT_DATE, MODE_VIEW_DATA } from '../../common/constant'
import * as XLSX from 'xlsx'
import { handleError } from '../../common/helpers'
import SearchAndAddVisitPopup from './SearchAndAddVisitPopup'
import { authActions, MODULE_AUTH } from '../../store/auth'
import { useDispatch, useSelector } from 'react-redux'
import { getItemsService, updateListItemService } from '../../common/services'
import lists from '../../common/lists'
import { visitActions } from '../../store/Visit'
import useApp from 'antd/es/app/useApp'

const VisitListTab = ({
  filterHN,
  filterVisitDate,
  filter_treatment_course_flag,
  selectedVisitTypes,
  processing_status,
}) => {
  const { checkPermission } = useAuth()
  const ui = useUI()
  const app = useApp()
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])
  const [isVisibleSearchAndAddVisitPopup, setIsVisibleSearchAndAddVisitPopup] = useState(false)
  const [selectedVisitId, setSelectedVisitId] = useState(null)

  const [openAddVisitPopup, setOpenAddVisitPopup] = useState(false)
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const { patientVisitList, isLoading, isFetching, refetchPatientVisitList } = usePatientVisitList({
    filter: {
      filterHN: filterHN,
      filterVisitDate: filterVisitDate,
      filter_treatment_course_flag: filter_treatment_course_flag,
      filterVisitTypes: selectedVisitTypes,
      processing_status: processing_status,
    },
  })

  const exportToExcel = async (getDetails = false) => {
    ui.setLoading(true)
    try {
      const ngayVaoFrom = filterVisitDate?.[0] ? filterVisitDate?.[0].format(FORMAT_DATE) : null
      const ngayVaoTo = filterVisitDate?.[1] ? filterVisitDate?.[1].format(FORMAT_DATE) : null
      const allData = await getAllXmlData(null, ngayVaoFrom, ngayVaoTo, getDetails)

      // Create workbook
      const wb = XLSX.utils.book_new()

      // Add each table data as a separate sheet, even if empty
      allData.forEach(({ sheetName, data }) => {
        const ws = XLSX.utils.json_to_sheet(data.length > 0 ? data : [{}])
        XLSX.utils.book_append_sheet(wb, ws, sheetName)
      })

      // Save the file
      XLSX.writeFile(wb, `XML_Data_${ngayVaoFrom || 'All'}-${ngayVaoTo || ''}.xlsx`)
      ui.notiSuccess('Xuất file Excel thành công')
    } catch (error) {
      handleError(error, 'VisitListTab>exportToExcel')
      ui.notiError('Không thể xuất file Excel')
    }
    ui.setLoading(false)
  }

  const handleSetProcessingPerson = async (record, modeViewData) => {
    const processingPerson = await getItemsService(lists.patient_visit_mapping_view, {
      filter: `patient_visit_id eq ${record.patient_visit_id}`,
    }).then((res) => res?.value[0])

    dispatch(authActions.setModeViewData(modeViewData))

    const navigateOptions = {
      state: {
        backUrl: '/his/visit' + location.search,
      },
    }
    let patientVisitIdToNavigate = record.parent_patient_visit_id || record.patient_visit_id
    let params = []

    // navigate to exac visit
    params.push(`selectedPatientVisitId=${record.patient_visit_id}`)

    // navigate to specific tab
    if ([MODE_VIEW_DATA.OT, MODE_VIEW_DATA.MS].includes(modeViewData)) {
      params.push(`selectedTabKey=6`)
    } else {
      params.push(`selectedTabKey=3`)
    }

    if (
      processingPerson?.processing_person_id &&
      processingPerson?.processing_person_name &&
      processingPerson?.processing_person_id !== currentUser?.User_id
    ) {
      //hiển thị popup ghi đè hay ko
      app.modal.confirm({
        title: 'Thông báo',
        content: `Thông tin đang được xử lý bởi ${processingPerson.processing_person_name}. Bạn có muốn ghi đè không?`,
        okText: 'Ghi đè',
        cancelText: 'Chỉ xem',
        onOk: async () => {
          await updateListItemService(lists.patient_visit, record.patient_visit_id, {
            processing_person_id: currentUser?.User_id,
            processing_person_name: currentUser?.User_name,
          })
          navigate(`/his/visit/${patientVisitIdToNavigate}?${params.join('&')}`, navigateOptions)
        },
        onCancel: () => {
          dispatch(visitActions.setVisitDetailMode(FORM_MODE.view))
          params.push(`visitDetailMode=${FORM_MODE.view}`)

          navigate(`/his/visit/${patientVisitIdToNavigate}?${params.join('&')}`, navigateOptions)
        },
      })
    } else {
      await updateListItemService(lists.patient_visit, record.patient_visit_id, {
        processing_person_id: currentUser?.User_id,
        processing_person_name: currentUser?.User_name,
      })

      navigate(`/his/visit/${patientVisitIdToNavigate}?${params.join('&')}`, navigateOptions)
    }
  }

  return (
    <div>
      <div className="d-flex justify-content-between items-center mt-2">
        <div>
          <b style={{ color: COLOR.orange }}>
            Tổng số lượt khám:{' '}
            {patientVisitList?.reduce(
              (sum, visit) => sum + (visit.children ? visit.children.length : 0),
              0,
            )}
          </b>
        </div>
        <div className="d-flex gap-2">
          <AsyncButton
            className={!checkPermission(PERMISSION.EXPORT_XML) && 'd-none'}
            onClick={() => exportToExcel()}
            style={{ color: COLOR.lime }}
            icon={<i className="fa fa-download" />}
            disabled={isLoading || isFetching}>
            Tải XML
          </AsyncButton>
          <AsyncButton
            className={!checkPermission(PERMISSION.EXPORT_XML) && 'd-none'}
            onClick={() => exportToExcel(true)}
            style={{ color: COLOR.lime }}
            icon={<i className="fa fa-download" />}
            disabled={isLoading || isFetching}>
            Tải XML chi tiết
          </AsyncButton>
          <Link to={'/his/visit/portal-submission'}>
            <Button
              hidden={!checkPermission(PERMISSION.SUBMIT_TO_GATE)}
              icon={<i className="fa fa-paper-plane" />}
              variant="solid"
              color="cyan">
              Đẩy cổng
            </Button>
          </Link>
          <AsyncButton
            className={!checkPermission(PERMISSION.CREATE_VISIT) && 'd-none'}
            icon={<i className="fa-solid fa-circle-plus"></i>}
            variant="solid"
            color="cyan"
            onClick={() => setOpenAddVisitPopup(true)}>
            Tạo lượt khám
          </AsyncButton>
        </div>
      </div>
      {patientVisitList.length > 0 && (
        <>
          <Table
            size="small"
            className="custom-table mt-2"
            loading={isFetching || isLoading}
            scroll={{ x: 'max-content' }}
            dataSource={patientVisitList.map((item) => ({
              key: item.patient_visit_id,
              // numberOrder: index + 1,
              ...item,
            }))}
            columns={[
              ...TABLE_PATIENT_VISIT_MAPPING_VIEW_COLUMN,
              {
                title: 'Hành động',
                key: 'action',
                fixed: 'right',
                render: (_, record) =>
                  record.children ? null : (
                    <div className="d-flex gap-2 align-items-center">
                      <Button
                        type="link"
                        className={!checkPermission(PERMISSION.EDIT_VISIT) && 'd-none'}
                        icon={<i style={{ color: COLOR.lime }} className="fas fa-eye"></i>}
                        onClick={async () => {
                          await handleSetProcessingPerson(record, MODE_VIEW_DATA.NORMAL)
                        }}
                      />
                      <Button
                        className={!checkPermission(PERMISSION.CASHIER_MODE) && 'd-none'}
                        icon={<i className="fa-solid fa-cash-register"></i>}
                        type="primary"
                        size="small"
                        onClick={async () => {
                          await handleSetProcessingPerson(record, MODE_VIEW_DATA.CASHIER)
                        }}>
                        Cashier
                      </Button>

                      <Button
                        className={!checkPermission(PERMISSION.OT_MODE) && 'd-none'}
                        icon={<i className="fa-solid fa-pen-clip"></i>}
                        type="primary"
                        size="small"
                        onClick={async () => {
                          await handleSetProcessingPerson(record, MODE_VIEW_DATA.OT)
                        }}>
                        OT
                      </Button>

                      <Button
                        className={!checkPermission(PERMISSION.MS_MODE) && 'd-none'}
                        icon={<i className="fa-solid fa-pen-clip"></i>}
                        size="small"
                        type="primary"
                        onClick={async () => {
                          await handleSetProcessingPerson(record, MODE_VIEW_DATA.MS)
                        }}>
                        MS
                      </Button>
                      <Popover content="Thông tin đăng kí và thẻ">
                        <AsyncButton
                          hidden={
                            !(
                              checkPermission(PERMISSION.EDIT_VISIT) ||
                              (checkPermission(PERMISSION.EDIT_CARD_INFO) &&
                                record.processing_status === PROCESSING_STATUS.WAITING_BHYT.name_e)
                            )
                          }
                          type="primary"
                          size="small"
                          variant="solid"
                          color="cyan"
                          onClick={async () => {
                            setIsVisibleSearchAndAddVisitPopup(true)
                            setSelectedVisitId(
                              record?.parent_patient_visit_id || record.patient_visit_id,
                            )
                          }}
                          icon={<i className="fa-solid fa-address-card"></i>}></AsyncButton>
                      </Popover>
                    </div>
                  ),
              },
            ]}
            expandable={{
              defaultExpandAllRows: false,
              defaultExpandedRowKeys: [`parent-${STATUS_ORDER[0]}`, `parent-${STATUS_ORDER[1]}`],
            }}
            pagination={true}></Table>
        </>
      )}

      {openAddVisitPopup && (
        <Modal
          className="custom-modal"
          title="TẠO LƯỢT KHÁM DÙNG BHYT"
          width={2000}
          open={openAddVisitPopup}
          onCancel={() => setOpenAddVisitPopup(false)}
          destroyOnClose
          maskClosable={false}
          keyboard={false}
          footer={null}>
          <SearchAndAddVisitPopup
            onSave={() => {
              setOpenAddVisitPopup(false)
              refetchPatientVisitList()
            }}
            initialSelectedPatientVisitId={null}
            currentPatientVisit={{}}
            onBack={() => setOpenAddVisitPopup(false)}
            formMode="add"
          />
        </Modal>
      )}

      {isVisibleSearchAndAddVisitPopup && (
        <Modal
          className="custom-modal"
          title="THÔNG TIN ĐĂNG KÍ VÀ THẺ"
          width={2000}
          open={isVisibleSearchAndAddVisitPopup}
          onCancel={() => setIsVisibleSearchAndAddVisitPopup(false)}
          destroyOnClose
          footer={null}>
          <SearchAndAddVisitPopup
            onSave={() => {
              setIsVisibleSearchAndAddVisitPopup(false)
            }}
            initialSelectedPatientVisitId={selectedVisitId}
            onBack={() => setIsVisibleSearchAndAddVisitPopup(false)}
            formMode="edit"
          />
        </Modal>
      )}
    </div>
  )
}

export default VisitListTab
