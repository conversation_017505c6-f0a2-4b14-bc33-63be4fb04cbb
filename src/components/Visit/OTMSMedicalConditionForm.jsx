import { Button, DatePicker, Form, Input, Select, Tag } from 'antd'
import PropTypes from '../../common/PropTypes'
import { useForm } from 'antd/es/form/Form'
import AsyncButton from '../../common/components/AsyncButton'
import { FORM_MODE, FORMAT_DATETIME, MODE_VIEW_DATA } from '../../common/constant'
import { useEffect } from 'react'
import dayjs from '../../common/dayjs'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import LazySelect from '../../common/components/LazySelect'
import lists from '../../common/lists'
import { addListItemService, updateListItemService } from '../../common/services'
import { useUI } from '../../common/UIProvider'
import MedicalRecordField, { MEDICAL_RECORD_FIELD_TYPE } from '../MedicalRecord/MedicalRecordField'

export const templateHOI_CHAN = {
  nameL: `Tóm tắt quá trình diễn biến bệnh, điều trị và chăm sóc người bệnh:

Tóm tắt kết quả cận lâm sàng:

Kết luận:

Hướng điều trị:
`,
  sqNumber: 1,
}

const OTMSMedicalConditionForm = ({ onClose, record, mode, currentPatientVisit }) => {
  const { modeViewData, currentUser } = useSelector((state) => state[MODULE_AUTH])

  const [form] = useForm()
  const ui = useUI()

  const handleSubmit = async () => {
    try {
      form.validateFields()
    } catch (error) {
      return
    }

    try {
      const values = form.getFieldsValue()
      //  console.log('values', values)
      const NGUOI_THUC_HIEN = Array.isArray(values.NGUOI_THUC_HIEN)
        ? values.NGUOI_THUC_HIEN.join(';')
        : values.NGUOI_THUC_HIEN || ''

      const newRecord = {
        ...values,
        NGUOI_THUC_HIEN: NGUOI_THUC_HIEN,
        patient_visit_id: currentPatientVisit.patient_visit_id,
        active_flag: true,
        lu_user_id: currentUser?.User_id,
      }

      if (mode === FORM_MODE.edit) {
        await updateListItemService(
          lists.medical_condition,
          record?.medical_condition_id,
          newRecord,
        )
      }

      if (mode === FORM_MODE.new) {
        await addListItemService(lists.medical_condition, newRecord)
      }

      ui.notiSuccess('Cập nhật thành công')
      onClose()
    } catch (error) {
      //  console.log('error', error)

      ui.notiError('Thất bại')
    }
  }

  const handleSetFormValues = () => {
    const NGUOI_THUC_HIEN = (record?.NGUOI_THUC_HIEN || '')
      .split(';')
      .map((name) => name.trim())
      .filter(Boolean)

    form.setFieldsValue({
      ...record,
      THOI_DIEM_DBLS: record?.THOI_DIEM_DBLS ? dayjs(record.THOI_DIEM_DBLS) : null,
      NGUOI_THUC_HIEN: NGUOI_THUC_HIEN,
    })
  }

  useEffect(() => {
    handleSetFormValues()
  }, [record])

  return (
    <Form form={form} layout="vertical">
      <div className="container-fluid py-3">
        {(modeViewData === MODE_VIEW_DATA.MS || modeViewData === MODE_VIEW_DATA.NORMAL) && (
          <>
            <Form.Item name="DIEN_BIEN_LS" label="Diễn biến lâm sàng">
              <Input.TextArea autoSize={{ minRows: 2 }}></Input.TextArea>
            </Form.Item>
            <Form.Item name="GIAI_DOAN_BENH" label="Giai đoạn bệnh">
              <Input></Input>
            </Form.Item>
            <MedicalRecordField
              form={form}
              formMode={FORM_MODE.edit}
              fieldName="HOI_CHAN"
              label="Hội chẩn"
              templateOptions={[templateHOI_CHAN]}
              underlineLabel={false}
              labelBold={false}
            />
            <br />

            <Form.Item name="PHAU_THUAT" label="Phẩu thuật">
              <Input.TextArea autoSize={{ minRows: 2 }}></Input.TextArea>
            </Form.Item>
            <MedicalRecordField
              form={form}
              formMode={FORM_MODE.edit}
              fieldName="NGUOI_THUC_HIEN"
              label="Người thực hiện:"
              labelBold={false}
              fieldType={MEDICAL_RECORD_FIELD_TYPE.TAG_SELECT}
              tagFieldName="MA_BHXH"
              tagFieldNameForDisplay="employee_name"
              lazySelectProps={{
                list: lists.employee_dataset,
                searchFields: ['employee_name', 'MA_BHXH'],
                keyProp: 'MA_BHXH',
                placeholder: 'Tìm và chọn',
                className: 'w-[300px]',
                filter: `MA_BHXH ne null`,
                renderOption: (item) => (
                  <Select.Option
                    item={item}
                    value={item.MA_BHXH}
                    key={item.MA_BHXH}
                    label={`${item.employee_name} - ${item.MA_BHXH}`}>
                    <div className="whitespace-normal">
                      <Tag>{item.MA_BHXH}</Tag>
                      <div>{item.employee_name}</div>
                    </div>
                  </Select.Option>
                ),
              }}
            />
          </>
        )}

        <Form.Item name="THOI_DIEM_DBLS" label="Thời điểm diễn biến lâm sàng">
          <DatePicker showTime className="w-100" format={FORMAT_DATETIME} />
        </Form.Item>

        <div className="d-flex justify-content-end mt-4">
          <Button icon={<i className="fa-regular fa-circle-xmark"></i>} onClick={onClose}>
            Hủy
          </Button>
          <AsyncButton
            icon={<i className="fa-solid fa-paper-plane"></i>}
            className="ms-2"
            type="primary"
            onClick={handleSubmit}>
            {mode === FORM_MODE.new ? 'Tạo' : 'Cập nhật'}
          </AsyncButton>
        </div>
      </div>
    </Form>
  )
}

OTMSMedicalConditionForm.propTypes = {
  onClose: PropTypes.func.isRequired,
  record: PropTypes.object,
  mode: PropTypes.string.isRequired,
  currentPatientVisit: PropTypes.object,
}

export default OTMSMedicalConditionForm
