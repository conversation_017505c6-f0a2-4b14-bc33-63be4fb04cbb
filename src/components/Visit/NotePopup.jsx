import React, { useState, useEffect } from 'react'
import { Modal, Table, Form, Input, Button } from 'antd'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { getItemsService, addListItemService } from '../../common/services'
import lists from '../../common/lists'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../store/auth'
import { useUI } from '../../common/UIProvider'
import { handleError } from '../../common/helpers'

const { TextArea } = Input

/**
 * NotePopup: Popup ghi chú chung cho Rule hoặc Visit
 * @param {boolean} visible - Hiển thị popup
 * @param {() => void} onClose - Đóng popup
 * @param {'visit'|'rule'} entityType - Loại đối tượng: 'visit' hoặc 'rule'
 * @param {Object} entity - <PERSON><PERSON><PERSON> tượng dữ liệu, c<PERSON> thuộc t<PERSON>h patient_visit_id hoặc rule_visit_item_detail_id
 */
const NotePopup = ({ visible, onClose, entityType, entity }) => {
  const ui = useUI()
  const queryClient = useQueryClient()
  const [noteContent, setNoteContent] = useState('')
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  // Xác định filterField và filterId dựa trên entityType
  const filterField = entityType === 'rule' ? 'rule_visit_item_detail_id' : 'patient_visit_id'
  const filterId =
    entityType === 'rule' ? entity?.rule_visit_item_detail_id : entity?.patient_visit_id
  const filterlist =
    entityType === 'rule' ? lists.rule_visit_item_detail_note_nl_view : lists.patient_visit_note

  // Query danh sách note
  const notesQuery = useQuery({
    queryKey: ['notes', filterField, filterId, filterlist?.listName],
    queryFn: async () => {
      let filter = []
      filter.push(`${filterField} eq ${filterId}`)
      const notes = await getItemsService(filterlist, {
        filter: filter.join(' and '),
        orderBy: 'lu_updated desc',
      }).then((res) => res.value)
      return notes
    },
    enabled: !!filterField && !!filterId && !!filterlist?.listName && visible,
  })

  // Mutation thêm note mới
  const addNoteMutation = useMutation({
    mutationFn: (content) => {
      if (entityType === 'rule') {
        return addListItemService(lists.rule_visit_item_detail_note, {
          rule_visit_item_detail_id: filterId,
          note: content,
          lu_user_id: currentUser?.User_id,
          created_by: currentUser?.Employee_name,
        })
      } else if (entityType === 'patient_visit') {
        return addListItemService(lists.patient_visit_note, {
          [filterField]: filterId,
          note: content,
          lu_user_id: currentUser?.User_id,
          created_by: currentUser?.Employee_name,
        })
      }
      // Nếu không phải 'rule' hoặc 'visit', không thực hiện gì cả
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes', filterField, filterId] })
      setNoteContent('')
      ui.notiSuccess('Thêm ghi chú thành công')
    },
    onError: (error) => {
      handleError(error)
    },
  })

  // Reset nội dung khi popup đóng hoặc đổi filterId
  useEffect(() => {
    if (!visible) setNoteContent('')
  }, [visible, filterId])

  const columns = [
    { title: 'Ghi chú', dataIndex: 'note', key: 'note' },
    { title: 'Người tạo', dataIndex: 'created_by', key: 'created_by' },
  ]

  const handleSave = () => {
    if (!noteContent.trim()) return
    addNoteMutation.mutate(noteContent)
  }

  return (
    <Modal
      className="custom-modal"
      title={`GHI CHÚ THEO ${filterField === 'rule_visit_item_detail_id' ? 'ĐIỀU KIỆN' : 'LƯỢT KHÁM'}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={2000}
      destroyOnClose>
      <div>
        {entityType === 'rule' ? (
          <div style={{ fontWeight: 'bold', marginBottom: 8 }}>Điều kiện: {entity?.rule_name}</div>
        ) : (
          <div style={{ fontWeight: 'bold', marginBottom: 8 }}>Lượt khám: {entity?.visit_code}</div>
        )}
      </div>
      <Table
        className="custom-table"
        rowKey={entityType == 'rule' ? 'rule_visit_item_detail_note_id' : 'patient_visit_note_id'}
        dataSource={notesQuery.data || []}
        columns={columns}
        loading={notesQuery.isLoading}
        pagination={false}
      />

      <Form layout="vertical" style={{ marginTop: 16 }}>
        <Form.Item>
          <TextArea
            rows={4}
            value={noteContent}
            onChange={(e) => setNoteContent(e.target.value)}
            placeholder="Vui lòng nhập thông tin ghi chú tại đây..."
          />
        </Form.Item>
        <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
          <Button
            type="primary"
            onClick={() => {
              handleSave()
            }}
            loading={addNoteMutation.isLoading}>
            Lưu
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default NotePopup

/*
  === SỬ DỤNG ===
  import React, { useState } from 'react'
  import NotePopup from './NotePopup'

  const [isNoteVisible, setIsNoteVisible] = useState(false)
  const [filterField, setFilterField] = useState(null)
  const [filterId, setFilterId] = useState(null)

  // Khi click nút Ghi chú
  onClick={() => {
    setFilterField('rule_visit_item_detail_id')
    setFilterId(record.rule_visit_item_detail_id)
    setIsNoteVisible(true)
  }}

  // Render ở cuối component:
  <NotePopup
    visible={isNoteVisible}
    onClose={() => setIsNoteVisible(false)}
    filterField={filterField}
    filterId={filterId}
  />
*/
