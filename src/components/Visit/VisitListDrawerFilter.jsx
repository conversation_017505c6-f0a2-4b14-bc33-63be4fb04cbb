import { Form, Select, Radio, Tag, Checkbox } from 'antd'
import PropTypes from '../../common/PropTypes'
import { useEffect, useState } from 'react'
import { useVisitTypeRef } from './hooks/useVisitTypeRef'
import { PROCESSING_STATUS } from './VisitConstant'

const propsTypes = {
  form: PropTypes.object,
  selectedVisitTypes: PropTypes.array,
  setSelectedVisitTypes: PropTypes.func,
}

function VisitListDrawerFilter({
  selectedVisitTypes,
  setSelectedVisitTypes,
  form,
  selectedTabKey,
}) {
  const [filteredVisitTypes, setFilteredVisitTypes] = useState([])
  const { visitTypeRef } = useVisitTypeRef()

  const handleVisitTypeChange = (value) => {
    if (!value) {
      return
    }

    setSelectedVisitTypes((prev) => [...new Set([...prev, value])])
  }

  useEffect(() => {
    form.setFieldsValue({ visit_type_name_e: undefined })
  }, [selectedVisitTypes])

  const handleSearch = (input) => {
    const lowerInput = input.toLowerCase()
    const filtered = visitTypeRef.filter(
      (item) =>
        (item.name_e.toLowerCase().includes(lowerInput) ||
          item.visit_type_rcd.toLowerCase().includes(lowerInput)) &&
        !selectedVisitTypes.some((selected) => selected === item.visit_type_rcd),
    )
    setFilteredVisitTypes(filtered)
  }

  useEffect(() => {
    setFilteredVisitTypes(visitTypeRef)
  }, [visitTypeRef])

  return (
    <>
      <Form.Item name="treatment_course_flag" valuePropName="checked">
        <Checkbox>Theo đợt điều trị</Checkbox>
      </Form.Item>
      <Form.Item
        hidden={selectedTabKey != '1'}
        label="Trạng thái xác nhận"
        name="ss_confirmation_flag"
        initialValue={'ALL'}>
        <Radio.Group>
          <Radio value={'ALL'}>Tất cả</Radio>
          <Radio value={'NA'}>Chưa xác nhận</Radio>
          <Radio value={'Yes'}>Có</Radio>
          <Radio value={'No'}>Không</Radio>
        </Radio.Group>
      </Form.Item>

      <Form.Item label="Trạng thái xử lý" name="processing_status" initialValue={'ALL'}>
        <Select allowClear placeholder="Chọn trạng thái">
          <Select.Option value="ALL">Tất cả</Select.Option>
          {Object.keys(PROCESSING_STATUS).map((key) => (
            <Select.Option key={key} value={key}>
              {PROCESSING_STATUS[key].name_l}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item label="Loại" name="visit_type_name_e">
        <Select
          allowClear
          showSearch
          placeholder="Chọn loại"
          onChange={handleVisitTypeChange}
          onSearch={handleSearch}
          filterOption={false}>
          {filteredVisitTypes
            .filter((item) => !selectedVisitTypes.some((sel) => sel === item.visit_type_rcd))
            .map((item) => (
              <Select.Option key={item.visit_type_rcd} value={item.visit_type_rcd} item={item}>
                {item.name_e}
              </Select.Option>
            ))}
        </Select>
      </Form.Item>

      <div className="d-flex gap-2 flex-wrap">
        {selectedVisitTypes.map((item, index) => (
          <Tag
            key={index}
            closable
            onClose={() => {
              setSelectedVisitTypes((prev) => prev.filter((_, i) => i !== index))
            }}>
            {visitTypeRef.find((i) => i.visit_type_rcd === item)?.name_e || item}
          </Tag>
        ))}
      </div>
    </>
  )
}

VisitListDrawerFilter.propTypes = propsTypes

export default VisitListDrawerFilter
