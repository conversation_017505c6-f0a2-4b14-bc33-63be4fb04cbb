XML 1:
STT	Tên cột	Ghi chú Valid	Điều kiện ktr trên app	Bắt buộc	Status	rule_rcd	name	error_message	rule_group_id	rule_object	warning_type	active_flag
1	MA_LK			x	Completed							
2	STT				Completed							
3	MA_BN		Ràng tại Popup Tạo lượt khám	x	Completed							
4	HO_TEN		Ràng tại Popup Tạo lượt khám	x	Completed							
5	SO_CCCD		Ràng tại Popup Tạo lượt khám		Completed							
6	NGAY_SINH		Ràng tại Popup Tạo lượt khám	x	Completed							
7	GIOI_TINH	Là mã giới tính của người bệnh (1: Nam; 2: <PERSON><PERSON>; 3: <PERSON><PERSON><PERSON> x<PERSON><PERSON> định)	Ràng tại Popup Tạo lượt khám	x	Completed							
8	NHOM_MAU				Completed							
9	MA_QUOCTICH	Theo phụ lục 2	Ràng tại Popup Tạo lượt khám	x	Completed							
10	MA_DANTOC		Ràng tại Popup Tạo lượt khám	x	Completed							
11	MA_NGHE_NGHIEP		Ràng tại Popup Tạo lượt khám	x	Completed							
12	DIA_CHI		Ràng tại Popup Tạo lượt khám	x	Completed							
13	MATINH_CU_TRU		Ràng tại Popup Tạo lượt khám	x	Completed							
14	MAHUYEN_CU_TRU		Ràng tại Popup Tạo lượt khám	x	Completed							
15	MAXA_CU_TRU		Ràng tại Popup Tạo lượt khám	x	Completed							
16	DIEN_THOAI				Completed							
17	MA_THE_BHYT				Completed							
18	MA_DKBD				Completed							
19	GT_THE_TU				Completed							
20	GT_THE_DEN				Completed							
21	NGAY_MIEN_CCT				Completed							
22	LY_DO_VV		"OPD thì mặc định là 'Khám bệnh, chữa bệnh cấp thuốc, VTYT' nên không cần check

IPD thì ràng phải có ít nhất 1 mã bệnh kèm theo (MA_BENH_KT)

Flow: Quét visit theo trạng thái NEW
Check: nếu visit_type_group_rcd = 'IPD' thì phải tồn tại ít nhất 1 record api_patient_visit_medical_coding_view primary_flag=0
Resolve: xem trigger"	x	Open	CHECK_LY_DO_VV		Không được để trống		VISIT	REQUIRED	1
23	LY_DO_VNT	Bắt buộc điền nếu MA_LOAI_KCB = 03	Nếu là IPD thì mặc định là 'Điều trị nội trú' rồi nên không cần check		Completed							
24	MA_LY_DO_VNT				Completed							
25	CHAN_DOAN_VAO		Quét bảng xml có warning_status = NEW	x	Open	CHECK_CHAN_DOAN_VAO		Không được để trống		XML	REQUIRED	1
26	CHAN_DOAN_RV		Quét bảng xml có warning_status = NEW	x	Open	CHECK_CHAN_DOAN_RV		Không được để trống		XML	REQUIRED	1
27	MA_BENH_CHINH		"Quét visit theo trạng thái 'Chờ tạo bảng XML'
Check: phải tồn tại ít nhất 1 record api_patient_visit_medical_coding_view primary_flag=1"	x	Open	CHECK_MA_BENH_CHINH		Chưa chọn mã bệnh chính		VISIT	REQUIRED	1
28	MA_BENH_KT	Tổng số mã bệnh kèm theo chỉ được <=12			Completed							
29	MA_BENH_YHCT				Completed							
30	MA_PTTT_QT				Completed							
31	MA_DOITUONG_KCB		Ràng tại Popup Tạo lượt khám	x	Completed							
32	MA_NOI_DI	Nếu có dữ liệu giấy chuyển tuyến thì bắt buộc điền	Ràng tại Nút Tạo bảng XML		Completed							
33	MA_NOI_DEN				Completed							
34	MA_TAI_NAN				Completed							
35	NGAY_VAO		pv.actual_visit_date_time luôn có	x	Completed							
36	NGAY_VAO_NOI_TRU	Phải kiểm tra >= NGAY_VAO	pv.actual_visit_date_time luôn có và luôn bằng NGAY_VAO		Completed							
37	NGAY_RA		"Quét visit
Check: nếu là IPD thì phải có NGAY_RA từ table Discharge_Certificate"	x	Open	"CHECK_NGAY_RA
(chờ data từ FV)"						
38	GIAY_CHUYEN_TUYEN	Nếu có dữ liệu giấy chuyển tuyến thì bắt buộc điền	Nút tạo lượt khám		Completed							
39	SO_NGAY_DTRI	"- MA_DOITUONG_KCB là ""01"", ""07"", ""09"" default SO_NGAY_DTRI = 0
-  MA_DOITUONG_KCB là ""02"", ""03"", ""04"" , ""06"" SO_NGAY_DTRI= NGAY_RA (-) NGAY_VAO (+) 1"	Đã tính trong sql đảm bảo có dữ liệu	x	Completed							
40	PP_DIEU_TRI				Completed							
41	KET_QUA_DTRI		"Quét visit
Check: có KET_QUA_DTRI trên popup Thông tin lượt khám"	x	Open	CHECK_KET_QUA_DTRI		Không được để trống		VISIT	REQUIRED	1
42	MA_LOAI_RV		"Quét visit theo trạng thái 'Chờ tạo bảng XML'
Check: có MA_LOAI_RV trên popup Thông tin lượt khám"	x	Open	CHECK_MA_LOAI_RV		Chưa chọn mã loại ra viện		VISIT	REQUIRED	1
43	GHI_CHU				Completed							
44	NGAY_TTOAN				Completed							
45	T_THUOC	Kiểm tra phải bằng Tổng nguyên cột T_BHTT ở XML2	Quét bảng xml có warning_status = NEW	x	Open	CHECK_T_THUOC		Chưa bằng tổng T_BHTT ở XML2		XML	REQUIRED	1
46	T_VTYT	Kiểm tra phải bằng Tổng nguyên cột T_BHTT ở XML3 của VTYT	Quét bảng xml có warning_status = NEW	x	Need To Discuss	CHECK_T_VTYT		Chưa bằng tổng T_BHTT của VTYT ở XML3		XML	REQUIRED	1
47	T_TONGCHI_BV	Kiểm tra phải bằng tổng tiền nguyên cột THANH_TIEN_BV ở XML2 cộng với tổng tiền nguyên cột THANH_TIEN_BV ở XML3	Quét bảng xml có warning_status = NEW	x	Open	CHECK_T_TONGCHI_BV		Chưa bằng tổng THANH_TIEN_BV ở XML2 và XML3		XML	REQUIRED	1
48	T_TONGCHI_BH	Kiểm tra phải bằng tổng tiền nguyên cột THANH_TIEN_BH ở XML2 cộng với tổng tiền nguyên cột THANH_TIEN_BH ở XML3	Quét bảng xml có warning_status = NEW	x	Open	CHECK_T_TONGCHI_BH		Chưa bằng tổng THANH_TIEN_BH ở XML2 và XML3		XML	REQUIRED	1
49	T_BNTT	Kiểm tra phải bằng tổng tiền nguyên cột T_BNTT ở XML2 cộng với tổng tiền nguyên cột T_BNTT ở XML3	Quét bảng xml có warning_status = NEW	x	Open	CHECK_T_BNTT		Chưa bằng tổng T_BNTT ở XML2 và XML3		XML	REQUIRED	1
50	T_BNCCT	Kiểm tra phải bằng tổng tiền nguyên cột T_BNCCT ở XML2 cộng với tổng tiền nguyên cột T_BNCCT ở XML3	Quét bảng xml có warning_status = NEW	x	Open	CHECK_T_BNCCT		Chưa bằng tổng T_BNCCT ở XML2 và XML3		XML	REQUIRED	1
51	T_BHTT	Kiểm tra phải bằng tổng tiền nguyên cột T_BHTT ở XML2 cộng với tổng tiền nguyên cột T_BHTT ở XML3	Quét bảng xml có warning_status = NEW	x	Open	CHECK_T_BHTT		Chưa bằng tổng T_BHTT ở XML2 và XML3		XML	REQUIRED	1
52	T_NGUONKHAC		Ràng tại Nút Tạo bảng XML	x	Completed							
53	T_BHTT_GDV		Ràng tại Nút Tạo bảng XML	x	Completed							
54	NAM_QT		Ràng tại Nút Tạo bảng XML	x	Completed							
55	THANG_QT		Ràng tại Nút Tạo bảng XML	x	Completed							
56	MA_LOAI_KCB		Ràng tại Nút Tạo bảng XML	x	Completed							
57	MA_KHOA		"Quét visit theo trạng thái 'Chờ tạo bảng XML'
Check: nếu là IPD thì phải có MA_KHOA từ table Discharge_Certificate"	x	Need To Discuss	"CHECK_MA_KHOA
(chờ data từ FV)"						
58	MA_CSKCB		Ràng tại Nút Tạo bảng XML	x	Completed							
59	MA_KHUVUC				Completed							
60	CAN_NANG		Ràng tại Popup Tạo lượt khám	x	Completed							
61	CAN_NANG_CON				Completed							
62	NAM_NAM_LIEN_TUC				Completed							
63	NGAY_TAI_KHAM	Nếu có dữ liệu thì XML14 bắt buộc nhập			Completed							
64	MA_HSBA		Ràng tại Nút Tạo bảng XML	x	Completed							
65	MA_TTDV				Completed							 

XML 2:
STT	Tên cột	Ghi chú Valid	Điều kiện ktr trên app	Bắt buộc	Status	rule_rcd	name	error_message	rule_group_id	rule_object	warning_type	active_flag
1	MA_LK			x	Completed							
2	STT			x	Completed							
3	MA_THUOC		Quét visit theo trạng thái 'Chờ tạo bảng XML'	x	Open	CHECK_MA_THUOC		Chưa chọn mã thuốc		XML	REQUIRED	1
4	MA_PP_CHEBIEN			Trường hợp thuốc cổ truyền thì bắt buộc	Completed							
5	MA_CSKCB_THUOC				Completed							
6	MA_NHOM		Quét visit theo trạng thái 'Chờ tạo bảng XML'	x	Open	CHECK_MA_NHOM		Chưa chọn mã nhóm		XML	REQUIRED	1
7	TEN_THUOC		Quét visit theo trạng thái 'Chờ tạo bảng XML'	x	Open	CHECK_TEN_THUOC		Chưa chọn tên thuốc		XML	REQUIRED	1
8	DON_VI_TINH		Quét visit theo trạng thái 'Chờ tạo bảng XML'	x	Open	CHECK_DON_VI_TINH		Chưa chọn đơn vị tính		XML	REQUIRED	1
9	HAM_LUONG		Quét visit theo trạng thái 'Chờ tạo bảng XML'	x	Open	CHECK_HAM_LUONG		Chưa chọn hàm lượng		XML	REQUIRED	1
10	DUONG_DUNG		Quét visit theo trạng thái 'Chờ tạo bảng XML'	x	Open	CHECK_DUONG_DUNG		Chưa chọn đường dùng		XML	REQUIRED	1
11	DANG_BAO_CHE				Completed							
12	LIEU_DUNG		Quét visit theo trạng thái 'Chờ tạo bảng XML'	x	Open	CHECK_LIEU_DUNG		Chưa chọn liều dùng		XML	REQUIRED	1
13	CACH_DUNG				Completed							
14	SO_DANG_KY		Quét visit theo trạng thái 'Chờ tạo bảng XML'	x	Open	CHECK_SO_DANG_KY		Chưa chọn số đăng ký		XML	REQUIRED	1
15	TT_THAU		Quét visit theo trạng thái 'Chờ tạo bảng XML'	Bắt buộc trừ mã nhóm 7,17	Open	CHECK_TT_THAU		Chưa chọn thông tin thầu		XML	REQUIRED	1
16	PHAM_VI		Quét visit theo trạng thái 'Chờ tạo bảng XML'	x	Open	CHECK_PHAM_VI		Chưa chọn phạm vi		XML	REQUIRED	1
17	TYLE_TT_BH		Ràng tại DS Charge item	x	Completed							
18	SO_LUONG		Ràng tại DS Charge item	x	Completed							
19	DON_GIA		Ràng tại DS Charge item	x	Completed							
20	THANH_TIEN_BV		Ràng tại DS Charge item	x	Completed							
21	THANH_TIEN_BH		Ràng tại DS Charge item	x	Completed							
22	T_NGUONKHAC_NSNN				Completed							
23	T_NGUONKHAC_VTNN				Completed							
24	T_NGUONKHAC_VTTN				Completed							
25	T_NGUONKHAC_CL				Completed							
26	T_NGUONKHAC				Completed							
27	MUC_HUONG		Ràng tại DS Charge item	x	Completed							
28	T_BNTT		Ràng tại DS Charge item	x	Completed							
29	T_BNCCT		Ràng tại DS Charge item	x	Completed							
30	T_BHTT		Ràng tại DS Charge item	x	Completed							
31	MA_KHOA	"""Check Danh mục đẩy cổng
- Có một Danh mục lưu thông tin mã khoa và tên khoa bhyt"""	Ràng tại DS Charge item	x	Completed							
32	MA_BAC_SI		Ràng tại DS Charge item	x	Completed							
33	MA_DICH_VU				Completed							
34	NGAY_YL			x	Completed							
35	NGAY_TH_YL	>= NGAY_YL			Completed							
36	MA_PTTT		Ràng tại Nút Tạo bảng XML,Luôn điền là 1	x	Completed							
37	NGUON_CTRA		Ràng tại Nút Tạo bảng XML,Luôn điền là 1	x	Completed							
38	VET_THUONG_TP		Ràng tại Nút Tạo bảng XML	Điền NULL	Completed					 		

XML3: 
STT	Tên cột	Ghi chú Valid	Điều kiện ktr trên app	Bắt buộc	Status
1	MA_LK			x	Completed
2	STT			x	Completed
3	MA_DICH_VU		"Quét visit theo trạng thái 'Chờ tạo bảng XML'
- Nếu item là Dịch vụ thì phải có mã dịch vụ BH"		Completed
4	MA_PTTT_QT				Completed
5	MA_VAT_TU		"Quét visit theo trạng thái 'Chờ tạo bảng XML'
- Nếu item là Vật tư thì phải có mã VTYT BH"		Completed
6	MA_NHOM	MA_NHOM = 19 => Không valid all thông tin trong bảng	Quét visit theo trạng thái 'Chờ tạo bảng XML'	x	Completed
7	GOI_VTYT	Ký tự G + số từ 1-999	Quét visit theo trạng thái 'Chờ tạo bảng XML'		Completed
8	TEN_VAT_TU		"Quét visit theo trạng thái 'Chờ tạo bảng XML'
- Nếu item là Vật tư thì phải có tên VTYT BH"		Completed
9	TEN_DICH_VU		"Quét visit theo trạng thái 'Chờ tạo bảng XML'
- Nếu item là Dịch vụ thì phải có tên Dịch vụ BH"		Completed
10	MA_XANG_DAU			Bắt buộc khi MA_NHOM = 12	Completed
11	DON_VI_TINH			x	Completed
12	PHAM_VI			x	Completed
13	SO_LUONG		Ràng tại DS Charge item	x	Completed
14	DON_GIA_BV		Ràng tại DS Charge item	x	Completed
15	DON_GIA_BH		Ràng tại DS Charge item	x	Completed
16	TT_THAU		Ràng tại DS Charge item	Bắt buộc khi MA_NHOM = 10	Completed
17	TYLE_TT_DV		Ràng tại DS Charge item	x	Completed
18	TYLE_TT_BH		Ràng tại DS Charge item	x	Completed
19	THANH_TIEN_BV		Ràng tại DS Charge item	x	Completed
20	THANH_TIEN_BH		Ràng tại DS Charge item	x	Completed
21	T_TRANTT		Ràng tại DS Charge item	Bắt buộc khi VTYT có trần thanh toán	Completed
22	MUC_HUONG		Ràng tại DS Charge item	x	Completed
23	T_NGUONKHAC_NSNN				Completed
24	T_NGUONKHAC_VTNN				Completed
25	T_NGUONKHAC_VTTN				Completed
26	T_NGUONKHAC_CL				Completed
27	T_NGUONKHAC				Completed
28	T_BNTT		Ràng tại DS Charge item	x	Completed
29	T_BNCCT		Ràng tại DS Charge item	x	Completed
30	T_BHTT		Ràng tại DS Charge item	x	Completed
31	MA_KHOA		Ràng tại DS Charge item	x	Completed
32	MA_GIUONG	"Check Danh mục đẩy cổng
- Có một Danh mục lưu thông tin mã khoa và tên khoa bhyt"			Need To Discuss
33	MA_BAC_SI		Ràng tại DS Charge item	x	Completed
34	NGUOI_THUC_HIEN		Ràng tại DS Charge item	Bắt buộc MA_NHOM =1,2,3,8,18	Completed
35	MA_BENH				Completed
36	MA_BENH_YHCT				Completed
37	NGAY_YL		Ràng tại Nút Tạo bảng XML	x	Completed
38	NGAY_TH_YL		Ràng tại Nút Tạo bảng XML	bắt buộc MA_NHOM =1,2,3,8,18	Completed
39	NGAY_KQ				Completed
40	MA_PTTT		Ràng tại Nút Tạo bảng XML, luôn ghi là 1	x	Completed
41	VET_THUONG_TP	Điền NULL	Ràng tại Nút Tạo bảng XML		Completed
42	PP_VO_CAM		"Chỉ ràng đối với IPD
Ràng tại Nút Tạo bảng XML, chỉ kiểm tra bắt buộc với các item có mã nhóm là 8 và 18
- Quét visit theo trạng thái 'Chờ tạo bảng XML"		Open
43	VI_TRI_TH_DVKT				Completed
44	MA_MAY		"Nếu item là thuộc mã nhóm 2-> Ràng tại Nút Tạo bảng XML
- Quét bảng xml có warning_status = NEW"		Need To Discuss
45	MA_HIEU_SP				Completed
46	TAI_SU_DUNG	Điền NULL hoặc VTYT có tỉ lệ TAI_SD thì điền 1	Nếu cột TL_TAI_SD của VTYT có thông tin->Ràng tại Nút Tạo bảng XML		Completed 

XML 4:
STT	Tên cột	Ghi chú Valid	Điều kiện ktr trên app	Bắt buộc	Status	rule_rcd	name	error_message	rule_group_id	rule_object	warning_type	active_flag
1	MA_LK			x	Completed							
2	STT			x	Completed							
3	MA_DICH_VU		Ràng tại Nút Tạo bảng XML		Open							
4	MA_CHI_SO		Nếu có DV chuẩn đoán hình ảnh, xét nghiệm, thăm dò chức năng-> Bắt buộc nếu mã nhóm = 1,2,3		Open							
5	TEN_CHI_SO		Nếu có DV chuẩn đoán hình ảnh, xét nghiệm, thăm dò chức năng-> Bắt buộc nếu mã nhóm = 1,2,3	x	Open							
6	GIA_TRI		Nếu có DV chuẩn đoán hình ảnh, xét nghiệm, thăm dò chức năng-> Bắt buộc nếu mã nhóm = 1,2,3		Open							
7	DON_VI_DO		Nếu có DV chuẩn đoán hình ảnh, xét nghiệm, thăm dò chức năng-> Bắt buộc nếu mã nhóm = 1,2,3		Open							
8	MO_TA				Completed							
9	KET_LUAN			Bắt buộc nhập nếu MA_NHOM (XML 3) = 2	Open							
10	NGAY_KQ			Bắt buộc khi trường GIA_TRI có dữ liệu	Open							
11	MA_BS_DOC_KQ			x	Open	CHECK_MA_BS_DOC_KQ		Chưa chọn mã bác sĩ đọc kết quả		XML	REQUIRED	1 

XML 5: 
STT	Tên cột	Ghi chú Valid	Điều kiện ktr trên app	Bắt buộc	Status
1	MA_LK			x	Completed
2	STT			x	Completed
3	DIEN_BIEN_LS		"Ràng tại Nút Tạo bảng XML
- Check Đã tồn tại 1 dòng bên Phiếu điều trị hay chưa=> Nếu chưa Cảnh báo MS chưa điền"	x	Completed
4	GIAI_DOAN_BENH				Completed
5	HOI_CHAN				Completed
6	PHAU_THUAT				Completed
7	THOI_DIEM_DBLS		"Ràng tại Nút Tạo bảng XML
- Check Đã tồn tại 1 dòng bên Phiếu điều trị hay chưa=> Nếu chưa Cảnh báo OT chưa điền"	x	Completed
8	NGUOI_THUC_HIEN		"Ràng tại Nút Tạo bảng XML
- Check Đã tồn tại 1 dòng bên Phiếu điều trị hay chưa=> Nếu chưa Cảnh báo MS chưa điền"	x	Completed 


XML 7:
Tên cột	Ghi chú Valid	Điều kiện ktr trên app	Bắt buộc	Status
MA_LK			x	Completed
SO_LUU_TRU				Completed
MA_YTE		Ràng tại Nút Tạo bảng XML=> Đối với IPD	x	Completed
MA_KHOA_RV		Ràng tại Nút Tạo bảng XML=> Đối với IPD	x	Completed
NGAY_VAO		Ràng tại Nút Tạo bảng XML=> Đối với IPD	x	Completed
NGAY_RA		Lấy từ XML1 nên không cần check bên này	x	Completed
MA_DINH_CHI_THAI				Completed
NGUYENNHAN_DINHCHI		Ràng tại Nút Tạo bảng XML=> Đối với IPD	bắt buộc khi MA_DINH_CHI_THAI =1	Need To Discuss
THOIGIAN_DINHCHI		Ràng tại Nút Tạo bảng XML=> Đối với IPD	bắt buộc khi MA_DINH_CHI_THAI =1	Need To Discuss
TUOI_THAI		Ràng tại Nút Tạo bảng XML=> Đối với IPD	bắt buộc khi MA_DINH_CHI_THAI =1	Need To Discuss
CHAN_DOAN_RV		"Ràng tại Nút Tạo bảng XML=> Đối với IPD
- Sẽ check Chuẩn đoán ra viện trong data giấy ra viện ""dc.diagnosis""
- Check: tồn tại first api_patient_visit_medical_coding_view primary_flag=0
Resolve: 
    - Recheck from flow
    - Click recheck on rule in ruleList
Level: required -- warning, required"	x	Completed
PP_DIEUTRI			x	Open
GHI_CHU				Completed
MA_TTDV		Ràng tại Nút Tạo bảng XML=> Đối với IPD	x	Completed
MA_BS			x	Open
TEN_BS			x	Open
NGAY_CT			x	Open
MA_CHA		- Trường hợp trẻ em dưới 16 tuổi sẽ điền thông tin		Need To Discuss
MA_ME		- Trường hợp trẻ em dưới 16 tuổi sẽ điền thông tin		Need To Discuss
MA_THE_TAM	Check định dạng 3 ký tự đầu là TE1; 2 ký tự sau phải nằm trong DM tỉnh thành; 10 số cuối phải có trong bảng CAPMA_BHXH	- Trường hợp trẻ em dưới 16 tuổi sẽ điền thông tin		Need To Discuss
HO_TEN_CHA		- Trường hợp trẻ em dưới 16 tuổi sẽ điền thông tin		Need To Discuss
HO_TEN_ME		- Trường hợp trẻ em dưới 16 tuổi sẽ điền thông tin		Need To Discuss
SO_NGAY_NGHI	Nếu 1 trường có dữ liệu thì bắt buộc nhập cả 3 trường		= NGAY_RAVIEN hoặc NGAY_RAVIEN + 1	Need To Discuss
NGOAITRU_TUNGAY				Need To Discuss
NGOAITRU_DENNGAY				Need To Discuss

