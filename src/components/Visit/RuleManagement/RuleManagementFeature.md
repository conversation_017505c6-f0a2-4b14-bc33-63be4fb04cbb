# Tổng quan nghiệp vụ

Bạn đang xây dựng hệ thống kiểm tra quy tắc (rule checking) cho hồ sơ bệnh nhân, với các đặc điểm:

- Hai loại rules chính:

  - Rules kiểm tra dữ liệu VISIT (thông tin thăm khám)
  - Rules kiểm tra dữ liệu XML (chuẩn bị cho xuất XML)

- Hai cách kiểm tra:

  - Kiểm tra tự động (system_checked_flag): H<PERSON> thống tự validate theo logic

  - Kiểm tra thủ công (manual_checked_flag): Người dùng xác nhận đã kiểm tra

- Luồng xử lý rules:

  1. Fetch dữ liệu bảng rule_ref và rule_visit_item_detail_nl_view, currentPatientVisit , patientMedicalCodingList, allSSTables từ database
  2. Duyệt từng phần tử của bảng rule_ref
  3. Trong quá trình duy<PERSON>t ph<PERSON>n tử, tu<PERSON> từng rule khác nhau sẽ kiểm tra theo từng nguồn dữ liệu khác nhau. Hiện tại có 2 rule_object là VISIT thì kiểm tra tại mảng currentPatientVisit và patientMedicalCodingList , rule_object là XML thì kiểm tra tại allSSTables. Validate rules theo logic nghiệp vụ, xem logic nghiệp vụ tại file #RuleXML.xlsx. Ngoài ra map với rule_visit_item_detail_nl_view hiện tại để xác định rule_visit_item_detail_nl_view nào cần cập nhật và thêm mới, đầu ra cuối cùng sẽ là mảng tên validatedRuleVisitDetailNlViews . 
  4. Lưu validatedRuleVisitDetailNlViews kết quả để tạo mới hoặc cập nhật cho table rule_visit_item_detail
  5. Sau khi thực hiện xong, tại 2 bảng  currentPatientVisit và allSSTables (bên trong gồm 7 bảng ss_table_1 --> ss_table_7 ) đều có warning_status . Dựa vào validatedRuleVisitDetailNlViews để cập nhật vào cột warning_status tương ứng. 

- Cấu trúc database xem tại file RuleManagementFeature.md.

- Nếu currentPatientVisit có dữ liệu thì tại useRuleManagement sẽ chỉ xử lí các RuleRef có object là Visit nếu rỗng thì không xử lí . Trong quá trình kiểm tra (validated) nếu phát hiện rule nào có system_checked_flag là false thì cập nhật currentPatientVisit.warning_status == INVALID , tất cả system_checked_flag là true thì thì cập nhật currentPatientVisit.warning_status == VALID thông qua phương thức updateItemService

- Nếu allSSTables?.ssTable1? có dữ liệu thì tại useRuleManagement sẽ chỉ xử lí các RuleRef có object là XML nếu rỗng thì không xử lí .Trong quá trình kiểm tra (validated) nếu phát hiện rule nào có system_checked_flag là false thì cập nhật currentPatientVisit.warning_status == INVALID , tất cả system_checked_flag là true thì thì cập nhật currentPatientVisit.warning_status == VALID

- "ss_table_n.warning_status
  Apply cho tất cả các bảng XML" Description
  NEW Sau khi tạo bảng xml xong
  CHECKING Flow quét những record NEW để chuyển sang CHECKING và bắt đầu kiểm tra tất cả các cột
  INVALID Sau khi kiểm tra xong nếu có lỗi thì chuyển trạng thái sang INVALID
  WARNING Sau khi kiểm tra xong nếu có không có lỗi mà chỉ có những rule WARNING thì chuyển trạng thái sang WARNING
  VALID Sau khi kiểm tra xong và không có lỗi

- "patiet_visit.warning_status
  Bảng patient_visit (lưu theo patient_visit_id liên quan, ví dụ thông tin chung thì lưu mainVisit, thông tin charge thì lưu visit của charge đó)" Description
  NEW Sau khi trạng thái được chuyển thành WAITING_XML_CREATION
  CHECKING Flow quét những record NEW để chuyển sang CHECKING và bắt đầu kiểm tra tất cả các cột
  INVALID Sau khi kiểm tra xong nếu có lỗi thì chuyển trạng thái sang INVALID
  WARNING Sau khi kiểm tra xong nếu có không có lỗi mà chỉ có những rule WARNING thì chuyển trạng thái sang WARNING
  VALID Sau khi kiểm tra xong và không có lỗi

# 💻 Giao Diện Người Dùng

- Hiển thị danh sách các điều kiện, phân loại theo `rule_object`.
- Cho phép tick checkbox để đánh dấu đã kiểm tra (`manual_checked_flag`).
- Nút "Đồng bộ rules" để lưu vào hệ thống.
- Nút "Xác nhận đã kiểm tra" cho rule XML để:
  - Kiểm tra tất cả XML rule đã được đánh dấu.
  - Gọi hàm `generateXMLTableByInvoices`.
  - Cập nhật `processing_status`.

# Kiến trúc phần mềm

Bạn đã thiết kế kiến trúc code theo hướng:

- Hook useRuleManagement:

  - Xử lý toàn bộ business logic liên quan đến rules
  - Quản lý data fetching thông qua React Query
  - Cung cấp mutations để update database
  - Cập nhật cache khi thay đổi dữ liệu
  - Cấu trúc các cột của đầu vào ( các props) thì sẽ kham khảo tại RuleManagementFeature.md, cụ thể:
    - currentPatientVisit: patient_visit_mapping_view
    - patientMedicalCodingList : patient_visit_medical_coding_nl_view
    - allSSTables?.ssTable1? : tổng hợp từ ss_table_1, ss_table_2, ss_table_3, ss_table_4, ss_table_5, ss_table_6, ss_table_7

* Các biến, state từ server ( phía backend) trả về
  - ruleVisitDetailNlViewsData
  - RuleRefData
  - validatedRules

- CheckConditionsTab Component:

  - Hiển thị UI cho người dùng tương tác
  - Sử dụng hook useRuleManagement cho logic nghiệp vụ
  - Quản lý UI state riêng biệt
  - Các biến state từ client (phía frontend) dùng để hiển thị lên giao diện
    - ruleVisitDetailDatasource

- ssXmlTableService

  - Tôi có một file tên là ssXmlTableService để gọi các hàm getSsXmlTable từ 1 đến 7. Biết rằng bảng sẽ query về có các đặc điểm như sau:
  - ss_table_1_nl_view , lấy bảng theo patient_visit_id , trả về một object
  - ss_table_2_nl_view, ss_table_3_nl_view,ss_table_4_nl_view ,ss_table_5_nl_view , ss_table_7_nl_view , lấy bảng theo table_1_id , mỗi bảng trả về một array.

- useSSXmlTable

  - get data từ bảng ss_table_1_nl_view đến ss_table_7_nl_view

- ruleService

  - Cung cấp hàm gọi API getData, update
  - Cung cấp các hàm helper (filter, kiểm tra điều kiện) để xử lí local

- Tách biệt rõ ràng:

  - Logic nghiệp vụ nằm trong hooks
  - UI và tương tác người dùng nằm trong components
  - API service tách riêng (ruleService.js)
