# Hiểu về Nghiệp vụ Quản lý Rules
Vâng, từ các file và đoạn code đã chia sẻ, tôi hiểu rõ về nghiệp vụ và yêu cầu của bạn:

# 📋 YÊU CẦU NGHIỆP VỤ: Rule Checking System

## 1. 🎯 Mục Tiêu Tổng Quan

Tạo ra **danh sách điều kiện kiểm tra** (`rule_visit_item_detail`) cho từng lượt khám bệnh nh<PERSON>, phục vụ các chức năng:
- Kiểm tra điều kiện tự động và thủ công.
- G<PERSON> nhận kết quả kiểm tra của hệ thống và người dùng.
- Quyết định các bước xử lý tiếp theo như đẩy XML, cập nhật trạng thái, tạo lịch sử xử lý.

---

## 2. 📥 Dữ Liệu Đầu V<PERSON>

| <PERSON>uồ<PERSON> dữ liệu                 | <PERSON>ô tả                                                              |
|-----------------------------|---------------------------------------------------------------------|
| `rule_ref`                  | Danh sách các rule định nghĩa. Gồm rule_rcd, rule_name, rule_object, warning_type,... |
| `rule_visit_item_detail`    | Bảng lưu kết quả kiểm tra rule theo lượt khám                      |
| `patient_visit`, `ss_table_1` | Dữ liệu từ lượt khám và bảng XML (cho rule loại XML)               |

---

## 3. 🧩 Phân Loại Rule Theo `rule_object`

| `rule_object` | Mô tả                                  |
|---------------|-----------------------------------------|
| `VISIT`       | Rule kiểm tra thông tin hành chính lượt khám |
| `XML`         | Rule kiểm tra thông tin trong bảng `ss_table_1` |
| *(tương lai)* | `CHARGES`, `DRUGS`, `SERVICE`,...     |

---

## 4. 🔄 Quy Trình Kiểm Tra Rule

### 4.1 Ánh xạ `rule_ref` và `rule_visit_item_detail`
- Nếu đã có record → sử dụng lại.
- Nếu chưa có → chuẩn bị tạo mới (`itemsToCreate`).

### 4.2 Kiểm Tra Hệ Thống (`getValidatedRules`)
- Gọi:
  - `validateVisitRule()` nếu rule liên quan đến VISIT.
  - `validateXmlRule()` nếu rule liên quan đến XML.
- Gán `system_checked_flag = true | false`.

### 4.3 Chuẩn Bị Dữ Liệu Lưu
- `itemsToCreate`: các rule chưa tồn tại trong `rule_visit_item_detail`.
- `itemsToUpdate`: các rule đã tồn tại cần cập nhật lại kết quả kiểm tra.

---

## 5. 💻 Giao Diện Người Dùng

- Hiển thị danh sách các điều kiện, phân loại theo `rule_object`.
- Cho phép tick checkbox để đánh dấu đã kiểm tra (`manual_checked_flag`).
- Nút "Đồng bộ rules" để lưu vào hệ thống.
- Nút "Xác nhận đã kiểm tra" cho rule XML để:
  - Kiểm tra tất cả XML rule đã được đánh dấu.
  - Gọi hàm `generateXMLTableByInvoices`.
  - Cập nhật `processing_status`.
  - Tạo record lịch sử xử lý (visit history).

---

## 6. Kiến trúc phần mềm
Bạn đã thiết kế kiến trúc code theo hướng:

1. Hook useRuleManagement:

   * Xử lý toàn bộ business logic liên quan đến rules
   * Quản lý data fetching thông qua React Query
   * Cung cấp mutations để update database
   * Cập nhật cache khi thay đổi dữ liệu
2. CheckConditionsTab Component:

   * Hiển thị UI cho người dùng tương tác
   * Sử dụng hook useRuleManagement cho logic nghiệp vụ
   * Quản lý UI state riêng biệt
3. Tách biệt rõ ràng:

    * Logic nghiệp vụ nằm trong hooks
    * UI và tương tác người dùng nằm trong components
    * API service tách riêng (ruleService.js)
## Yêu cầu cụ thể
Qua các chỉnh sửa gần đây, bạn muốn:

1. Di chuyển mutation updateManualCheckedFlagMutation từ component vào hook
2. Tách riêng phần API và cache khỏi UI logic
3. Đảm bảo hook chỉ xử lý data fetching/update và cache management
4. Giữ UI logic (thông báo, loading) trong component
Việc này đã được thực hiện thành công, giúp phân tách trách nhiệm rõ ràng giữa các lớp trong ứng dụng, dễ bảo trì và mở rộng hệ thống quản lý rules.

## 7. 🚀 Mở Rộng Tương Lai

- Hỗ trợ thêm các loại `rule_object` như:
  - `CHARGE_DETAIL`, `DRUG_USAGE`, `SERVICE_LINE`, ...
- Cho phép cấu hình rule động từ giao diện quản trị.
- Cho phép kiểm tra điều kiện dựa vào dữ liệu đa bảng.

---

## 8. 📌 Định Hướng Refactor
Tách logic kiểm tra rule theo object:

```ts
function validateRuleByObject(rule, visitData, xmlData) {
  switch (rule.rule_object) {
    case 'VISIT':
      return validateVisitRule(rule, visitData, [])
    case 'XML':
      return validateXmlRule(rule, xmlData)
    default:
      return false
  }
}

