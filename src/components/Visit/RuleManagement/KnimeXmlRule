// system imports
import org.knime.base.node.jsnippet.expression.AbstractJSnippet;
import org.knime.base.node.jsnippet.expression.Abort;
import org.knime.base.node.jsnippet.expression.Cell;
import org.knime.base.node.jsnippet.expression.ColumnException;
import org.knime.base.node.jsnippet.expression.TypeException;
import static org.knime.base.node.jsnippet.expression.Type.*;
import java.util.Date;
import java.util.Calendar;
import org.w3c.dom.Document;


// Your custom imports:
import java.util.UUID;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
// system variables
public class JSnippet extends AbstractJSnippet {

  // Fields for output columns
  /** Output column: "system_checked_flag" */
  public Boolean out_system_checked_flag;
  /** Output column: "ss_xml_id" */
  public String out_ss_xml_id;
  /** Output column: "patient_visit_id" */
  public String out_patient_visit_id;
  /** Output column: "ss_xml_column_name" */
  public String out_ss_xml_column_name;
  /** Output column: "rule_visit_item_detail_id" */
  public String out_rule_visit_item_detail_id;
  /** Output column: "manual_checked_flag" */
  public Boolean out_manual_checked_flag;
  /** Output column: "MA_LK" */
  public String out_MA_LK;
  /** Output column: "invoice_no_" */
  public String out_invoice_no_;

// Your custom variables:
// expression start
    public void snippet() throws TypeException, ColumnException, Abort {
// Enter your code here:
LocalDateTime now = LocalDateTime.now();
DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
String formattedDateTime = now.format(formatter);

out_system_checked_flag = false;
double epsilon = 0.0001;
// table 1
if (v_CHAN_DOAN_RV != null && !v_CHAN_DOAN_RV.isEmpty() && c_rule_rcd.equals("CHECK_CHAN_DOAN_RV")) {
	out_system_checked_flag = true;
} 
if (v_CHAN_DOAN_VAO != null && !v_CHAN_DOAN_VAO.isEmpty() && c_rule_rcd.equals("CHECK_CHAN_DOAN_VAO")) {
	out_system_checked_flag = true;
} 
if (Math.abs(v_T_BHTT_XML2 - v_T_THUOC) < epsilon  && c_rule_rcd.equals("CHECK_T_THUOC")) {
	out_system_checked_flag = true;
} 
if (v_T_TONGCHI_BV == (v_THANH_TIEN_BV_XML2 + v_THANH_TIEN_BV_XML3) && c_rule_rcd.equals("CHECK_T_TONGCHI_BV")) {
	out_system_checked_flag = true;
} 
if (v_T_TONGCHI_BH == (v_THANH_TIEN_BH_XML2 + v_THANH_TIEN_BH_XML3) && c_rule_rcd.equals("CHECK_T_TONGCHI_BH")) {
	out_system_checked_flag = true;
} 
if (v_T_BNTT == (v_T_BNTT_XML2 + v_T_BNTT_XML3) && c_rule_rcd.equals("CHECK_T_BNTT")) {
	out_system_checked_flag = true;
} 
if (v_T_BNCCT == (v_T_BNCCT_XML2 + v_T_BNCCT_XML3) && c_rule_rcd.equals("CHECK_T_BNCCT")) {
	out_system_checked_flag = true;
} 
if (v_T_BHTT == (v_T_BHTT_XML2 + v_T_BHTT_XML3) && c_rule_rcd.equals("CHECK_T_BHTT")) {
	out_system_checked_flag = true;
} 
// table 2
if (v_MA_THUOC != null && !v_MA_THUOC.trim().isEmpty() && !v_MA_THUOC.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_MA_THUOC")) {
	out_system_checked_flag = true;
}
if (v_MA_NHOM != null && !v_MA_NHOM.trim().isEmpty() && !v_MA_NHOM.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_MA_NHOM")) {
	out_system_checked_flag = true;
}
if (v_TEN_THUOC != null && !v_TEN_THUOC.trim().isEmpty() && !v_TEN_THUOC.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_TEN_THUOC")) {
	out_system_checked_flag = true;
} 
if (v_DON_VI_TINH != null && !v_DON_VI_TINH.trim().isEmpty() && !v_DON_VI_TINH.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_DON_VI_TINH")) {
	out_system_checked_flag = true;
} 
if (v_HAM_LUONG != null && !v_HAM_LUONG.trim().isEmpty() && !v_HAM_LUONG.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_HAM_LUONG")) {
	out_system_checked_flag = true;
} 
if (v_DUONG_DUNG != null && !v_DUONG_DUNG.trim().isEmpty() && !v_DUONG_DUNG.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_DUONG_DUNG")) {
	out_system_checked_flag = true;
} 
if (v_LIEU_DUNG != null && !v_LIEU_DUNG.trim().isEmpty() && !v_LIEU_DUNG.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_LIEU_DUNG")) {
	out_system_checked_flag = true;
} 
if (v_SO_DANG_KY != null && !v_SO_DANG_KY.trim().isEmpty() && !v_SO_DANG_KY.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_SO_DANG_KY")) {
	out_system_checked_flag = true;
} 
if (v_TT_THAU != null && !v_TT_THAU.trim().isEmpty() && !v_TT_THAU.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_TT_THAU")) {
	out_system_checked_flag = true;
} 
if (v_PHAM_VI != null && !v_PHAM_VI.trim().isEmpty() && !v_PHAM_VI.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_PHAM_VI")) {
	out_system_checked_flag = true;
} 
// table 3
if (v_MA_LOAI_KCB.equals("03") && (v_MA_NHOM_XML3.equals("8") || v_MA_NHOM_XML3.equals("18"))) {
	if (v_PP_VO_CAM != null && !v_PP_VO_CAM.trim().isEmpty() && !v_PP_VO_CAM.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_PP_VO_CAM")) {
	out_system_checked_flag = true;
	} 
}
// table 4
if (v_MA_DICH_VU != null && !v_MA_DICH_VU.trim().isEmpty() && !v_MA_DICH_VU.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_MA_DICH_VU")) {
	out_system_checked_flag = true;
} 
if (v_MA_NHOM_XML3.equals("1") || v_MA_NHOM_XML3.equals("2") || v_MA_NHOM_XML3.equals("3")) {
	if (v_TEN_CHI_SO != null && !v_TEN_CHI_SO.trim().isEmpty() && !v_TEN_CHI_SO.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_TEN_CHI_SO")) {
	out_system_checked_flag = true;
	} 
}
if (v_MA_NHOM_XML3.equals("1") || v_MA_NHOM_XML3.equals("2") || v_MA_NHOM_XML3.equals("3")) {
	if (v_MA_CHI_SO != null && !v_MA_CHI_SO.trim().isEmpty() && !v_MA_CHI_SO.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_MA_CHI_SO")) {
	out_system_checked_flag = true;
	} 
}
if (v_MA_NHOM_XML3.equals("1") || v_MA_NHOM_XML3.equals("2") || v_MA_NHOM_XML3.equals("3")) {
	if (v_GIA_TRI != null && !v_GIA_TRI.trim().isEmpty() && !v_GIA_TRI.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_GIA_TRI")) {
	out_system_checked_flag = true;
	} 
}
if (v_MA_NHOM_XML3.equals("1") || v_MA_NHOM_XML3.equals("2") || v_MA_NHOM_XML3.equals("3")) {
	if (v_DON_VI_DO != null && !v_DON_VI_DO.trim().isEmpty() && !v_DON_VI_DO.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_DON_VI_DO")) {
	out_system_checked_flag = true;
	} 
}
if (v_MA_NHOM_XML3.equals("2")) {
	if (v_KET_LUAN != null && !v_KET_LUAN.trim().isEmpty() && !v_KET_LUAN.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_KET_LUAN")) {
	out_system_checked_flag = true;
	} 
}
if (v_GIA_TRI != null && !v_GIA_TRI.trim().isEmpty() && !v_GIA_TRI.trim().equalsIgnoreCase("missing")) {
	if (v_NGAY_KQ != null && !v_NGAY_KQ.trim().isEmpty() && !v_NGAY_KQ.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_NGAY_KQ")) {
	out_system_checked_flag = true;
	} 
} else if (v_NGAY_KQ != null && !v_NGAY_KQ.trim().isEmpty() && !v_NGAY_KQ.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_NGAY_KQ")) {
	out_system_checked_flag = true;
}
if (v_MA_BS_DOC_KQ != null && !v_MA_BS_DOC_KQ.trim().isEmpty() && !v_MA_BS_DOC_KQ.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_MA_BS_DOC_KQ")) {
	out_system_checked_flag = true;
}
// table 7
if (v_PP_DIEUTRI != null && !v_PP_DIEUTRI.trim().isEmpty() && !v_PP_DIEUTRI.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_PP_DIEUTRI")) {
	out_system_checked_flag = true;
} 
if (v_TEN_BS != null && !v_TEN_BS.trim().isEmpty() && !v_TEN_BS.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_TEN_BS")) {
	out_system_checked_flag = true;
} 
if (v_MA_BS != null && !v_MA_BS.trim().isEmpty() && !v_MA_BS.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_MA_BS")) {
	out_system_checked_flag = true;
} 
if (v_NGAY_CT != null && !v_NGAY_CT.trim().isEmpty() && !v_NGAY_CT.trim().equalsIgnoreCase("missing") && c_rule_rcd.equals("CHECK_NGAY_CT")) {
	out_system_checked_flag = true;
} 

out_manual_checked_flag = false;
out_ss_xml_id = v_table_1_id;
out_patient_visit_id = v_patient_visit_id__;
out_ss_xml_column_name = c_rule_rcd.replace("CHECK_", "");
out_rule_visit_item_detail_id = UUID.randomUUID().toString();
out_lu_updated = formattedDateTime;
out_MA_LK = v_MA_LK;
out_invoice_no_ = v_invoice_no_;










// expression end
    }
}
