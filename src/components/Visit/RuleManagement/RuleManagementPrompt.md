# Trong Quy Trình <PERSON> , cụ thể là chức năng Kiểm Tra Hệ <PERSON>ố<PERSON> (`getValidatedRules`) 


Trong đoạn code tôi khoanh vùng trên, nếu tôi muốn các hàm validateXmlRule hay validateVisitRule thay vì chỉ trả true false , tôi muốn trả luôn một objeect  có cấu trúc như  dưới này: 

```
{
ar_invoice_detail_id: null,
charge_detail_id: null,
rule_rcd: item?.rule_rcd,
patient_visit_id: mainPatientVisitId,
system_checked_flag: item.system_checked_flag || false,
manual_checked_flag: false, // Default value cho item mới
MA_LK: dataObject.MA_LK || null,
ss_xml_id: dataObject.table_1_id || null,
ss_xml_column_name: getColumnNameForRule(item?.rule_rcd),
invoice_no_: dataObject.invoice_no_ || null,
ss_xml_table_name: item?.rule_object === 'XML' ? 'ss_table_1' : 'patient_visit',
lu_updated: dayjs(),
lu_user_id: currentUser?.User_id,
}
```

Biết rằng validateXmlRule hay validateVisitRule sẽ trả lại object khác nhau đôi chút:

Trong hàm validateXmlRule các điểm cần chú ý:
Dữ liệu trả về: 

```
{
ar_invoice_detail_id: null,
charge_detail_id: null,
rule_rcd: item?.rule_rcd,
patient_visit_id: mainPatientVisitId,
system_checked_flag: item.system_checked_flag || false,
manual_checked_flag: false, // Default value cho item mới
MA_LK: {maLK} || null,
ss_xml_id: {ssXmlId} || null,
ss_xml_column_name: getColumnNameForRule(item?.rule_rcd),
invoice_no_: {invoiceNo} || null,
ss_xml_table_name: {ssXmlTableName},
lu_updated: dayjs(),
lu_user_id: currentUser?.User_id,
}
```


* _{ssXmlTableName} là tên bảng hiện tại đang thực hiện;{ssXmlId} là giá trị id của record nằm trong bảng ssXmlTableName đang xét, {maLK} luôn lấy ssTable1.MALK, {invoiceNo} luôn lấy {ssTable1.invoiceno}_
* một số item rule_rcd sẽ dùng bảng ssTable2 hoặc ssTable3 hoặc ssTable4 để kiểm tra đúng sai, không phải chỉ dùng mỗi ssTable1
* trong hàm luôn khởi tạo các biến như systemCheckedFlag, ssXmlId, ssXmlColumnName, invoiceNo, ssXmlTableName đầu tiên để khi kiểm tra item?.rule_rcd là gì thì sẽ gán giá tị tương ứng
* tham số đầu vào rule, allSsTables

Trong hàm validateVisitRule các điểm cần chú ý:
* Dữ liệu trả về: 
    ```
    {
    ar_invoice_detail_id: null,
    charge_detail_id: null,
    rule_rcd: item?.rule_rcd,
    patient_visit_id: mainPatientVisitId,
    system_checked_flag: item.system_checked_flag || false,
    manual_checked_flag: false, // Default value cho item mới
    MA_LK: null,
    ss_xml_id:  null,
    ss_xml_column_name: null,
    invoice_no_:  null,
    ss_xml_table_name: null,
    lu_updated: dayjs(),
    lu_user_id: currentUser?.User_id,
    }
    `
* tham số đầu vào rule, patientVisit, medicalCodings

Yêu cầu thêm: 
* bạn có thể hạn chế ghi đoạn code dưới này nhiều nhất có thể
```
    {
    ar_invoice_detail_id: null,
    charge_detail_id: null,
    rule_rcd: item?.rule_rcd,
    patient_visit_id: mainPatientVisitId,
    system_checked_flag: item.system_checked_flag || false,
    manual_checked_flag: false, // Default value cho item mới
    MA_LK: null,
    ss_xml_id:  null,
    ss_xml_column_name: null,
    invoice_no_:  null,
    ss_xml_table_name: null,
    lu_updated: dayjs(),
    lu_user_id: currentUser?.User_id,
    }
    `
* trong getValidatedRules , tham số context lúc này là allSSTables , không phải các table tách riêng ra
* trong các hàm validateXmlRule và validateVisitRule vẫn cứ giữ nguyên các điều kiện kiểm tra item.rule_rcd có sẵn 

<!-- Ngày 14-05-2025 , yêu cầu thêm: Bạn có thể viết tiếp giúp tôi các điều kiểm tra tại hàm validateXmlRule tiếp được không, biết rằng 
* Xem các rule xml cần viết tiếp tại file #RuleXML.xlsx
* Chỉ tìm rule có Status == Open  -->

14-05-2025
Nhờ bạn chỉnh sửa lại hàm validateXmlRule tại #file:ruleService.js như sau:

Kết quả đầu ra cuối cùng hàm trả về là mảng thay vì object như trước
Trong quá trình kiểm tra từng case: nếu case đó phải sử lí array có số dòng n , thì trả lại số n array cấu trúc baseRuleVisitItemDetail tương ứng, xử lí object thì trả về object , nói chung là xử lí từng dòng. Ví dụ sử lí 3 dòng của ssTable3 thì trả về 3 dòng ruleVisitItemDetail tương ứng. Nếu không có dữ liệu thì không cần tạo ruleVisitItemDetail mặc định.
ssTable2, ssTable3, ssTable4, ssTable5, ssTable7 là mảng, nên phải luôn luôn kiểm tra từng dòng, nếu dòng kiểm tra hợp lệ hay không thì vẫn sẽ tạo item ruleVisitItemDetail tương ứng. Nếu từng dòng không có nhóm cần kiểm tra thì kết quả luôn đúng
Tra tại file #file:RuleManagementDatabase.md và #file:allSSTableData.md và #file:RuleManagementFeature.md để nắm thông tin data và cấu trúc kiểm tra
Xem các rule xml cần viết tiếp tại file #file:RuleXML.xlsx
Chỉ tìm rule có Status == Open và nằm ở XML 4, XML 5, XML 7
Dùng const MA_NHOM_XET_NGHIEM_HINH_ANH_THAM_DO_RCD để đại diện ['1','2','3']
ss_table_4 có cột MA_NHOM, không cần phải lấy MA_NHOM từ ss_table_3 xem tại #file:RuleManagementDatabase.md và #file:allSSTableData.md
Chỉnh lại code của hàm liên quan khác nếu bị ảnh hưởng.
Không cần phải gán lại giá trị ssXmlTableName trong hàm switch
Trả lời phản hồi bằng tiếng việt.

Xử lý tất cả các dòng trong ssTable4 không cần filter trước
Kiểm tra điều kiện đúng riêng cho từng dòng: nếu dòng đó có it.GIA_TRI thì yêu cầu item.NGAY_KQ phai có giá trị, ngược lại luôn đánh giá đúng.
Tạo kết quả kiểm tra riêng cho từng dòng


19-05-2025
1. Tại file #useRuleManagement quản lí các biến từ server: 
    * ruleVisitDetailNlViewsData
    * RuleRefData
    * validatedRules
2. Tại file #CheckConditionsTab quản lí các biến như 
    * ruleVisitDetailDatasource
Tôi muốn tên biến cũng phản ánh được cấu trúc data lưu trữ vì vậy hãy đổi tên các biến sau
* Trong 1., đổi validatedRules thành validatedRuleVisitDetailNlViews
* Trong 2., đổi ruleVisitDetailDatasource thành validatedRuleVisitDetailNlViewsDatasource
