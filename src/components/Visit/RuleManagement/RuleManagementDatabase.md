# Using this script to get description

DECLARE @TableName NVARCHAR(128) = 'ss_table_1_nl_view';
DECLARE @Line NVARCHAR(MAX);

DECLARE cur CURSOR FOR
SELECT 
    CAST(ROW_NUMBER() OVER (ORDER BY c.column_id) AS VARCHAR) + '. ' +
    c.name + ' | ' + 
    t.name + ' | Len: ' + CAST(c.max_length AS VARCHAR) +
    ' | Nullable: ' + CASE WHEN c.is_nullable = 1 THEN 'Yes' ELSE 'No' END +
    ' | Identity: ' + CASE WHEN c.is_identity = 1 THEN 'Yes' ELSE 'No' END
FROM sys.columns c
JOIN sys.types t ON c.user_type_id = t.user_type_id
WHERE c.object_id = OBJECT_ID(@TableName);

-- In tiêu đề bảng
PRINT 'Table: ' + @TableName;

-- Duyệt từng dòng và in ra
OPEN cur;
FETCH NEXT FROM cur INTO @Line;

WHIL<PERSON> @@FETCH_STATUS = 0
BEGIN
    PRINT @Line;
    FETCH NEXT FROM cur INTO @Line;
END

CLOSE cur;
DEALLOCATE cur;

# Table: rule_group_ref

1. rule_group_rcd | nvarchar | Len: 500 | Nullable: No | Identity: No
2. name | nvarchar | Len: 500 | Nullable: Yes | Identity: No
3. rule_object | nvarchar | Len: 500 | Nullable: Yes | Identity: No
4. active_flag | bit | Len: 1 | Nullable: Yes | Identity: No
5. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
6. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No

# Table: rule_item_mapping

1. rule_item_mapping_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. item_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
3. rule_group_rcd | nvarchar | Len: 500 | Nullable: Yes | Identity: No
4. rule_rcd | nvarchar | Len: 500 | Nullable: Yes | Identity: No
5. active_flag | bit | Len: 1 | Nullable: Yes | Identity: No
6. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
7. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No

# Table: rule_ref

1. rule_rcd | nvarchar | Len: 500 | Nullable: No | Identity: No
2. name | nvarchar | Len: 1000 | Nullable: Yes | Identity: No
3. error_message | nvarchar | Len: -1 | Nullable: Yes | Identity: No
4. rule_group_rcd | nvarchar | Len: 500 | Nullable: Yes | Identity: No
5. rule_object | nvarchar | Len: 500 | Nullable: Yes | Identity: No
6. warning_type | nvarchar | Len: 100 | Nullable: Yes | Identity: No
7. active_flag | bit | Len: 1 | Nullable: Yes | Identity: No
8. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
9. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No

# Table: rule_visit_item_detail

1. rule_visit_item_detail_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. rule_rcd | nvarchar | Len: 500 | Nullable: Yes | Identity: No
3. rule_item_mapping_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
4. system_checked_flag | bit | Len: 1 | Nullable: Yes | Identity: No
5. manual_checked_flag | bit | Len: 1 | Nullable: Yes | Identity: No
6. patient_visit_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
7. charge_detail_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
8. ar_invoice_detail_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
9. ss_xml_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
10. ss_xml_column_name | nvarchar | Len: 500 | Nullable: Yes | Identity: No
11. ss_xml_table_name | nvarchar | Len: 500 | Nullable: Yes | Identity: No
12. MA_LK | nvarchar | Len: 500 | Nullable: Yes | Identity: No
13. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
14. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
15. invoice*no* | nvarchar | Len: -1 | Nullable: Yes | Identity: No

# Table: rule_visit_item_detail_note

1. rule_visit_item_detail_note_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. note | nvarchar | Len: -1 | Nullable: Yes | Identity: No
3. rule_visit_item_detail_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
4. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
5. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
6. created_by | nvarchar | Len: 1000 | Nullable: Yes | Identity: No

# Table: rule_visit_item_detail_nl_view

1. rule_visit_item_detail_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. rule_rcd | nvarchar | Len: 500 | Nullable: Yes | Identity: No
3. rule_item_mapping_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
4. system_checked_flag | bit | Len: 1 | Nullable: Yes | Identity: No
5. manual_checked_flag | bit | Len: 1 | Nullable: Yes | Identity: No
6. patient_visit_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
7. charge_detail_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
8. ar_invoice_detail_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
9. ss_xml_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
10. ss_xml_column_name | nvarchar | Len: 500 | Nullable: Yes | Identity: No
11. ss_xml_table_name | nvarchar | Len: 500 | Nullable: Yes | Identity: No
12. MA_LK | nvarchar | Len: 500 | Nullable: Yes | Identity: No
13. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
14. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
15. invoice*no* | nvarchar | Len: -1 | Nullable: Yes | Identity: No
16. rule_name | nvarchar | Len: 1000 | Nullable: Yes | Identity: No
17. rule_error_message | nvarchar | Len: -1 | Nullable: Yes | Identity: No
18. rule_object | nvarchar | Len: 500 | Nullable: Yes | Identity: No
19. warning_type | nvarchar | Len: 100 | Nullable: Yes | Identity: No
20. visit_code | varchar | Len: 20 | Nullable: Yes | Identity: No

# Table: rule_visit_item_detail_note_nl_view

1. rule_visit_item_detail_note_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. note | nvarchar | Len: -1 | Nullable: Yes | Identity: No
3. rule_visit_item_detail_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
4. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
5. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
6. created_by | nvarchar | Len: 1000 | Nullable: Yes | Identity: No
7. rule_name | nvarchar | Len: 1000 | Nullable: Yes | Identity: No

# Table: patient_visit_mapping_view

1. patient_visit_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. treatment_course_flag | bit | Len: 1 | Nullable: Yes | Identity: No
3. treatment_course_start_date | datetime | Len: 8 | Nullable: Yes | Identity: No
4. treatment_course_end_date | datetime | Len: 8 | Nullable: Yes | Identity: No
5. patient_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
6. warning_status | nvarchar | Len: 500 | Nullable: Yes | Identity: No
7. free_copay_flag | bit | Len: 1 | Nullable: Yes | Identity: No
8. right_channel_flag | bit | Len: 1 | Nullable: Yes | Identity: No
9. serious_illness_flag | bit | Len: 1 | Nullable: Yes | Identity: No
10. note | nvarchar | Len: -1 | Nullable: Yes | Identity: No
11. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
12. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
13. free_copay_start_date | datetime | Len: 8 | Nullable: Yes | Identity: No
14. free_copay_end_date | datetime | Len: 8 | Nullable: Yes | Identity: No
15. emergency_case_flag | bit | Len: 1 | Nullable: Yes | Identity: No
16. referral_disposition_rcd | nvarchar | Len: 100 | Nullable: Yes | Identity: No
17. referral_code | nvarchar | Len: 500 | Nullable: Yes | Identity: No
18. medical_record_code | nvarchar | Len: 100 | Nullable: Yes | Identity: No
19. referral_reason | nvarchar | Len: 500 | Nullable: Yes | Identity: No
20. referral_date | datetime | Len: 8 | Nullable: Yes | Identity: No
21. referral_type_rcd | varchar | Len: 10 | Nullable: Yes | Identity: No
22. serious_illness_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
23. designated_illness_reason | nvarchar | Len: -1 | Nullable: Yes | Identity: No
24. card_type_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
25. processing_status | nvarchar | Len: 500 | Nullable: Yes | Identity: No
26. sync_visit_charge_status | nvarchar | Len: 500 | Nullable: Yes | Identity: No
27. ethnicity_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
28. occupation_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
29. parent_patient_visit_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
30. NGAY_RA | datetime | Len: 8 | Nullable: Yes | Identity: No
31. TEN_BS | nvarchar | Len: 1000 | Nullable: Yes | Identity: No
32. MA_BS | varchar | Len: 500 | Nullable: Yes | Identity: No
33. KET_QUA_DTRI | varchar | Len: 50 | Nullable: Yes | Identity: No
34. MA_LOAI_RV | varchar | Len: 50 | Nullable: Yes | Identity: No
35. KET_QUA_DTRI_DESCRIPTION | nvarchar | Len: 500 | Nullable: Yes | Identity: No
36. MA_LOAI_RV_DESCRIPTION | nvarchar | Len: 500 | Nullable: Yes | Identity: No
37. serious_illness_icd10id | nvarchar | Len: 100 | Nullable: Yes | Identity: No
38. serious_illness_icd_code | nvarchar | Len: 100 | Nullable: Yes | Identity: No
39. processing_person_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
40. processing_person_name | nvarchar | Len: 1000 | Nullable: Yes | Identity: No
41. ss_confirmation_flag | bit | Len: 1 | Nullable: Yes | Identity: No
42. ss_confirmation_note | nvarchar | Len: -1 | Nullable: Yes | Identity: No
43. signed_health_insurance_consent_form_flag | bit | Len: 1 | Nullable: Yes | Identity: No
44. visible_patient_id | varchar | Len: 20 | Nullable: Yes | Identity: No
45. fullname | nvarchar | Len: 602 | Nullable: Yes | Identity: No
46. dob | datetime | Len: 8 | Nullable: Yes | Identity: No
47. sex | nvarchar | Len: 300 | Nullable: Yes | Identity: No
48. visit_code | varchar | Len: 20 | Nullable: Yes | Identity: No
49. visit_type_rcd | char | Len: 6 | Nullable: Yes | Identity: No
50. visit_type_name_e | nvarchar | Len: 300 | Nullable: Yes | Identity: No
51. visit_type_group_rcd | nvarchar | Len: 300 | Nullable: Yes | Identity: No
52. actual_visit_datetime | datetime | Len: 8 | Nullable: Yes | Identity: No
53. closure_visit_datetime | datetime | Len: 8 | Nullable: Yes | Identity: No
54. creation_date_time | datetime | Len:

# Table: ss_table_1_nl_view
1. table_1_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. include | bit | Len: 1 | Nullable: No | Identity: No
3. patient_visit_id__ | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
4. employee_id__ | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
5. ma_doituong_kcb_ | nvarchar | Len: 500 | Nullable: Yes | Identity: No
6. invoice_no_ | varchar | Len: 40 | Nullable: Yes | Identity: No
7. gioi_tinh_ | nvarchar | Len: 30 | Nullable: No | Identity: No
8. ket_qua_dtri_ | nvarchar | Len: 500 | Nullable: Yes | Identity: No
9. ma_loai_rv_ | nvarchar | Len: 500 | Nullable: Yes | Identity: No
10. muc_huong_ | int | Len: 4 | Nullable: Yes | Identity: No
11. ma_bac_si_ | nvarchar | Len: 500 | Nullable: Yes | Identity: No
12. tyle_tt | int | Len: 4 | Nullable: Yes | Identity: No
13. MA_LK | varchar | Len: 250 | Nullable: Yes | Identity: No
14. STT | int | Len: 4 | Nullable: Yes | Identity: No
15. MA_BN | varchar | Len: 20 | Nullable: Yes | Identity: No
16. HO_TEN | nvarchar | Len: 512 | Nullable: Yes | Identity: No
17. SO_CCCD | varchar | Len: 50 | Nullable: Yes | Identity: No
18. NGAY_SINH | datetime | Len: 8 | Nullable: Yes | Identity: No
19. GIOI_TINH | int | Len: 4 | Nullable: Yes | Identity: No
20. NHOM_MAU | varchar | Len: 50 | Nullable: Yes | Identity: No
21. MA_QUOCTICH | varchar | Len: 50 | Nullable: Yes | Identity: No
22. MA_DANTOC | varchar | Len: 50 | Nullable: Yes | Identity: No
23. MA_NGHE_NGHIEP | varchar | Len: 50 | Nullable: Yes | Identity: No
24. DIA_CHI | nvarchar | Len: 4000 | Nullable: Yes | Identity: No
25. MATINH_CU_TRU | varchar | Len: 50 | Nullable: Yes | Identity: No
26. MAHUYEN_CU_TRU | varchar | Len: 50 | Nullable: Yes | Identity: No
27. MAXA_CU_TRU | varchar | Len: 50 | Nullable: Yes | Identity: No
28. DIEN_THOAI | varchar | Len: 50 | Nullable: Yes | Identity: No
29. MA_THE_BHYT | varchar | Len: 250 | Nullable: Yes | Identity: No
30. MA_DKBD | nvarchar | Len: 500 | Nullable: Yes | Identity: No
31. GT_THE_TU | datetime | Len: 8 | Nullable: Yes | Identity: No
32. GT_THE_DEN | datetime | Len: 8 | Nullable: Yes | Identity: No
33. NGAY_MIEN_CCT | datetime | Len: 8 | Nullable: Yes | Identity: No
34. LY_DO_VV | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
35. LY_DO_VNT | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
36. MA_LY_DO_VNT | varchar | Len: 50 | Nullable: Yes | Identity: No
37. CHAN_DOAN_VAO | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
38. CHAN_DOAN_RV | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
39. MA_BENH_CHINH | varchar | Len: 50 | Nullable: Yes | Identity: No
40. MA_BENH_KT | varchar | Len: 150 | Nullable: Yes | Identity: No
41. MA_BENH_YHCT | varchar | Len: 300 | Nullable: Yes | Identity: No
42. MA_PTTT_QT | varchar | Len: 150 | Nullable: Yes | Identity: No
43. MA_DOITUONG_KCB | int | Len: 4 | Nullable: Yes | Identity: No
44. MA_NOI_DI | varchar | Len: 5 | Nullable: Yes | Identity: No
45. MA_NOI_DEN | varchar | Len: 5 | Nullable: Yes | Identity: No
46. MA_TAI_NAN | varchar | Len: 1 | Nullable: Yes | Identity: No
47. NGAY_VAO | datetime | Len: 8 | Nullable: Yes | Identity: No
48. NGAY_VAO_NOI_TRU | datetime | Len: 8 | Nullable: Yes | Identity: No
49. NGAY_RA | datetime | Len: 8 | Nullable: Yes | Identity: No
50. GIAY_CHUYEN_TUYEN | varchar | Len: 50 | Nullable: Yes | Identity: No
51. SO_NGAY_DTRI | int | Len: 4 | Nullable: Yes | Identity: No
52. PP_DIEU_TRI | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
53. KET_QUA_DTRI | varchar | Len: 50 | Nullable: Yes | Identity: No
54. MA_LOAI_RV | varchar | Len: 50 | Nullable: Yes | Identity: No
55. GHI_CHU | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
56. NGAY_TTOAN | datetime | Len: 8 | Nullable: Yes | Identity: No
57. T_THUOC | money | Len: 8 | Nullable: Yes | Identity: No
58. T_VTYT | money | Len: 8 | Nullable: Yes | Identity: No
59. T_TONGCHI_BV | money | Len: 8 | Nullable: Yes | Identity: No
60. T_TONGCHI_BH | money | Len: 8 | Nullable: Yes | Identity: No
61. T_BNTT | money | Len: 8 | Nullable: Yes | Identity: No
62. T_BNCCT | money | Len: 8 | Nullable: Yes | Identity: No
63. T_BHTT | money | Len: 8 | Nullable: Yes | Identity: No
64. T_NGUONKHAC | money | Len: 8 | Nullable: Yes | Identity: No
65. T_BHTT_GDV | money | Len: 8 | Nullable: Yes | Identity: No
66. NAM_QT | varchar | Len: 50 | Nullable: Yes | Identity: No
67. THANG_QT | varchar | Len: 50 | Nullable: Yes | Identity: No
68. MA_LOAI_KCB | varchar | Len: 50 | Nullable: Yes | Identity: No
69. MA_KHOA | varchar | Len: 50 | Nullable: Yes | Identity: No
70. MA_CSKCB | varchar | Len: 50 | Nullable: Yes | Identity: No
71. MA_KHUVUC | varchar | Len: 5 | Nullable: Yes | Identity: No
72. CAN_NANG | decimal | Len: 9 | Nullable: Yes | Identity: No
73. CAN_NANG_CON | varchar | Len: 150 | Nullable: Yes | Identity: No
74. NAM_NAM_LIEN_TUC | datetime | Len: 8 | Nullable: Yes | Identity: No
75. NGAY_TAI_KHAM | datetime | Len: 8 | Nullable: Yes | Identity: No
76. MA_HSBA | varchar | Len: 150 | Nullable: Yes | Identity: No
77. MA_TTDV | varchar | Len: 50 | Nullable: Yes | Identity: No
78. DU_PHONG | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
79. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
80. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
81. approve_status | nvarchar | Len: 500 | Nullable: Yes | Identity: No
82. warning_status | nvarchar | Len: 500 | Nullable: Yes | Identity: No
83. gate_status | nvarchar | Len: 500 | Nullable: Yes | Identity: No
84. file_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
85. ar_invoice_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
86. maKetQua | varchar | Len: 50 | Nullable: Yes | Identity: No
87. maGiaoDich | varchar | Len: 50 | Nullable: Yes | Identity: No
88. thongDiep | nvarchar | Len: 1100 | Nullable: Yes | Identity: No
89. thoiGianTiepNhan | varchar | Len: 50 | Nullable: Yes | Identity: No



# Table: ss_table_2_nl_view
1. table_2_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. table_1_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
3. include | bit | Len: 1 | Nullable: Yes | Identity: No
4. patient_visit_id__ | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
5. ngay_vao | datetime | Len: 8 | Nullable: Yes | Identity: No
6. hn_ | varchar | Len: 20 | Nullable: No | Identity: No
7. ho_ten_ | nvarchar | Len: 512 | Nullable: No | Identity: No
8. invoice_no_ | varchar | Len: 40 | Nullable: No | Identity: No
9. item_code_ | nvarchar | Len: 40 | Nullable: No | Identity: No
10. ma_bac_si_ | nvarchar | Len: 500 | Nullable: Yes | Identity: No
11. MA_LK | varchar | Len: 250 | Nullable: Yes | Identity: No
12. STT | int | Len: 4 | Nullable: Yes | Identity: No
13. MA_THUOC | varchar | Len: 500 | Nullable: Yes | Identity: No
14. MA_PP_CHEBIEN | varchar | Len: 500 | Nullable: Yes | Identity: No
15. MA_CSKCB_THUOC | varchar | Len: 50 | Nullable: Yes | Identity: No
16. MA_NHOM | varchar | Len: 50 | Nullable: Yes | Identity: No
17. TEN_THUOC | nvarchar | Len: 4000 | Nullable: Yes | Identity: No
18. DON_VI_TINH | nvarchar | Len: 100 | Nullable: Yes | Identity: No
19. HAM_LUONG | varchar | Len: 1000 | Nullable: Yes | Identity: No
20. DUONG_DUNG | varchar | Len: 50 | Nullable: Yes | Identity: No
21. DANG_BAO_CHE | nvarchar | Len: 4000 | Nullable: Yes | Identity: No
22. LIEU_DUNG | nvarchar | Len: 4000 | Nullable: Yes | Identity: No
23. CACH_DUNG | nvarchar | Len: 4000 | Nullable: Yes | Identity: No
24. SO_DANG_KY | nvarchar | Len: 1000 | Nullable: Yes | Identity: No
25. TT_THAU | nvarchar | Len: 200 | Nullable: Yes | Identity: No
26. PHAM_VI | varchar | Len: 50 | Nullable: Yes | Identity: No
27. TYLE_TT_BH | int | Len: 4 | Nullable: Yes | Identity: No
28. SO_LUONG | decimal | Len: 9 | Nullable: Yes | Identity: No
29. DON_GIA | money | Len: 8 | Nullable: Yes | Identity: No
30. THANH_TIEN_BV | money | Len: 8 | Nullable: Yes | Identity: No
31. THANH_TIEN_BH | money | Len: 8 | Nullable: Yes | Identity: No
32. T_NGUONKHAC_NSNN | money | Len: 8 | Nullable: Yes | Identity: No
33. T_NGUONKHAC_VTNN | money | Len: 8 | Nullable: Yes | Identity: No
34. T_NGUONKHAC_VTTN | money | Len: 8 | Nullable: Yes | Identity: No
35. T_NGUONKHAC_CL | money | Len: 8 | Nullable: Yes | Identity: No
36. T_NGUONKHAC | money | Len: 8 | Nullable: Yes | Identity: No
37. MUC_HUONG | int | Len: 4 | Nullable: Yes | Identity: No
38. T_BNTT | money | Len: 8 | Nullable: Yes | Identity: No
39. T_BNCCT | money | Len: 8 | Nullable: Yes | Identity: No
40. T_BHTT | money | Len: 8 | Nullable: Yes | Identity: No
41. MA_KHOA | varchar | Len: 50 | Nullable: Yes | Identity: No
42. MA_BAC_SI | varchar | Len: 500 | Nullable: Yes | Identity: No
43. MA_DICH_VU | varchar | Len: 500 | Nullable: Yes | Identity: No
44. NGAY_YL | datetime | Len: 8 | Nullable: Yes | Identity: No
45. NGAY_TH_YL | datetime | Len: 8 | Nullable: Yes | Identity: No
46. MA_PTTT | varchar | Len: 50 | Nullable: Yes | Identity: No
47. NGUON_CTRA | varchar | Len: 50 | Nullable: Yes | Identity: No
48. VET_THUONG_TP | varchar | Len: 50 | Nullable: Yes | Identity: No
49. DU_PHONG | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
50. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
51. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
52. warning_status | nvarchar | Len: 500 | Nullable: Yes | Identity: No
53. TEN_HOAT_CHAT | nvarchar | Len: 2048 | Nullable: Yes | Identity: No


# Table: ss_table_3_nl_view
1. table_3_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. table_1_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
3. include | bit | Len: 1 | Nullable: No | Identity: No
4. patient_visit_id__ | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
5. hn_ | varchar | Len: 20 | Nullable: No | Identity: No
6. ho_ten_ | nvarchar | Len: 512 | Nullable: No | Identity: No
7. invoice_no_ | varchar | Len: 40 | Nullable: No | Identity: No
8. charge_detail_id_ | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
9. item_code_ | nvarchar | Len: 40 | Nullable: No | Identity: No
10. ss_item_id__ | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
11. ma_bac_si_ | nvarchar | Len: 500 | Nullable: Yes | Identity: No
12. ngay_vao | datetime | Len: 8 | Nullable: Yes | Identity: No
13. ngay_ra | datetime | Len: 8 | Nullable: Yes | Identity: No
14. MA_LK | varchar | Len: 250 | Nullable: Yes | Identity: No
15. STT | int | Len: 4 | Nullable: Yes | Identity: No
16. MA_DICH_VU | varchar | Len: 100 | Nullable: Yes | Identity: No
17. MA_PTTT_QT | varchar | Len: 500 | Nullable: Yes | Identity: No
18. MA_VAT_TU | varchar | Len: 500 | Nullable: Yes | Identity: No
19. MA_NHOM | varchar | Len: 50 | Nullable: Yes | Identity: No
20. GOI_VTYT | varchar | Len: 50 | Nullable: Yes | Identity: No
21. TEN_VAT_TU | nvarchar | Len: 4000 | Nullable: Yes | Identity: No
22. TEN_DICH_VU | nvarchar | Len: 4000 | Nullable: Yes | Identity: No
23. MA_XANG_DAU | varchar | Len: 50 | Nullable: Yes | Identity: No
24. DON_VI_TINH | nvarchar | Len: 200 | Nullable: Yes | Identity: No
25. PHAM_VI | varchar | Len: 50 | Nullable: Yes | Identity: No
26. SO_LUONG | numeric | Len: 9 | Nullable: Yes | Identity: No
27. DON_GIA_BV | money | Len: 8 | Nullable: Yes | Identity: No
28. DON_GIA_BH | money | Len: 8 | Nullable: Yes | Identity: No
29. TT_THAU | varchar | Len: 50 | Nullable: Yes | Identity: No
30. TYLE_TT_DV | decimal | Len: 9 | Nullable: Yes | Identity: No
31. TYLE_TT_BH | decimal | Len: 9 | Nullable: Yes | Identity: No
32. THANH_TIEN_BV | money | Len: 8 | Nullable: Yes | Identity: No
33. THANH_TIEN_BH | money | Len: 8 | Nullable: Yes | Identity: No
34. T_TRANTT | money | Len: 8 | Nullable: Yes | Identity: No
35. MUC_HUONG | int | Len: 4 | Nullable: Yes | Identity: No
36. T_NGUONKHAC_NSNN | money | Len: 8 | Nullable: Yes | Identity: No
37. T_NGUONKHAC_VTNN | money | Len: 8 | Nullable: Yes | Identity: No
38. T_NGUONKHAC_VTTN | money | Len: 8 | Nullable: Yes | Identity: No
39. T_NGUONKHAC_CL | money | Len: 8 | Nullable: Yes | Identity: No
40. T_NGUONKHAC | money | Len: 8 | Nullable: Yes | Identity: No
41. T_BNTT | money | Len: 8 | Nullable: Yes | Identity: No
42. T_BNCCT | money | Len: 8 | Nullable: Yes | Identity: No
43. T_BHTT | money | Len: 8 | Nullable: Yes | Identity: No
44. MA_KHOA | varchar | Len: 50 | Nullable: Yes | Identity: No
45. MA_GIUONG | varchar | Len: 50 | Nullable: Yes | Identity: No
46. MA_BAC_SI | varchar | Len: 500 | Nullable: Yes | Identity: No
47. NGUOI_THUC_HIEN | varchar | Len: 500 | Nullable: Yes | Identity: No
48. MA_BENH | varchar | Len: 500 | Nullable: Yes | Identity: No
49. MA_BENH_YHCT | varchar | Len: 500 | Nullable: Yes | Identity: No
50. NGAY_YL | datetime | Len: 8 | Nullable: Yes | Identity: No
51. NGAY_TH_YL | datetime | Len: 8 | Nullable: Yes | Identity: No
52. NGAY_KQ | datetime | Len: 8 | Nullable: Yes | Identity: No
53. MA_PTTT | varchar | Len: 50 | Nullable: Yes | Identity: No
54. VET_THUONG_TP | varchar | Len: 50 | Nullable: Yes | Identity: No
55. PP_VO_CAM | varchar | Len: 50 | Nullable: Yes | Identity: No
56. VI_TRI_TH_DVKT | varchar | Len: 50 | Nullable: Yes | Identity: No
57. MA_MAY | varchar | Len: 2000 | Nullable: Yes | Identity: No
58. MA_HIEU_SP | nvarchar | Len: 1000 | Nullable: Yes | Identity: No
59. TAI_SU_DUNG | varchar | Len: 50 | Nullable: Yes | Identity: No
60. DU_PHONG | nvarchar | Len: 4000 | Nullable: Yes | Identity: No
61. lu_updated | datetime | Len


# Table: ss_table_4_nl_view
1. table_4_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. table_1_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
3. include | bit | Len: 1 | Nullable: No | Identity: No
4. patient_visit_id__ | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
5. ngay_vao | datetime | Len: 8 | Nullable: No | Identity: No
6. hn_ | varchar | Len: 20 | Nullable: No | Identity: No
7. ho_ten_ | nvarchar | Len: 512 | Nullable: No | Identity: No
8. invoice_no_ | varchar | Len: 40 | Nullable: No | Identity: No
9. charge_detail_id_ | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
10. item_code_ | nvarchar | Len: 40 | Nullable: No | Identity: No
11. service_category_e_ | nvarchar | Len: 500 | Nullable: Yes | Identity: No
12. lab_orderable_code_ | varchar | Len: 50 | Nullable: Yes | Identity: No
13. lab_orderable_name_ | nvarchar | Len: 500 | Nullable: Yes | Identity: No
14. order_number_ | varchar | Len: 50 | Nullable: Yes | Identity: No
15. lab_process_code_ | varchar | Len: 50 | Nullable: Yes | Identity: No
16. lab_process_name_ | nvarchar | Len: 500 | Nullable: Yes | Identity: No
17. MA_LK | varchar | Len: 250 | Nullable: Yes | Identity: No
18. STT | int | Len: 4 | Nullable: Yes | Identity: No
19. MA_DICH_VU | varchar | Len: 50 | Nullable: Yes | Identity: No
20. MA_CHI_SO | varchar | Len: 50 | Nullable: Yes | Identity: No
21. TEN_CHI_SO | nvarchar | Len: 1000 | Nullable: Yes | Identity: No
22. GIA_TRI | nvarchar | Len: 100 | Nullable: Yes | Identity: No
23. DON_VI_DO | nvarchar | Len: 100 | Nullable: Yes | Identity: No
24. MO_TA | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
25. KET_LUAN | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
26. NGAY_KQ | datetime | Len: 8 | Nullable: Yes | Identity: No
27. MA_BS_DOC_KQ | varchar | Len: 500 | Nullable: Yes | Identity: No
28. DU_PHONG | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
29. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
30. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
31. warning_status | nvarchar | Len: 500 | Nullable: Yes | Identity: No
32. MA_NHOM | varchar | Len: 50 | Nullable: Yes | Identity: No

# Table: ss_table_5_nl_view
1. table_5_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. table_1_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
3. include | bit | Len: 1 | Nullable: No | Identity: No
4. patient_visit_id__ | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
5. visit_code | varchar | Len: 50 | Nullable: Yes | Identity: No
6. ngay_vao | datetime | Len: 8 | Nullable: No | Identity: No
7. invoice_no_ | varchar | Len: 150 | Nullable: Yes | Identity: No
8. hn_ | varchar | Len: 50 | Nullable: Yes | Identity: No
9. ho_ten_ | nvarchar | Len: 500 | Nullable: Yes | Identity: No
10. MA_LK | varchar | Len: 250 | Nullable: Yes | Identity: No
11. STT | int | Len: 4 | Nullable: Yes | Identity: No
12. DIEN_BIEN_LS | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
13. GIAI_DOAN_BENH | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
14. HOI_CHAN | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
15. PHAU_THUAT | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
16. THOI_DIEM_DBLS | datetime | Len: 8 | Nullable: Yes | Identity: No
17. NGUOI_THUC_HIEN | nvarchar | Len: 1000 | Nullable: Yes | Identity: No
18. DU_PHONG | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
19. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
20. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
21. warning_status | nvarchar | Len: 500 | Nullable: Yes | Identity: No


# Table: ss_table_7_nl_view
1. table_7_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. table_1_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
3. include | bit | Len: 1 | Nullable: Yes | Identity: No
4. patient_visit_id__ | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
5. invoice_no_ | varchar | Len: 150 | Nullable: Yes | Identity: No
6. hn_ | varchar | Len: 50 | Nullable: Yes | Identity: No
7. ho_ten_ | nvarchar | Len: 500 | Nullable: Yes | Identity: No
8. visit_type_group_ | varchar | Len: 50 | Nullable: Yes | Identity: No
9. visit_type_ | varchar | Len: 250 | Nullable: Yes | Identity: No
10. MA_LK | varchar | Len: 250 | Nullable: Yes | Identity: No
11. SO_LUU_TRU | varchar | Len: 250 | Nullable: Yes | Identity: No
12. MA_YTE | varchar | Len: 200 | Nullable: Yes | Identity: No
13. MA_KHOA_RV | varchar | Len: 250 | Nullable: Yes | Identity: No
14. NGAY_VAO | datetime | Len: 8 | Nullable: Yes | Identity: No
15. NGAY_RA | datetime | Len: 8 | Nullable: Yes | Identity: No
16. MA_DINH_CHI_THAI | varchar | Len: 50 | Nullable: Yes | Identity: No
17. NGUYENNHAN_DINHCHI | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
18. THOIGIAN_DINHCHI | varchar | Len: 50 | Nullable: Yes | Identity: No
19. TUOI_THAI | int | Len: 4 | Nullable: Yes | Identity: No
20. CHAN_DOAN_RV | nvarchar | Len: 4000 | Nullable: Yes | Identity: No
21. PP_DIEUTRI | nvarchar | Len: 4000 | Nullable: Yes | Identity: No
22. GHI_CHU | nvarchar | Len: 4000 | Nullable: Yes | Identity: No
23. MA_TTDV | varchar | Len: 50 | Nullable: Yes | Identity: No
24. MA_BS | varchar | Len: 250 | Nullable: Yes | Identity: No
25. TEN_BS | nvarchar | Len: 500 | Nullable: Yes | Identity: No
26. NGAY_CT | varchar | Len: 50 | Nullable: Yes | Identity: No
27. MA_CHA | varchar | Len: 50 | Nullable: Yes | Identity: No
28. MA_ME | varchar | Len: 50 | Nullable: Yes | Identity: No
29. MA_THE_TAM | varchar | Len: 50 | Nullable: Yes | Identity: No
30. HO_TEN_CHA | nvarchar | Len: 500 | Nullable: Yes | Identity: No
31. HO_TEN_ME | nvarchar | Len: 500 | Nullable: Yes | Identity: No
32. SO_NGAY_NGHI | varchar | Len: 50 | Nullable: Yes | Identity: No
33. NGOAITRU_TUNGAY | datetime | Len: 8 | Nullable: Yes | Identity: No
34. NGOAITRU_DENNGAY | datetime | Len: 8 | Nullable: Yes | Identity: No
35. DU_PHONG | nvarchar | Len: 8000 | Nullable: Yes | Identity: No
36. lu_updated | datetime | Len: 8 | Nullable: No | Identity: No
37. lu_user_id | uniqueidentifier | Len: 16 | Nullable: Yes | Identity: No
38. warning_status | nvarchar | Len: 500 | Nullable: Yes | Identity: No



# Table: patient_visit_medical_coding_nl_view

1. patient_visit_id | uniqueidentifier | Len: 16 | Nullable: No | Identity: No
2. MA_BENH_CHINH | varchar | Len: 40 | Nullable: Yes | Identity: No
3. TEN_BENH_CHINH | nvarchar | Len: 1000 | Nullable: Yes | Identity: No
4. MA_BENH_KT | varchar | Len: 40 | Nullable: Yes | Identity: No
5. TEN_BENH_KT | nvarchar | Len: 1000 | Nullable: Yes | Identity: No
6. primary_flag | bit | Len: 1 | Nullable: Yes | Identity: No
