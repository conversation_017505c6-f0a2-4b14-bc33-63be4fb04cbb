import { Button, DatePicker, Form, Input, Select } from 'antd'
import React, { useEffect, useState } from 'react'
import { useUI } from '../../common/UIProvider'
import { useWatch } from 'antd/es/form/Form'
import { useDebounceValue } from 'usehooks-ts'
import { FORMAT_DATE } from '../../common/constant'
import { addListItemService, getItemsService, getMetadataService } from '../../common/services'
import lists from '../../common/lists'
import {
  generateDynamicTableColumns,
  generateDynamicTableDataSoruce,
  handleError,
} from '../../common/helpers'
import { getSystemSettingTableColumnListByTableName } from './DM_BHYTService'
import { useCommon } from '../../store/common'
import { findEntityPropertyByName, parseMetadata } from '../../SI/helper'
import DynamicTableComponent from '../../common/components/DynamicTable/DynamicTableComponent'
import ExcelHandler from '../../common/components/ExcelHandler'
const TechnicalServicesPage = () => {
  //hooks
  const [form] = Form.useForm()
  const ui = useUI()
  const { MetadataXml } = useCommon()

  // Filter
  const filterTechServiceCodeOrName = useDebounceValue(
    useWatch('filterTechServiceCodeOrName', form),
    500,
  )[0]
  const filterStartOrEndDate = useWatch('filterStartOrEndDate', form)
  // state
  const [technicalServices, setTechnicalServices] = useState([])
  const [loading, setLoading] = useState(false)
  const [tableMetadataEntity, setTableMetadataEntity] = useState(null)
  const [technicalServicesSystemSettingTableCols, setTechnicalServicesSystemSettingTableCols] =
    useState([])

  // others
  const { RangePicker } = DatePicker
  const handleGetTechnicalServices = async (skip = 0, top = 2000, withLoading = true) => {
    withLoading && setLoading(true)
    try {
      let filter = []
      if (filterTechServiceCodeOrName) {
        filter.push(
          `(contains(MA_TUONG_DUONG, '${filterTechServiceCodeOrName}') or contains(TEN_DVKT_PHEDUYET, '${filterTechServiceCodeOrName}') )`,
        )
      }

      if (filterStartOrEndDate) {
        filter.push(
          `TU_NGAY ge ${filterStartOrEndDate[0].format(
            FORMAT_DATE,
          )} and DEN_NGAY le ${filterStartOrEndDate[1].format(FORMAT_DATE)}`,
        )
      }
      const technical_services = await getItemsService(lists.ss_technical_services_view, {
        filter: filter.join('and '),
        top,
        skip,
      })

      // const mergedData = await Promise.all(
      //   technical_services.value.map(async (techService) => {
      //     const technical_services_detail = await getItemsService(lists.technical_services_detail, {
      //       filter: `(technical_services_id eq ${techService.technical_services_id})`,
      //       top: 1,
      //       orderBy: `lu_updated desc`
      //     })

      //     return {
      //       ...techService,
      //       ...(technical_services_detail.value[0] || {})
      //     }
      //   })
      // )
      setTechnicalServices(technical_services.value)
      return { value: technical_services.value, '@odata.count': technical_services['@odata.count'] }
    } catch (error) {
      console.error(error)
    } finally {
      setLoading(false)
    }
  }
  useEffect(() => {
    handleGetTechnicalServices()
  }, [filterTechServiceCodeOrName, filterStartOrEndDate])

  const handleGetSystemSettingTableColumnList = async () => {
    const response = await getSystemSettingTableColumnListByTableName('technical_services_detail')
    setTechnicalServicesSystemSettingTableCols(response)
    //dispatch(siXmlToolActions.setSystemSettingTableColumnList(response))
  }
  const handleFetchTableMetadataEntity = async () => {
    let metadata_xml = MetadataXml
    if (!metadata_xml) {
      const response = await getMetadataService()
      metadata_xml = response.data
    }

    const parsedMetadata = await parseMetadata(metadata_xml)
    const entityType = findEntityPropertyByName(
      parsedMetadata,
      lists.technical_services_detail.listName,
    )
    setTableMetadataEntity(entityType)
    return entityType
  }
  const handleImport = (importedData) => {}

  const handleConfirmImportExcel = (data) => {
    handleGetTechnicalServices()
    // Implement your save logic here
  }
  const handleOnSaveTechnicalServicesTable = async (mergedParentAndDetailResponse) => {
    // ui.notiSuccess(response)
    try {
      let responseWithoutPrimaryKey
      const { [lists.technical_services_detail.primaryKeyName]: _, ...rest } =
        mergedParentAndDetailResponse
      responseWithoutPrimaryKey = {
        ...rest,
      }

      await addListItemService(lists.technical_services_detail, responseWithoutPrimaryKey)
      handleGetTechnicalServices()
    } catch (error) {
      handleError(error)
    }
    // return null
  }
  useEffect(() => {
    handleGetSystemSettingTableColumnList(), handleFetchTableMetadataEntity()
  }, [])

  return (
    <div className="container-fluid">
      <Form form={form}>
        <div className="row mt-2">
          <h1 className="fw-bold"> Danh sách DVKT BHYT </h1>
        </div>
        <Button onClick={() => {}}> GET FORM VALUE </Button>
        <hr className="row mt-2"></hr>
        <div className="row mt-4 d-flex  ">
          <div className="d-flex  justify-content-md-start gap-5">
            <Form.Item
              // initialValue={lists.ss_table_1.listName}
              style={{ width: 400 }}
              label="Tìm kiếm:"
              name={'filterTechServiceCodeOrName'}>
              <Input allowClear placeholder="Tìm kiếm mã hoặc tên"></Input>
            </Form.Item>
            <Form.Item label="Ngày bắt đầu:" name={'filterStartOrEndDate'}>
              <RangePicker allowClear></RangePicker>
            </Form.Item>
            <Button
              loading={loading}
              onClick={() => {
                handleGetTechnicalServices()
              }}>
              Refresh
            </Button>
          </div>
        </div>
        <hr className="row mt-2"></hr>
        {/* <div className="row mt-4 ">
          <div className="d-flex justify-content-md-end gap-2">
            <AsyncButton>
              Tạo mới
            </AsyncButton>
            <AsyncButton
              style={{
                backgroundColor: COLOR.lime,
                color: 'white',
                borderColor: 'white'
              }}>
              Nhập file
            </AsyncButton>
            <AsyncButton
              style={{
                backgroundColor: COLOR.lightgreen,
                color: COLOR.lime,
                borderColor: 'white'
              }}>
              Xuất file
            </AsyncButton>
          </div>
        </div> */}
      </Form>

      {technicalServices.length > 0 && technicalServicesSystemSettingTableCols.length > 0 && (
        <>
          {technicalServices.length > 0 && (
            <ExcelHandler
              dataToExport={technicalServices}
              dataTableName={lists.technical_services.listName}
              onImport={handleImport}
              onSave={handleConfirmImportExcel}
              metadataEntity={tableMetadataEntity}
            />
          )}
          <DynamicTableComponent
            canNavigateToDetailPage={false}
            isEditableTable={true}
            tableColumns={generateDynamicTableColumns(
              technicalServices,
              technicalServicesSystemSettingTableCols,
            )}
            tableDataSource={generateDynamicTableDataSoruce(technicalServices)}
            tableMetadataEntity={tableMetadataEntity}
            tablePrimaryKeyName={lists.technical_services.primaryKeyName}
            tableName={lists.technical_services.listName}
            onSave={handleOnSaveTechnicalServicesTable}></DynamicTableComponent>

          {/* <DM_BHYTTableComponent
            tableColumns={generateDM_BHYTTableColumns(technicalServicesSystemSettingTableCols)}
            tableDataSource={generateDM_BHYTTableDataSoruce(technicalServices)}
            tablePrimaryKeyName={lists.technical_services.primaryKeyName}
            tableName={lists.technical_services.listName}
            tableMetadataEntity={tableMetadataEntity}
            isEditableTable={true}></DM_BHYTTableComponent> */}
        </>
      )}
    </div>
  )
}

export default TechnicalServicesPage
