import React, { useState } from 'react'
import { Image, Input, Popover, Button } from 'antd'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faSignOutAlt, faUser } from '@fortawesome/free-solid-svg-icons'
import { useNavigate } from 'react-router-dom'
import lists from '../common/lists'
import { logbookActions } from '../store/logbook'
import { handleError } from '../common/helpers'
import { getItemsService } from '../common/services'
import { useDispatch, useSelector } from 'react-redux'
import { useAuth } from '../common/AuthProvider'
import { MODULE_AUTH } from '../store/auth'
import imgLogo2 from '../assets/logoFV-withtext.png'

const Header = () => {
  const [searchValue, setSearchValue] = useState('') // State để lưu giá trị input
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  const content = (
    <div>
      <Button type="text" onClick={() => console.log('Logged out')}>
        <FontAwesomeIcon icon={faSignOutAlt} className="mr-1" /> {/* Thêm icon Logout */}
        Logout
      </Button>
    </div>
  )

  const navigateToElogbookDetail = async (value) => {
    try {
      // Kiểm tra xem giá trị có phải là số nguyên hay không
      let filter
      // Kiểm tra xem giá trị chỉ chứa các ký tự số
      if (/^\d+$/.test(value)) {
        // Nếu chỉ chứa số, tìm kiếm theo record_id

        filter = `(record_code eq ${value})`
      } else {
        // Nếu có ký tự không phải số, tìm kiếm theo record_code
        filter = `(record_id eq ${value})`
      }

      let data = await getItemsService(lists.app_record, {
        filter: filter,
      })

      let appRecord = data.value[0]
      if (appRecord) {
        dispatch(logbookActions.setPatientId(appRecord?.patient_id))
        dispatch(logbookActions.setPatientVisitId(appRecord?.patient_visit_id))

        // Navigate to a new page with the record's ID
        navigate(`/elogbook-detail/${appRecord.record_id}?form_mode=edit`)
      } else {
        //  console.log('No record found')
      }
    } catch (error) {
      handleError(error)
    }
  }

  return (
    <div className="row bg-linear-to-r from-[#F8F6DC] to-[#BAD5D5] px-1 ">
      <div className="col flex items-end">
        <Image height={60} preview={false} src={imgLogo2} alt="Logo" />
      </div>

      <div className="col flex justify-end gap-2 items-center">
        <Input.Search
          style={{ width: '60%' }}
          value={searchValue}
          placeholder="Search Record Logbook No"
          onChange={(e) => setSearchValue(e.target.value)} // Cập nhật giá trị khi nhập
          onSearch={navigateToElogbookDetail} // Gọi hàm khi nhấn Enter hoặc icon kính lúp
        />

        <div className="flex items-center">
          <div className="mr-2">{currentUser.Employee_name}</div>
          <span className="text-lg cursor-pointer" onClick={() => console.log('User Icon Clicked')}>
            <Popover content={content} trigger="click">
              <div className="flex items-center justify-center w-10 h-10 rounded-full border border-gray-300 cursor-pointer hover:bg-gray-200">
                <FontAwesomeIcon icon={faUser} className="text-lg" />
              </div>
            </Popover>
          </span>
        </div>
      </div>
    </div>
  )
}

export default Header
