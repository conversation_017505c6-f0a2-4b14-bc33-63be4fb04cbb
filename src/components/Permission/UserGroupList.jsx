import React, { useState } from 'react'
import { Button, Table, Tag, Modal, Popconfirm, Input, Space } from 'antd'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  getUserGroups,
  deleteUserGroup,
  getUserGroupMembershipsByGroupId,
} from '../../services/permissionService'
import { handleError } from '../../common/helpers'
import { useUI } from '../../common/UIProvider'
import UserGroupForm from './UserGroupForm'
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  TeamOutlined,
} from '@ant-design/icons'

const UserGroupList = () => {
  const ui = useUI()
  const queryClient = useQueryClient()
  const [searchText, setSearchText] = useState('')
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [currentGroup, setCurrentGroup] = useState(null)
  const [modalMode, setModalMode] = useState('create') // 'create' or 'edit'
  const [showMembersModal, setShowMembersModal] = useState(false)
  const [selectedGroupForMembers, setSelectedGroupForMembers] = useState(null)

  // Query to fetch user groups
  const { data: userGroupsData, isLoading } = useQuery({
    queryKey: ['userGroups'],
    queryFn: () => getUserGroups(),
  })

  // Query to fetch group members when needed
  const { data: groupMembersData, isLoading: isLoadingMembers } = useQuery({
    queryKey: ['groupMembers', selectedGroupForMembers],
    queryFn: () => getUserGroupMembershipsByGroupId(selectedGroupForMembers),
    enabled: !!selectedGroupForMembers,
  })

  // Mutation to delete a user group
  const deleteGroupMutation = useMutation({
    mutationFn: (userGroupId) => deleteUserGroup(userGroupId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userGroups'] })
      ui.notiSuccess('User group deleted successfully')
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to delete user group')
    },
  })

  // Filter user groups based on search text
  const filteredGroups =
    userGroupsData?.value?.filter((group) => {
      if (!searchText) return true
      return (
        group.name_l?.toLowerCase().includes(searchText.toLowerCase()) ||
        group.name_e?.toLowerCase().includes(searchText.toLowerCase())
      )
    }) || []

  // Handle creating or editing a user group
  const handleAddEdit = (group) => {
    setCurrentGroup(group)
    setModalMode(group ? 'edit' : 'create')
    setIsModalVisible(true)
  }

  // Handle deleting a user group
  const handleDelete = (userGroupId) => {
    deleteGroupMutation.mutate(userGroupId)
  }

  // Handle viewing group members
  const handleViewMembers = (group) => {
    setSelectedGroupForMembers(group.user_group_id)
    setShowMembersModal(true)
  }

  // Table columns
  const columns = [
    {
      title: 'ID',
      dataIndex: 'user_group_id',
      key: 'user_group_id',
      width: 300,
      ellipsis: true,
    },
    {
      title: 'Name (English)',
      dataIndex: 'name_e',
      key: 'name_e',
      sorter: (a, b) => a.name_e.localeCompare(b.name_e),
    },
    {
      title: 'Name (Local)',
      dataIndex: 'name_l',
      key: 'name_l',
      sorter: (a, b) => a.name_l.localeCompare(b.name_l),
    },
    {
      title: 'Status',
      dataIndex: 'active_flag',
      key: 'active_flag',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>{active ? 'Active' : 'Inactive'}</Tag>
      ),
      filters: [
        { text: 'Active', value: true },
        { text: 'Inactive', value: false },
      ],
      onFilter: (value, record) => record.active_flag === value,
    },
    {
      title: 'Last Updated',
      dataIndex: 'lu_updated',
      key: 'lu_updated',
      render: (date) => new Date(date).toLocaleString(),
      sorter: (a, b) => new Date(a.lu_updated) - new Date(b.lu_updated),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            icon={<TeamOutlined />}
            onClick={() => handleViewMembers(record)}
            title="View Members"
          />
          <Button icon={<EditOutlined />} onClick={() => handleAddEdit(record)} title="Edit" />
          <Popconfirm
            title="Are you sure you want to delete this user group?"
            onConfirm={() => handleDelete(record.user_group_id)}
            okText="Yes"
            cancelText="No">
            <Button danger icon={<DeleteOutlined />} title="Delete" />
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <Input
          placeholder="Search user groups"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300 }}
          prefix={<SearchOutlined />}
          allowClear
        />
        <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddEdit(null)}>
          Add User Group
        </Button>
      </div>

      <Table
        size="small"
        columns={columns}
        dataSource={filteredGroups}
        rowKey="user_group_id"
        loading={isLoading}
        pagination={false}
      />

      {/* Modal for creating/editing user groups */}
      <UserGroupForm
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        group={currentGroup}
        mode={modalMode}
        onSuccess={() => {
          setIsModalVisible(false)
          queryClient.invalidateQueries({ queryKey: ['userGroups'] })
        }}
      />

      {/* Modal for viewing group members */}
      <Modal
        title="Group Members"
        open={showMembersModal}
        onCancel={() => {
          setShowMembersModal(false)
          setSelectedGroupForMembers(null)
        }}
        footer={null}
        width={800}>
        {isLoadingMembers ? (
          <div>Loading members...</div>
        ) : (
          <Table
            size="small"
            columns={[
              {
                title: 'User ID',
                dataIndex: 'user_id',
                key: 'user_id',
              },
              {
                title: 'Status',
                dataIndex: 'active_flag',
                key: 'active_flag',
                render: (active) => (
                  <Tag color={active ? 'green' : 'red'}>{active ? 'Active' : 'Inactive'}</Tag>
                ),
              },
              {
                title: 'Last Updated',
                dataIndex: 'lu_updated',
                key: 'lu_updated',
                render: (date) => new Date(date).toLocaleString(),
              },
            ]}
            dataSource={groupMembersData?.value || []}
            rowKey="user_group_membership_id"
            pagination={false}
          />
        )}
      </Modal>
    </div>
  )
}

export default UserGroupList
