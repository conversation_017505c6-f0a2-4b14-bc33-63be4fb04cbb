import React, { useEffect } from 'react'
import { Modal, Form, Input, Switch, Button } from 'antd'
import { useMutation } from '@tanstack/react-query'
import { createPermission, updatePermission } from '../../services/permissionService'
import { handleError } from '../../common/helpers'
import { useUI } from '../../common/UIProvider'
import PropTypes from 'prop-types'

const PermissionForm = ({ visible, onCancel, permission, mode, onSuccess }) => {
  const [form] = Form.useForm()
  const ui = useUI()

  // Reset form when modal opens or permission changes
  useEffect(() => {
    if (visible) {
      form.resetFields()
      if (permission && mode === 'edit') {
        form.setFieldsValue({
          permission_rcd: permission.permission_rcd,
          name: permission.name,
          active_flag: permission.active_flag,
        })
      }
    }
  }, [visible, permission, form, mode])

  // Mutation for creating a permission
  const createMutation = useMutation({
    mutationFn: (values) => createPermission(values),
    onSuccess: () => {
      ui.notiSuccess('Permission created successfully')
      onSuccess()
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to create permission')
    },
  })

  // Mutation for updating a permission
  const updateMutation = useMutation({
    mutationFn: ({ code, values }) => updatePermission(code, values),
    onSuccess: () => {
      ui.notiSuccess('Permission updated successfully')
      onSuccess()
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to update permission')
    },
  })

  // Handle form submission
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()

      if (mode === 'create') {
        createMutation.mutate(values)
      } else {
        // For edit mode, we don't allow changing the permission code
        const { permission_rcd, ...updateValues } = values
        updateMutation.mutate({ code: permission.permission_rcd, values: updateValues })
      }
    } catch (error) {
      console.error('Validation failed:', error)
    }
  }

  return (
    <Modal
      title={mode === 'create' ? 'Create Permission' : 'Edit Permission'}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={createMutation.isPending || updateMutation.isPending}
          onClick={handleSubmit}>
          {mode === 'create' ? 'Create' : 'Update'}
        </Button>,
      ]}>
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          active_flag: true,
        }}>
        <Form.Item
          name="permission_rcd"
          label="Permission Code"
          rules={[{ required: true, message: 'Please enter the permission code' }]}
          disabled={mode === 'edit'}>
          <Input disabled={mode === 'edit'} />
        </Form.Item>

        <Form.Item
          name="name"
          label="Name"
          rules={[{ required: true, message: 'Please enter the permission name' }]}>
          <Input />
        </Form.Item>

        <Form.Item name="active_flag" label="Active" valuePropName="checked">
          <Switch />
        </Form.Item>
      </Form>
    </Modal>
  )
}

PermissionForm.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  permission: PropTypes.object,
  mode: PropTypes.oneOf(['create', 'edit']).isRequired,
  onSuccess: PropTypes.func.isRequired,
}

export default PermissionForm
