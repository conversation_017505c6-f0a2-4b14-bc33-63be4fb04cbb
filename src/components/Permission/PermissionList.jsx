import React, { useState, useEffect } from 'react'
import { Button, Table, Tag, Modal, Popconfirm, Input, Space } from 'antd'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  getPermissions,
  deletePermission,
  getGroupPermissionMappingsByPermissionCode,
  createPermission,
} from '../../services/permissionService'
import { handleError } from '../../common/helpers'
import { useUI } from '../../common/UIProvider'
import PermissionForm from './PermissionForm'
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  TeamOutlined,
} from '@ant-design/icons'
import { PERMISSION } from '../../components/Auth/AuthConstant'

const PermissionList = () => {
  const ui = useUI()
  const queryClient = useQueryClient()
  const [searchText, setSearchText] = useState('')
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [currentPermission, setCurrentPermission] = useState(null)
  const [modalMode, setModalMode] = useState('create') // 'create' or 'edit'
  const [showGroupsModal, setShowGroupsModal] = useState(false)
  const [selectedPermissionForGroups, setSelectedPermissionForGroups] = useState(null)
  const [isCreatingMissingPermissions, setIsCreatingMissingPermissions] = useState(false)

  // Query to fetch permissions
  const { data: permissionsData, isLoading } = useQuery({
    queryKey: ['permissions'],
    queryFn: () => getPermissions(),
  })

  // Mutation to create a permission
  const createPermissionMutation = useMutation({
    mutationFn: (permissionData) => createPermission(permissionData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] })
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to create permission')
    },
  })

  const checkAndCreateMissingPermissions = async () => {
    const existingPermissions = permissionsData.value.map((p) => p.permission_rcd)
    const missingPermissions = []

    // Check for missing permissions
    Object.values(PERMISSION).forEach((permissionCode) => {
      if (!existingPermissions.includes(permissionCode)) {
        missingPermissions.push({
          permission_rcd: permissionCode,
          name: permissionCode,
          active_flag: true,
          lu_updated: new Date().toISOString(),
        })
      }
    })

    // Create missing permissions
    if (missingPermissions.length > 0) {
      try {
        setIsCreatingMissingPermissions(true)
        for (const permission of missingPermissions) {
          await createPermissionMutation.mutateAsync(permission)
        }
        ui.notiSuccess(`Created ${missingPermissions.length} missing permission(s)`)
      } catch (error) {
        handleError(error)
        ui.notiError('Failed to create missing permissions')
        // Even if there's an error, we don't want to retry
      } finally {
        setIsCreatingMissingPermissions(false)
      }
    } else {
      ui.notiSuccess('All permissions are present')
    }
  }

  // Query to fetch groups with this permission when needed
  const { data: permissionGroupsData, isLoading: isLoadingGroups } = useQuery({
    queryKey: ['permissionGroups', selectedPermissionForGroups],
    queryFn: () => getGroupPermissionMappingsByPermissionCode(selectedPermissionForGroups),
    enabled: !!selectedPermissionForGroups,
  })

  // Mutation to delete a permission
  const deletePermissionMutation = useMutation({
    mutationFn: (permissionRcd) => deletePermission(permissionRcd),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] })
      ui.notiSuccess('Permission deleted successfully')
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to delete permission')
    },
  })

  // Filter permissions based on search text
  const filteredPermissions =
    permissionsData?.value?.filter((permission) => {
      if (!searchText) return true
      return (
        permission.permission_rcd?.toLowerCase().includes(searchText.toLowerCase()) ||
        permission.name?.toLowerCase().includes(searchText.toLowerCase())
      )
    }) || []

  // Handle creating or editing a permission
  const handleAddEdit = (permission) => {
    setCurrentPermission(permission)
    setModalMode(permission ? 'edit' : 'create')
    setIsModalVisible(true)
  }

  // Handle deleting a permission
  const handleDelete = (permissionRcd) => {
    deletePermissionMutation.mutate(permissionRcd)
  }

  // Handle viewing groups with this permission
  const handleViewGroups = (permission) => {
    setSelectedPermissionForGroups(permission.permission_rcd)
    setShowGroupsModal(true)
  }

  // Table columns
  const columns = [
    {
      title: 'Permission Code',
      dataIndex: 'permission_rcd',
      key: 'permission_rcd',
      sorter: (a, b) => a.permission_rcd.localeCompare(b.permission_rcd),
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Status',
      dataIndex: 'active_flag',
      key: 'active_flag',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>{active ? 'Active' : 'Inactive'}</Tag>
      ),
      filters: [
        { text: 'Active', value: true },
        { text: 'Inactive', value: false },
      ],
      onFilter: (value, record) => record.active_flag === value,
    },
    {
      title: 'Last Updated',
      dataIndex: 'lu_updated',
      key: 'lu_updated',
      render: (date) => new Date(date).toLocaleString(),
      sorter: (a, b) => new Date(a.lu_updated) - new Date(b.lu_updated),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            icon={<TeamOutlined />}
            onClick={() => handleViewGroups(record)}
            title="View Groups"
          />
          <Button icon={<EditOutlined />} onClick={() => handleAddEdit(record)} title="Edit" />
          <Popconfirm
            title="Are you sure you want to delete this permission?"
            onConfirm={() => handleDelete(record.permission_rcd)}
            okText="Yes"
            cancelText="No">
            <Button danger icon={<DeleteOutlined />} title="Delete" />
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <Input
            placeholder="Search permissions"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 300 }}
            prefix={<SearchOutlined />}
            allowClear
          />
          {isCreatingMissingPermissions && (
            <span style={{ marginLeft: 16, color: '#1890ff' }}>
              Creating missing permissions...
            </span>
          )}
        </div>
        <div className="d-flex gap-2">
          <Button onClick={checkAndCreateMissingPermissions}>Check Missing Permissions</Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddEdit(null)}>
            Add Permission
          </Button>
        </div>
      </div>

      <Table
        size="small"
        columns={columns}
        dataSource={filteredPermissions}
        rowKey="permission_rcd"
        loading={isLoading || isCreatingMissingPermissions}
        pagination={false}
      />

      {/* Modal for creating/editing permissions */}
      <PermissionForm
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        permission={currentPermission}
        mode={modalMode}
        onSuccess={() => {
          setIsModalVisible(false)
          queryClient.invalidateQueries({ queryKey: ['permissions'] })
        }}
      />

      {/* Modal for viewing groups with this permission */}
      <Modal
        title="Groups with this Permission"
        open={showGroupsModal}
        onCancel={() => {
          setShowGroupsModal(false)
          setSelectedPermissionForGroups(null)
        }}
        footer={null}
        width={800}>
        {isLoadingGroups ? (
          <div>Loading groups...</div>
        ) : (
          <Table
            size="small"
            columns={[
              {
                title: 'Group ID',
                dataIndex: 'user_group_id',
                key: 'user_group_id',
              },
              {
                title: 'Status',
                dataIndex: 'active_flag',
                key: 'active_flag',
                render: (active) => (
                  <Tag color={active ? 'green' : 'red'}>{active ? 'Active' : 'Inactive'}</Tag>
                ),
              },
              {
                title: 'Last Updated',
                dataIndex: 'lu_updated',
                key: 'lu_updated',
                render: (date) => new Date(date).toLocaleString(),
              },
            ]}
            dataSource={permissionGroupsData?.value || []}
            rowKey="user_group_permission_mapping_id"
            pagination={false}
          />
        )}
      </Modal>
    </div>
  )
}

export default PermissionList
