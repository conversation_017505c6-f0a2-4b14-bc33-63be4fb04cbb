import React, { useState } from 'react'
import {
  Card,
  <PERSON>,
  Button,
  Select,
  Tag,
  Popconfirm,
  Row,
  Col,
  Typography,
  Empty,
  App,
} from 'antd'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  getUserGroups,
  getPermissions,
  getGroupPermissionMappingsByGroupId,
  assignPermissionToGroup,
  removePermissionFromGroup,
} from '../../services/permissionService'
import { handleError } from '../../common/helpers'
import { useUI } from '../../common/UIProvider'
import { DeleteOutlined, PlusOutlined, AppstoreAddOutlined } from '@ant-design/icons'

const { Title } = Typography
const { Option } = Select

const GroupPermissionMapping = () => {
  const ui = useUI()
  const queryClient = useQueryClient()
  const [selectedGroup, setSelectedGroup] = useState(null)
  const [selectedPermission, setSelectedPermission] = useState(null)
  const [isAssigningAll, setIsAssigningAll] = useState(false)
  const { modal } = App.useApp()

  // Query to fetch user groups
  const { data: userGroupsData, isLoading: isLoadingGroups } = useQuery({
    queryKey: ['userGroups'],
    queryFn: () => getUserGroups(),
  })

  // Query to fetch permissions
  const { data: permissionsData, isLoading: isLoadingPermissions } = useQuery({
    queryKey: ['permissions'],
    queryFn: () => getPermissions(),
  })

  // Query to fetch group permissions when a group is selected
  const { data: groupPermissionsData, isLoading: isLoadingGroupPermissions } = useQuery({
    queryKey: ['groupPermissions', selectedGroup],
    queryFn: () => getGroupPermissionMappingsByGroupId(selectedGroup),
    enabled: !!selectedGroup,
  })

  // Mutation to assign a permission to a group
  const assignPermissionMutation = useMutation({
    mutationFn: ({ permissionRcd, groupId }) => assignPermissionToGroup(permissionRcd, groupId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groupPermissions', selectedGroup] })
      ui.notiSuccess('Permission assigned to group successfully')
      setSelectedPermission(null)
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to assign permission to group')
    },
  })

  // Mutation to remove a permission from a group
  const removePermissionMutation = useMutation({
    mutationFn: (mappingId) => removePermissionFromGroup(mappingId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groupPermissions', selectedGroup] })
      ui.notiSuccess('Permission removed from group successfully')
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to remove permission from group')
    },
  })

  // Handle assigning a permission to the selected group
  const handleAssignPermission = () => {
    if (!selectedPermission || !selectedGroup) {
      ui.notiWarning('Please select both a permission and a group')
      return
    }

    // Check if permission is already assigned to the group
    const isPermissionAssigned = groupPermissionsData?.value?.some(
      (mapping) => mapping.permission_rcd === selectedPermission && mapping.active_flag,
    )

    if (isPermissionAssigned) {
      ui.notiWarning('Permission is already assigned to this group')
      return
    }

    assignPermissionMutation.mutate({ permissionRcd: selectedPermission, groupId: selectedGroup })
  }

  // Handle assigning all permissions to the selected group
  const handleAssignAllPermissions = () => {
    if (!selectedGroup) {
      ui.notiWarning('Please select a group')
      return
    }

    // Get all available permissions that aren't already assigned to the group
    const assignedPermissionCodes =
      groupPermissionsData?.value
        ?.filter((mapping) => mapping.active_flag)
        ?.map((mapping) => mapping.permission_rcd) || []

    const availablePermissions =
      permissionsData?.value?.filter(
        (permission) =>
          permission.active_flag && !assignedPermissionCodes.includes(permission.permission_rcd),
      ) || []

    if (availablePermissions.length === 0) {
      ui.notiWarning('All permissions are already assigned to this group')
      return
    }

    // Confirm before assigning all permissions using Ant Design Modal
    modal.confirm({
      title: 'Assign All Permissions',
      content: `Are you sure you want to assign all ${availablePermissions.length} available permissions to this group?`,
      okText: 'Yes',
      cancelText: 'No',
      onOk: async () => {
        setIsAssigningAll(true)

        try {
          // Use Promise.all to assign all permissions in parallel
          await Promise.all(
            availablePermissions.map((permission) =>
              assignPermissionToGroup(permission.permission_rcd, selectedGroup),
            ),
          )

          queryClient.invalidateQueries({ queryKey: ['groupPermissions', selectedGroup] })
          ui.notiSuccess(
            `${availablePermissions.length} permissions assigned to group successfully`,
          )
        } catch (error) {
          handleError(error)
          ui.notiError('Failed to assign all permissions to group')
        } finally {
          setIsAssigningAll(false)
        }
      },
    })
  }

  // Handle removing a permission from the group
  const handleRemovePermission = (mappingId) => {
    removePermissionMutation.mutate(mappingId)
  }

  // Get permission name by code
  const getPermissionNameByCode = (permissionRcd) => {
    const permission = permissionsData?.value?.find((p) => p.permission_rcd === permissionRcd)
    return permission ? permission.name : permissionRcd
  }

  // Table columns for group permissions
  const columns = [
    {
      title: 'Permission Code',
      dataIndex: 'permission_rcd',
      key: 'permission_rcd',
    },
    {
      title: 'Permission Name',
      key: 'permission_name',
      render: (_, record) => getPermissionNameByCode(record.permission_rcd),
    },
    {
      title: 'Status',
      dataIndex: 'active_flag',
      key: 'active_flag',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>{active ? 'Active' : 'Inactive'}</Tag>
      ),
    },
    {
      title: 'Last Updated',
      dataIndex: 'lu_updated',
      key: 'lu_updated',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Popconfirm
          title="Are you sure you want to remove this permission from the group?"
          onConfirm={() => handleRemovePermission(record.user_group_permission_mapping_id)}
          okText="Yes"
          cancelText="No">
          <Button danger icon={<DeleteOutlined />} disabled={!record.active_flag} />
        </Popconfirm>
      ),
    },
  ]

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="Group Permission Mapping">
            <Row gutter={[16, 16]}>
              <Col span={10}>
                <Title level={5}>Select Group</Title>
                <Select
                  placeholder="Select a user group"
                  style={{ width: '100%' }}
                  loading={isLoadingGroups}
                  value={selectedGroup}
                  onChange={setSelectedGroup}>
                  {userGroupsData?.value
                    ?.filter((g) => g.active_flag)
                    .map((group) => (
                      <Option key={group.user_group_id} value={group.user_group_id}>
                        {group.name_e} ({group.name_l})
                      </Option>
                    ))}
                </Select>
              </Col>
              <Col span={10}>
                <Title level={5}>Select Permission</Title>
                <Select
                  placeholder="Select a permission to assign"
                  style={{ width: '100%' }}
                  loading={isLoadingPermissions}
                  value={selectedPermission}
                  onChange={setSelectedPermission}
                  disabled={!selectedGroup}
                  showSearch
                  optionFilterProp="children">
                  {permissionsData?.value
                    ?.filter(
                      (user) =>
                        !groupPermissionsData?.value?.some(
                          (member) => member.permission_rcd === user.permission_rcd,
                        ),
                    )
                    ?.filter((p) => p.active_flag)
                    .map((permission) => (
                      <Option key={permission.permission_rcd} value={permission.permission_rcd}>
                        {permission.name} ({permission.permission_rcd})
                      </Option>
                    ))}
                </Select>
              </Col>
              <Col span={4} style={{ display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAssignPermission}
                  disabled={!selectedPermission || !selectedGroup}
                  loading={assignPermissionMutation.isPending}>
                  Assign
                </Button>
                <Button
                  type="primary"
                  icon={<AppstoreAddOutlined />}
                  onClick={handleAssignAllPermissions}
                  disabled={!selectedGroup}
                  loading={isAssigningAll}>
                  Assign All
                </Button>
              </Col>
            </Row>
          </Card>
        </Col>
        <Col span={24}>
          <Card title="Group Permissions">
            {!selectedGroup ? (
              <Empty description="Please select a group to view its permissions" />
            ) : (
              <Table
                size="small"
                columns={columns}
                dataSource={groupPermissionsData?.value || []}
                rowKey="user_group_permission_mapping_id"
                loading={isLoadingGroupPermissions}
                pagination={false}
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default GroupPermissionMapping
