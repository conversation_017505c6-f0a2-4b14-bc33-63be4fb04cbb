import React, { useState } from 'react'
import {
  Card,
  Table,
  Button,
  Select,
  Tag,
  Popconfirm,
  Row,
  Col,
  Typography,
  Empty,
  Modal,
  Input,
} from 'antd'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  getUserGroups,
  getUserGroupMembershipsByGroupId,
  addUserToGroup,
  removeUserFromGroup,
} from '../../services/permissionService'
import { getItemsService } from '../../common/services'
import lists from '../../common/lists'
import { batchGetItemsByColumnName, handleError } from '../../common/helpers'
import { useUI } from '../../common/UIProvider'
import { DeleteOutlined, UserAddOutlined, UsergroupAddOutlined } from '@ant-design/icons'
import PromiseContent from '../../common/components/PromiseContent'
import _ from 'lodash'

const { Title } = Typography
const { Option } = Select

const UserGroupMembership = () => {
  const ui = useUI()
  const queryClient = useQueryClient()
  const [selectedGroup, setSelectedGroup] = useState(null)
  const [selectedUser, setSelectedUser] = useState(null)
  const [searchUser, setSearchUser] = useState('')
  const [bulkUsernames, setBulkUsernames] = useState('')
  const [bulkModalVisible, setBulkModalVisible] = useState(false)

  // Query to fetch user groups
  const { data: userGroupsData, isLoading: isLoadingGroups } = useQuery({
    queryKey: ['userGroups'],
    queryFn: () => getUserGroups(),
  })

  // Query to fetch users
  const {
    data: usersData,
    isLoading: isLoadingUsers,
    isFetching: isFetchingUsers,
  } = useQuery({
    queryKey: ['users', searchUser],
    queryFn: async () => {
      const data = await getItemsService(lists.employee_dataset, {
        filter: `contains(user_name, '${searchUser}') or contains(employee_name, '${searchUser}')`,
        top: 1000,
      })
      const distinctData = _.unionBy(data.value, 'user_id').filter((item) => !!item.user_id)

      return distinctData
    },
  })

  // Query to fetch group members when a group is selected
  const { data: groupMembersData, isLoading: isLoadingMembers } = useQuery({
    queryKey: ['groupMembers', selectedGroup],
    queryFn: () => getUserGroupMembershipsByGroupId(selectedGroup),
    enabled: !!selectedGroup,
  })

  // Mutation to add a user to a group
  const addUserMutation = useMutation({
    mutationFn: ({ userId, groupId }) => addUserToGroup(userId, groupId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groupMembers', selectedGroup] })
      ui.notiSuccess('User added to group successfully')
      setSelectedUser(null)
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to add user to group')
    },
  })

  // Mutation to remove a user from a group
  const removeUserMutation = useMutation({
    mutationFn: (membershipId) => removeUserFromGroup(membershipId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groupMembers', selectedGroup] })
      ui.notiSuccess('User removed from group successfully')
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to remove user from group')
    },
  })

  // Add this mutation for bulk adding users
  const bulkAddUsersMutation = useMutation({
    mutationFn: async ({ usernames, groupId }) => {
      const trimmedUsernames = usernames
        .split(/[\n,]/)
        .map((u) => u.trim())
        .filter(Boolean)

      const usersData = await batchGetItemsByColumnName(
        trimmedUsernames,
        'user_name',
        lists.employee_dataset,
      )
      const validUsers = usersData.filter((user) => !!user.user_id)

      await Promise.all(validUsers.map((user) => addUserToGroup(user.user_id, groupId)))

      return {
        successful: validUsers.length,
        total: trimmedUsernames.length,
      }
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['groupMembers', selectedGroup] })
      ui.notiSuccess(
        `Successfully added ${result.successful} out of ${result.total} users to group`,
      )
      setBulkModalVisible(false)
      setBulkUsernames('')
    },
    onError: (error) => {
      handleError(error)
      ui.notiError('Failed to add users to group')
    },
  })

  // Handle adding a user to the selected group
  const handleAddUser = () => {
    if (!selectedUser || !selectedGroup) {
      ui.notiWarning('Please select both a user and a group')
      return
    }

    // Check if user is already in the group
    const isUserInGroup = groupMembersData?.value?.some(
      (member) => member.user_id === selectedUser && member.active_flag,
    )

    if (isUserInGroup) {
      ui.notiWarning('User is already a member of this group')
      return
    }

    addUserMutation.mutate({ userId: selectedUser, groupId: selectedGroup })
  }

  // Handle removing a user from the group
  const handleRemoveUser = (membershipId) => {
    removeUserMutation.mutate(membershipId)
  }

  // Table columns for group members
  const columns = [
    {
      title: 'User ID',
      dataIndex: 'user_id',
      key: 'user_id',
    },
    {
      title: 'User Name',
      key: 'user_name',
      render: (_, record) => {
        return (
          <PromiseContent
            promise={() =>
              getItemsService(lists.employee_dataset, { filter: `user_id eq ${record.user_id}` })
            }
            render={(data) => data?.value?.[0]?.user_name || 'N/A'}
          />
        )
      },
    },
    {
      title: 'Status',
      dataIndex: 'active_flag',
      key: 'active_flag',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>{active ? 'Active' : 'Inactive'}</Tag>
      ),
    },
    {
      title: 'Last Updated',
      dataIndex: 'lu_updated',
      key: 'lu_updated',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Popconfirm
          title="Are you sure you want to remove this user from the group?"
          onConfirm={() => handleRemoveUser(record.user_group_membership_id)}
          okText="Yes"
          cancelText="No">
          <Button danger icon={<DeleteOutlined />} disabled={!record.active_flag} />
        </Popconfirm>
      ),
    },
  ]

  // Add this modal component
  const BulkAddModal = () => (
    <Modal
      title="Bulk Add Users"
      open={bulkModalVisible}
      onOk={() => {
        if (!bulkUsernames.trim()) {
          ui.notiWarning('Please enter usernames')
          return
        }
        bulkAddUsersMutation.mutate({
          usernames: bulkUsernames,
          groupId: selectedGroup,
        })
      }}
      onCancel={() => {
        setBulkModalVisible(false)
        setBulkUsernames('')
      }}
      confirmLoading={bulkAddUsersMutation.isPending}>
      <Input.TextArea
        value={bulkUsernames}
        onChange={(e) => setBulkUsernames(e.target.value)}
        placeholder="Enter usernames separated by commas or newlines"
        rows={6}
      />
    </Modal>
  )

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="User Group Membership">
            <Row gutter={[16, 16]}>
              <Col span={10}>
                <Title level={5}>Select Group</Title>
                <Select
                  placeholder="Select a user group"
                  style={{ width: '100%' }}
                  loading={isLoadingGroups}
                  value={selectedGroup}
                  onChange={setSelectedGroup}>
                  {userGroupsData?.value
                    ?.filter((g) => g.active_flag)
                    .map((group) => (
                      <Option key={group.user_group_id} value={group.user_group_id}>
                        {group.name_e} ({group.name_l})
                      </Option>
                    ))}
                </Select>
              </Col>
              <Col span={10}>
                <Title level={5}>Select User</Title>
                <Select
                  placeholder="Select a user to add"
                  style={{ width: '100%' }}
                  loading={isLoadingUsers || isFetchingUsers}
                  value={selectedUser}
                  onChange={setSelectedUser}
                  disabled={!selectedGroup}
                  showSearch
                  onSearch={setSearchUser}
                  optionFilterProp="children">
                  {usersData
                    ?.filter(
                      (user) =>
                        !groupMembersData?.value?.some((member) => member.user_id === user.user_id),
                    )
                    .map((user) => (
                      <Option key={user.user_id} value={user.user_id}>
                        {user.employee_name} - {user.user_name}
                      </Option>
                    ))}
                </Select>
              </Col>
              <Col span={4} style={{ display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
                <Button
                  type="primary"
                  icon={<UserAddOutlined />}
                  onClick={handleAddUser}
                  disabled={!selectedUser || !selectedGroup}
                  loading={addUserMutation.isPending}>
                  Add User
                </Button>
                <Button
                  type="primary"
                  icon={<UsergroupAddOutlined />}
                  onClick={() => setBulkModalVisible(true)}
                  disabled={!selectedGroup}>
                  Bulk Add
                </Button>
              </Col>
            </Row>
          </Card>
        </Col>
        <Col span={24}>
          <Card title="Group Members">
            {!selectedGroup ? (
              <Empty description="Please select a group to view its members" />
            ) : (
              <Table
                size="small"
                columns={columns}
                dataSource={groupMembersData?.value || []}
                rowKey="user_group_membership_id"
                loading={isLoadingMembers}
                pagination={false}
              />
            )}
          </Card>
        </Col>
      </Row>
      <BulkAddModal />
    </div>
  )
}

export default UserGroupMembership
