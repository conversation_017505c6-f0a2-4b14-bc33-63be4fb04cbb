import React from 'react'
import ReactDOM from 'react-dom/client'
import Root from './common/Root'
import { registerServiceWorker } from './registerSW'

import './styles/bootstrap.min.css'
import './styles/all.min.css'
import 'nprogress/nprogress.css'
import './styles/index.scss'
import './styles/profile.scss'
import './styles/login-page.scss'
import './styles/spinner.scss'
import './styles/loading-page.scss'
import './styles/change-password-page.scss'
import './styles/forgot-password-page.scss'
import './styles/nprogress.scss'
import './styles/home.scss'
import './index.css'
import './styles/scroll-bar.scss'
import './styles/medical-record.scss'
import './styles/pdf-viewer.scss'

// Register the service worker for PWA support
registerServiceWorker()

ReactDOM.createRoot(document.getElementById('root'), { stricMode: false }).render(<Root />)
