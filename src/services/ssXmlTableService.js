import { getItemsService } from '../common/services'
import lists from '../common/lists'
import { handleError } from '../common/helpers'

/**
 * <PERSON><PERSON><PERSON> dữ liệu từ bảng ss_table_1_nl_view theo patient_visit_id
 * @param {string} patientVisitId - ID của lượt khám
 * @returns {Object} Một object chứa thông tin của ss_table_1_nl_view
 */
export const getSsXmlTable1NlViewByPatientVisitId = async (patientVisitId) => {
  try {
    let filter = `patient_visit_id__ eq ${patientVisitId} and include eq true`
    let data = await getItemsService(lists.ss_table_1_nl_view, {
      filter: filter,
      top: 1,
    })

    return data.value.length > 0 ? data : []
  } catch (error) {
    handleError(error)
    return []
  }
}

/**
 * <PERSON><PERSON><PERSON> danh sách dữ liệu từ bảng ss_table_2_nl_view theo table_1_id
 * @param {string} table1Id - ID của bảng 1
 * @returns {Array} Mảng các object chứa thông tin của ss_table_2_nl_view
 */
export const getSsXmlTable2NlViewsByTable1Id = async (table1Id) => {
  try {
    let filter = `table_1_id eq ${table1Id} and include eq true`
    let data = await getItemsService(lists.ss_table_2_nl_view, {
      filter: filter,
      top: 2000,
    })

    return data || []
  } catch (error) {
    handleError(error)
    return []
  }
}

/**
 * Lấy danh sách dữ liệu từ bảng ss_table_3_nl_view theo table_1_id
 * @param {string} table1Id - ID của bảng 1
 * @returns {Array} Mảng các object chứa thông tin của ss_table_3_nl_view
 */
export const getSsXmlTable3NlViewsByTable1Id = async (table1Id) => {
  try {
    let filter = `table_1_id eq ${table1Id} and include eq true`
    let data = await getItemsService(lists.ss_table_3_nl_view, {
      filter: filter,
      top: 2000,
    })

    return data || []
  } catch (error) {
    handleError(error)
    return []
  }
}

/**
 * Lấy danh sách dữ liệu từ bảng ss_table_4_nl_view theo table_1_id
 * @param {string} table1Id - ID của bảng 1
 * @returns {Array} Mảng các object chứa thông tin của ss_table_4_nl_view
 */
export const getSsXmlTable4NlViewsByTable1Id = async (table1Id) => {
  try {
    let filter = `table_1_id eq ${table1Id} and include eq true`
    let data = await getItemsService(lists.ss_table_4_nl_view, {
      filter: filter,
      top: 2000,
    })

    return data || []
  } catch (error) {
    handleError(error)
    return []
  }
}

/**
 * Lấy danh sách dữ liệu từ bảng ss_table_5_nl_view theo table_1_id
 * @param {string} table1Id - ID của bảng 1
 * @returns {Array} Mảng các object chứa thông tin của ss_table_5_nl_view
 */
export const getSsXmlTable5NlViewsByTable1Id = async (table1Id) => {
  try {
    let filter = `table_1_id eq ${table1Id} and include eq true`
    let data = await getItemsService(lists.ss_table_5_nl_view, {
      filter: filter,
      top: 2000,
    })

    return data || []
  } catch (error) {
    handleError(error)
    return []
  }
}

/**
 * Lấy danh sách dữ liệu từ bảng ss_table_7_nl_view theo table_1_id
 * @param {string} table1Id - ID của bảng 1
 * @returns {Array} Mảng các object chứa thông tin của ss_table_7_nl_view
 */
export const getSsXmlTable7NlViewsByTable1Id = async (table1Id) => {
  try {
    let filter = `table_1_id eq ${table1Id} and include eq true`
    let data = await getItemsService(lists.ss_table_7_nl_view, {
      filter: filter,
      top: 2000,
    })

    return data || []
  } catch (error) {
    handleError(error)
    return []
  }
}

/**
 * Hàm tiện ích để lấy tất cả các bảng XML liên quan đến một table_1_id
 * @param {string} table1Id - ID của bảng 1
 * @returns {Object} Đối tượng chứa tất cả các bảng XML
 */
export const getAllSsXmlTablesByTable1Id = async (table1Id) => {
  try {
    if (!table1Id) {
      return
      //throw new Error('table_1_id là bắt buộc')
    }

    // Lấy dữ liệu từ tất cả các bảng
    const [table2Data, table3Data, table4Data, table5Data, table7Data] = await Promise.all([
      getSsXmlTable2NlViewsByTable1Id(table1Id),
      getSsXmlTable3NlViewsByTable1Id(table1Id),
      getSsXmlTable4NlViewsByTable1Id(table1Id),
      getSsXmlTable5NlViewsByTable1Id(table1Id),
      getSsXmlTable7NlViewsByTable1Id(table1Id),
    ])

    return {
      ssTable2: table2Data?.value,
      ssTable3: table3Data?.value,
      ssTable4: table4Data?.value,
      ssTable5: table5Data?.value,
      ssTable7: table7Data?.value,
    }
  } catch (error) {
    handleError(error)
    return {
      ssTable2: [],
      ssTable3: [],
      ssTable4: [],
      ssTable5: [],
      ssTable7: [],
    }
  }
}

/**
 * Hàm tiện ích để lấy tất cả các bảng XML liên quan đến một patient_visit_id
 * @param {string} patientVisitId - ID lượt khám
 * @returns {Object} Đối tượng chứa tất cả các bảng XML
 */
export const getAllSsXmlTablesByPatientVisitId = async (patientVisitId) => {
  try {
    if (!patientVisitId) {
      throw new Error('patient_visit_id là bắt buộc')
    }

    // Lấy bảng 1 trước
    const data = await getSsXmlTable1NlViewByPatientVisitId(patientVisitId)
    // Check if data has a value property and it's an array with elements
    const ssTable1 =
      data?.value && Array.isArray(data.value) && data.value.length > 0 ? data.value[0] : null
    if (!ssTable1?.table_1_id) {
      return {
        ssTable1: {},
        ssTable2: [],
        ssTable3: [],
        ssTable4: [],
        ssTable5: [],
        ssTable7: [],
      }
    }

    // Lấy các bảng khác
    const otherTables = await getAllSsXmlTablesByTable1Id(ssTable1?.table_1_id)

    return {
      ssTable1: ssTable1,
      ...otherTables,
    }
  } catch (error) {
    handleError(error)
    return {
      ssTable1: {},
      ssTable2: [],
      ssTable3: [],
      ssTable4: [],
      ssTable5: [],
      ssTable7: [],
    }
  }
}
