import { deleteListItemService, getItemsService, updateListItemService } from '../common/services'
import lists from '../common/lists'
import { calculateTotalForColumn, handleError } from '../common/helpers'
import dayjs from '../common/dayjs'
import { v4 as uuidv4 } from 'uuid'
import { rule } from 'postcss'
import { RULE_WARNING_STATUS } from '../components/Visit/RuleManagement/RuleManagementConstant'
import { XML_LIST } from '../SI/constant'

// #region services

export const getAllRuleRefs = async () => {
  try {
    let filter = `active_flag eq true`
    let data = await getItemsService(lists.rule_ref, {
      filter: filter,
      top: 2000,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getRuleVisitItemDetailsByPatientVisitIds = async (
  mainPatientVisitId,
  selectedPatientVisitId,
) => {
  try {
    let filter = `patient_visit_id eq ${mainPatientVisitId} or patient_visit_id eq ${selectedPatientVisitId}`
    let data = await getItemsService(lists.rule_visit_item_detail, {
      filter: filter,
      top: 2000,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getRuleVisitItemDetailNlViewsByPatientVisitIds = async (
  mainPatientVisitId,
  selectedPatientVisitId,
) => {
  try {
    let filter = `patient_visit_id eq ${mainPatientVisitId} or patient_visit_id eq ${selectedPatientVisitId}`
    let data = await getItemsService(lists.rule_visit_item_detail_nl_view, {
      filter: filter,
      top: 2000,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getRuleVisitItemDetailNotesByPatientVisitId = async (patientVisitId) => {
  try {
    let filter = `patient_visit_id eq ${patientVisitId}`
    let data = await getItemsService(lists.rule_visit_item_detail_note, {
      filter: filter,
      top: 2000,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const getRuleVisitItemDetailNoteNlViewsByPatientVisitId = async (patientVisitId) => {
  try {
    let filter = `patient_visit_id eq ${patientVisitId}`
    let data = await getItemsService(lists.rule_visit_item_detail_note_nl_view, {
      filter: filter,
      top: 2000,
    })

    return data
  } catch (error) {
    handleError(error)
  }
}

export const updateManualCheckFlag = async (ruleVisitItemDetailId, manualCheckedFlag, luUserId) => {
  try {
    const ruleVisitItemDetail = {
      rule_visit_item_detail_id: ruleVisitItemDetailId,
      manual_checked_flag: manualCheckedFlag,
      lu_user_id: luUserId,
      lu_updated: dayjs(),
    }

    const response = await updateListItemService(
      lists.rule_visit_item_detail,
      ruleVisitItemDetailId,
      ruleVisitItemDetail,
    )
    return response
  } catch (error) {
    handleError(error)
  }
}

/**
 * Helper function for finding rule details by XML ID
 * @param {string} ruleRcd - The rule code to find
 * @param {string} ssXmlId - The XML record ID
 * @param {Array} ruleVisitItemDetails - List of existing rule visit item details
 * @returns {string|null} The rule_visit_item_detail_id if found
 */
function findRuleVisitItemDetailByXmlId(ruleRcd, ssXmlId, ruleVisitItemDetails = []) {
  const matchingRecord = ruleVisitItemDetails.find(
    (detail) =>
      detail.rule_rcd === ruleRcd && detail.ss_xml_id === ssXmlId && detail.rule_object === 'XML',
  )
  return matchingRecord || null
}

/**
 * Helper function for finding rule details by patient visit ID
 * @param {string} ruleRcd - The rule code to find
 * @param {string} patientVisitId - The patient visit ID
 * @param {Array} ruleVisitItemDetails - List of existing rule visit item details
 * @returns {string|null} The rule_visit_item_detail_id if found
 */
function findRuleVisitItemDetailByPatientVisit(ruleRcd, patientVisitId, ruleVisitItemDetails = []) {
  const matchingRecord = ruleVisitItemDetails.find(
    (item) =>
      item.rule_rcd === ruleRcd &&
      item.patient_visit_id === patientVisitId &&
      item.rule_object === 'VISIT',
  )
  return matchingRecord || null
}

/**
 * Validates a VISIT rule against patient visit data
 * @returns {boolean} Whether the rule passes validation
 */
export function validateVisitRule(rule, patientVisit, medicalCodings, ruleVisitItemDetails = []) {
  let results = []
  const mainPatientVisitId = patientVisit?.patient_visit_id
  const ssXmlTableName = rule?.ss_xml_table_name

  switch (rule.rule_rcd) {
    case 'CHECK_LY_DO_VV': {
      let systemCheckedFlag
      if (patientVisit.visit_type_group_rcd === 'OPD') {
        // OPD luôn đúng
        systemCheckedFlag = true
      } else {
        // IPD cần có ít nhất một chẩn đoán phụ
        systemCheckedFlag =
          Array.isArray(medicalCodings) &&
          medicalCodings.some((coding) => coding.primary_flag === 0)
      }

      const ruleVisitItemDetail = findRuleVisitItemDetailByPatientVisit(
        rule.rule_rcd,
        mainPatientVisitId,
        ruleVisitItemDetails,
      )

      const ruleDetailParam = {
        mainPatientVisitId,
        ssXmlId: null,
        maLK: null,
        invoiceNo: null,
        ssXmlTableName,
        manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
        ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
      }

      results.push(
        createBaseRuleVisitItemDetail(
          rule,
          systemCheckedFlag,
          ruleDetailParam,
          ruleVisitItemDetail,
        ),
      )
      break
    }
    case 'CHECK_MA_BENH_CHINH': {
      let systemCheckedFlag
      systemCheckedFlag = medicalCodings.some((coding) => coding.primary_flag == true)

      const ruleVisitItemDetail = findRuleVisitItemDetailByPatientVisit(
        rule.rule_rcd,
        mainPatientVisitId,
        ruleVisitItemDetails,
      )

      const ruleDetailParam = {
        mainPatientVisitId,
        ssXmlId: null,
        maLK: null,
        invoiceNo: null,
        ssXmlTableName,
        manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
        ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
      }

      results.push(
        createBaseRuleVisitItemDetail(
          rule,
          systemCheckedFlag,
          ruleDetailParam,
          ruleVisitItemDetail,
        ),
      )
      break
    }

    case 'CHECK_KET_QUA_DTRI': {
      const systemCheckedFlag = !!patientVisit.KET_QUA_DTRI

      const ruleVisitItemDetail = findRuleVisitItemDetailByPatientVisit(
        rule.rule_rcd,
        mainPatientVisitId,
        ruleVisitItemDetails,
      )

      const ruleDetailParam = {
        mainPatientVisitId,
        ssXmlId: null,
        maLK: null,
        invoiceNo: null,
        ssXmlTableName,
        manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
        ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
      }

      results.push(
        createBaseRuleVisitItemDetail(
          rule,
          systemCheckedFlag,
          ruleDetailParam,
          ruleVisitItemDetail,
        ),
      )
      break
    }

    case 'CHECK_MA_LOAI_RV': {
      const systemCheckedFlag = !!patientVisit.MA_LOAI_RV

      const ruleVisitItemDetail = findRuleVisitItemDetailByPatientVisit(
        rule.rule_rcd,
        mainPatientVisitId,
        ruleVisitItemDetails,
      )

      const ruleDetailParam = {
        mainPatientVisitId,
        ssXmlId: null,
        maLK: null,
        invoiceNo: null,
        ssXmlTableName,
        manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
        ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
      }

      results.push(
        createBaseRuleVisitItemDetail(
          rule,
          systemCheckedFlag,
          ruleDetailParam,
          ruleVisitItemDetail,
        ),
      )
      break
    }
  }
  return results
}

/**
 * Validates an XML rule against ss_table data
 * @returns {Array} Array of validation results
 */
export function validateXmlRule(rule, allSSXmlTables, mainVisit, ruleVisitItemDetails = []) {
  // Truy cập ssTable1 truyền vào từ context
  const ssTable1 = allSSXmlTables?.ssTable1 || {}
  const ssTable2 = allSSXmlTables?.ssTable2 || []
  const ssTable3 = allSSXmlTables?.ssTable3 || []
  const ssTable4 = allSSXmlTables?.ssTable4 || []
  const ssTable5 = allSSXmlTables?.ssTable5 || []
  const ssTable7 = allSSXmlTables?.ssTable7 || []
  const epsilon = 0.0001
  const MA_NHOM_XET_NGHIEM_HINH_ANH_THAM_DO_RCD = ['1', '2', '3']

  let mainPatientVisitId = ssTable1?.patient_visit_id__
  let maLK = ssTable1?.MA_LK
  let invoiceNo = ssTable1?.invoice_no_
  let chargeDetailId = null
  const ssXmlTableName = rule?.ss_xml_table_name

  // Khởi tạo mảng kết quả
  let results = []

  switch (rule.rule_rcd) {
    case 'CHECK_CHAN_DOAN_VAO': {
      const systemCheckedFlag = !!ssTable1.CHAN_DOAN_VAO
      const ssXmlId = ssTable1?.table_1_id || null
      const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
        rule.rule_rcd,
        ssXmlId,
        ruleVisitItemDetails,
      )
      const ruleDetailParam = {
        mainPatientVisitId,
        ssXmlId,
        maLK,
        invoiceNo,
        ssXmlTableName,
        manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
        ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
      }
      results.push(
        createBaseRuleVisitItemDetail(
          rule,
          systemCheckedFlag,
          ruleDetailParam,
          ruleVisitItemDetail,
        ),
      )
      break
    }

    case 'CHECK_CHAN_DOAN_RV': {
      const systemCheckedFlag = !!ssTable1.CHAN_DOAN_RV
      const ssXmlId = ssTable1?.table_1_id || null
      const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
        rule.rule_rcd,
        ssXmlId,
        ruleVisitItemDetails,
      )
      const ruleDetailParam = {
        mainPatientVisitId,
        ssXmlId,
        maLK,
        invoiceNo,
        ssXmlTableName,
        manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
        ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
      }
      results.push(
        createBaseRuleVisitItemDetail(
          rule,
          systemCheckedFlag,
          ruleDetailParam,
          ruleVisitItemDetail,
        ),
      )
      break
    }

    case 'CHECK_T_THUOC': {
      const SUM_T_BHTT_XML2 = ssTable2.reduce((sum, item) => {
        return sum + (item?.T_BHTT || 0)
      }, 0)

      const systemCheckedFlag = Math.abs(ssTable1.T_THUOC - SUM_T_BHTT_XML2) < epsilon
      const ssXmlId = ssTable1?.table_1_id || null
      const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
        rule.rule_rcd,
        ssXmlId,
        ruleVisitItemDetails,
      )
      const ruleDetailParam = {
        mainPatientVisitId,
        ssXmlId,
        maLK,
        invoiceNo,
        ssXmlTableName,
        manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
        ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
      }
      results.push(
        createBaseRuleVisitItemDetail(
          rule,
          systemCheckedFlag,
          ruleDetailParam,
          ruleVisitItemDetail,
        ),
      )
      break
    }

    case 'CHECK_T_TONGCHI_BV': {
      const SUM_THANH_TIEN_BV_XML2 = calculateTotalForColumn(ssTable2, 'THANH_TIEN_BV')
      const SUM_THANH_TIEN_BV_XML3 = calculateTotalForColumn(ssTable3, 'THANH_TIEN_BV')

      const systemCheckedFlag =
        Math.abs(ssTable1?.T_TONGCHI_BV - (SUM_THANH_TIEN_BV_XML2 + SUM_THANH_TIEN_BV_XML3)) <
        epsilon

      const ssXmlId = ssTable1?.table_1_id || null
      const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
        rule.rule_rcd,
        ssXmlId,
        ruleVisitItemDetails,
      )
      const ruleDetailParam = {
        mainPatientVisitId,
        ssXmlId,
        maLK,
        invoiceNo,
        ssXmlTableName,
        manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
        ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
      }
      results.push(
        createBaseRuleVisitItemDetail(
          rule,
          systemCheckedFlag,
          ruleDetailParam,
          ruleVisitItemDetail,
        ),
      )
      break
    }

    case 'CHECK_T_TONGCHI_BH': {
      const SUM_THANH_TIEN_BH_XML2 = calculateTotalForColumn(ssTable2, 'THANH_TIEN_BH')
      const SUM_THANH_TIEN_BH_XML3 = calculateTotalForColumn(ssTable3, 'THANH_TIEN_BH')

      const systemCheckedFlag =
        Math.abs(ssTable1?.T_TONGCHI_BH - (SUM_THANH_TIEN_BH_XML2 + SUM_THANH_TIEN_BH_XML3)) <
        epsilon

      const ssXmlId = ssTable1?.table_1_id || null
      const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
        rule.rule_rcd,
        ssXmlId,
        ruleVisitItemDetails,
      )
      const ruleDetailParam = {
        mainPatientVisitId,
        ssXmlId,
        maLK,
        invoiceNo,
        ssXmlTableName,
        manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
        ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
      }
      results.push(
        createBaseRuleVisitItemDetail(
          rule,
          systemCheckedFlag,
          ruleDetailParam,
          ruleVisitItemDetail,
        ),
      )
      break
    }

    case 'CHECK_T_BHTT': {
      const T_BHTT_XML2 = calculateTotalForColumn(ssTable2, 'T_BHTT')
      const T_BHTT_XML3 = calculateTotalForColumn(ssTable3, 'T_BHTT')

      const systemCheckedFlag = Math.abs(ssTable1.T_BHTT - (T_BHTT_XML2 + T_BHTT_XML3)) < epsilon

      const ssXmlId = ssTable1?.table_1_id || null
      const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
        rule.rule_rcd,
        ssXmlId,
        ruleVisitItemDetails,
      )
      const ruleDetailParam = {
        mainPatientVisitId,
        ssXmlId,
        maLK,
        invoiceNo,
        ssXmlTableName,
        manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
        ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
      }
      results.push(
        createBaseRuleVisitItemDetail(
          rule,
          systemCheckedFlag,
          ruleDetailParam,
          ruleVisitItemDetail,
        ),
      )
      break
    }

    case 'CHECK_T_BNCCT': {
      const T_BNCCT_XML2 = calculateTotalForColumn(ssTable2, 'T_BNCCT')
      const T_BNCCT_XML3 = calculateTotalForColumn(ssTable3, 'T_BNCCT')

      const systemCheckedFlag = Math.abs(ssTable1.T_BNCCT - (T_BNCCT_XML2 + T_BNCCT_XML3)) < epsilon

      const ssXmlId = ssTable1?.table_1_id || null
      const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
        rule.rule_rcd,
        ssXmlId,
        ruleVisitItemDetails,
      )
      const ruleDetailParam = {
        mainPatientVisitId,
        ssXmlId,
        maLK,
        invoiceNo,
        ssXmlTableName,
        manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
        ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
      }
      results.push(
        createBaseRuleVisitItemDetail(
          rule,
          systemCheckedFlag,
          ruleDetailParam,
          ruleVisitItemDetail,
        ),
      )
      break
    }

    case 'CHECK_T_BNTT': {
      const T_BNTT_XML2 = calculateTotalForColumn(ssTable2, 'T_BNTT')
      const T_BNTT_XML3 = calculateTotalForColumn(ssTable3, 'T_BNTT')

      const systemCheckedFlag = Math.abs(ssTable1.T_BNTT - (T_BNTT_XML2 + T_BNTT_XML3)) < epsilon

      const ssXmlId = ssTable1?.table_1_id || null
      const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
        rule.rule_rcd,
        ssXmlId,
        ruleVisitItemDetails,
      )
      const ruleDetailParam = {
        mainPatientVisitId,
        ssXmlId,
        maLK,
        invoiceNo,
        ssXmlTableName,
        manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
        ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
      }
      results.push(
        createBaseRuleVisitItemDetail(
          rule,
          systemCheckedFlag,
          ruleDetailParam,
          ruleVisitItemDetail,
        ),
      )
      break
    }

    // === XML 2 checks (thuốc) ===
    case 'CHECK_MA_THUOC': {
      if (ssTable2.length > 0) {
        // Kiểm tra từng dòng trong ssTable2
        for (const item of ssTable2) {
          const systemCheckedFlag = !!item.MA_THUOC
          const ssXmlId = item?.table_2_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_MA_NHOM': {
      if (ssTable2.length > 0) {
        // Kiểm tra từng dòng trong ssTable2
        for (const item of ssTable2) {
          const systemCheckedFlag = !!item.MA_NHOM
          const ssXmlId = item?.table_2_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_TEN_THUOC': {
      if (ssTable2.length > 0) {
        // Kiểm tra từng dòng trong ssTable2
        for (const item of ssTable2) {
          const systemCheckedFlag = !!item.TEN_THUOC
          const ssXmlId = item?.table_2_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_DON_VI_TINH': {
      if (ssTable2.length > 0) {
        // Kiểm tra từng dòng trong ssTable2
        for (const item of ssTable2) {
          const systemCheckedFlag = !!item.DON_VI_TINH
          const ssXmlId = item?.table_2_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_HAM_LUONG': {
      if (ssTable2.length > 0) {
        // Kiểm tra từng dòng trong ssTable2
        for (const item of ssTable2) {
          const systemCheckedFlag = !!item.HAM_LUONG
          const ssXmlId = item?.table_2_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_DUONG_DUNG': {
      if (ssTable2.length > 0) {
        // Kiểm tra từng dòng trong ssTable2
        for (const item of ssTable2) {
          const systemCheckedFlag = !!item.DUONG_DUNG
          const ssXmlId = item?.table_2_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_LIEU_DUNG': {
      if (ssTable2.length > 0) {
        // Kiểm tra từng dòng trong ssTable2
        for (const item of ssTable2) {
          const systemCheckedFlag = !!item.LIEU_DUNG
          const ssXmlId = item?.table_2_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_SO_DANG_KY': {
      if (ssTable2.length > 0) {
        // Kiểm tra từng dòng trong ssTable2
        for (const item of ssTable2) {
          const systemCheckedFlag = !!item.SO_DANG_KY
          const ssXmlId = item?.table_2_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_TT_THAU': {
      if (ssTable2.length > 0) {
        // Kiểm tra từng dòng trong ssTable2
        for (const item of ssTable2) {
          const systemCheckedFlag = !!item.TT_THAU
          const ssXmlId = item?.table_2_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_PHAM_VI': {
      if (ssTable2.length > 0) {
        // Kiểm tra từng dòng trong ssTable2
        for (const item of ssTable2) {
          const systemCheckedFlag = !!item.PHAM_VI
          const ssXmlId = item?.table_2_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    // === XML 3 checks ===
    case 'CHECK_PP_VO_CAM': {
      const isIPD = mainVisit?.visit_type_group_rcd === 'IPD'

      if (ssTable3 && ssTable3.length > 0) {
        for (const item of ssTable3) {
          let systemCheckedFlag

          if (!isIPD) {
            // Case 1: Not IPD - always pass
            systemCheckedFlag = true
          } else if (item.MA_NHOM === '8' || item.MA_NHOM === '18') {
            // Case 2: Is IPD with group 8 or 18 - need PP_VO_CAM
            systemCheckedFlag = !!item.PP_VO_CAM
          } else {
            // Other groups for IPD - pass
            systemCheckedFlag = true
          }

          const ssXmlId = item?.table_3_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )
          chargeDetailId = item?.charge_detail_id_

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            chargeDetailId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    // === XML 4 checks (xét nghiệm) ===
    case 'CHECK_MA_DICH_VU': {
      if (ssTable4.length > 0) {
        // Kiểm tra từng dòng trong ssTable4
        for (const item of ssTable4) {
          const systemCheckedFlag = !!item.MA_DICH_VU
          const ssXmlId = item?.table_4_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )
          chargeDetailId = item?.charge_detail_id_

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            chargeDetailId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_MA_CHI_SO': {
      if (ssTable4.length > 0) {
        // Kiểm tra từng dòng trong ssTable4, không cần filter
        for (const item of ssTable4) {
          // Logic kiểm tra:
          // - Nếu MA_NHOM thuộc nhóm XN/CĐHA/TDCN thì cần có MA_CHI_SO
          // - Nếu MA_NHOM không thuộc nhóm trên thì luôn đúng
          const isRelevantGroup = MA_NHOM_XET_NGHIEM_HINH_ANH_THAM_DO_RCD.includes(item.MA_NHOM)
          const systemCheckedFlag = isRelevantGroup ? !!item.MA_CHI_SO : true

          const ssXmlId = item?.table_4_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )
          chargeDetailId = item?.charge_detail_id_

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            chargeDetailId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_TEN_CHI_SO': {
      if (ssTable4.length > 0) {
        // Kiểm tra từng dòng trong ssTable4, không cần filter
        for (const item of ssTable4) {
          // Logic kiểm tra:
          // - Nếu MA_NHOM thuộc nhóm XN/CĐHA/TDCN thì cần có TEN_CHI_SO
          // - Nếu MA_NHOM không thuộc nhóm trên thì luôn đúng
          const isRelevantGroup = MA_NHOM_XET_NGHIEM_HINH_ANH_THAM_DO_RCD.includes(item.MA_NHOM)
          const systemCheckedFlag = isRelevantGroup ? !!item.TEN_CHI_SO : true

          const ssXmlId = item?.table_4_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )
          chargeDetailId = item?.charge_detail_id_

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            chargeDetailId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_GIA_TRI': {
      if (ssTable4.length > 0) {
        // Kiểm tra từng dòng trong ssTable4, không cần filter
        for (const item of ssTable4) {
          // Logic kiểm tra:
          // - Nếu MA_NHOM thuộc nhóm XN/CĐHA/TDCN thì cần có GIA_TRI
          // - Nếu MA_NHOM không thuộc nhóm trên thì luôn đúng
          const isRelevantGroup = MA_NHOM_XET_NGHIEM_HINH_ANH_THAM_DO_RCD.includes(item.MA_NHOM)
          const systemCheckedFlag = isRelevantGroup ? !!item.GIA_TRI : true

          const ssXmlId = item?.table_4_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )
          chargeDetailId = item?.charge_detail_id_

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            chargeDetailId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_DON_VI_DO': {
      if (ssTable4.length > 0) {
        // Kiểm tra từng dòng trong ssTable4, không cần filter
        for (const item of ssTable4) {
          // Logic kiểm tra:
          // - Nếu MA_NHOM thuộc nhóm XN/CĐHA/TDCN thì cần có DON_VI_DO
          // - Nếu MA_NHOM không thuộc nhóm trên thì luôn đúng
          const isRelevantGroup = MA_NHOM_XET_NGHIEM_HINH_ANH_THAM_DO_RCD.includes(item.MA_NHOM)
          const systemCheckedFlag = isRelevantGroup ? !!item.DON_VI_DO : true

          const ssXmlId = item?.table_4_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )
          chargeDetailId = item?.charge_detail_id_

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            chargeDetailId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_KET_LUAN': {
      if (ssTable4.length > 0) {
        // Lọc các dòng thuộc nhóm 2 (chẩn đoán hình ảnh)
        const relevantItems = ssTable4.filter((it) => it.MA_NHOM === '2')

        if (relevantItems.length > 0) {
          // Kiểm tra từng dòng thuộc nhóm cần kiểm tra
          for (const item of relevantItems) {
            const systemCheckedFlag = !!item.KET_LUAN

            const ssXmlId = item?.table_4_id || null
            const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
              rule.rule_rcd,
              ssXmlId,
              ruleVisitItemDetails,
            )
            chargeDetailId = item?.charge_detail_id_

            const ruleDetailParam = {
              mainPatientVisitId,
              ssXmlId,
              chargeDetailId,
              maLK,
              invoiceNo,
              ssXmlTableName,
              manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
              ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
            }

            results.push(
              createBaseRuleVisitItemDetail(
                rule,
                systemCheckedFlag,
                ruleDetailParam,
                ruleVisitItemDetail,
              ),
            )
          }
        }
      }
      break
    }

    case 'CHECK_NGAY_KQ': {
      if (ssTable4.length > 0) {
        for (const item of ssTable4) {
          // logic kiem tra
          // Nếu dòng có GIA_TRI thì cần có NGAY_KQ
          // Nếu dòng không có GIA_TRI thì luôn đúng.
          const hasGiaTri = !!item.GIA_TRI
          const systemCheckedFlag = hasGiaTri ? !!item?.NGAY_KQ : true

          const ssXmlId = item?.table_4_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )
          chargeDetailId = item?.charge_detail_id_

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            chargeDetailId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_MA_BS_DOC_KQ': {
      if (ssTable4.length > 0) {
        // Kiểm tra từng dòng trong ssTable4
        for (const item of ssTable4) {
          const systemCheckedFlag = !!item.MA_BS_DOC_KQ

          const ssXmlId = item?.table_4_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )
          chargeDetailId = item?.charge_detail_id_

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            chargeDetailId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    // === XML 7 checks (điều trị) ===
    case 'CHECK_PP_DIEUTRI': {
      if (ssTable7.length > 0) {
        // Kiểm tra từng dòng trong ssTable7
        for (const item of ssTable7) {
          const systemCheckedFlag = !!item.PP_DIEUTRI

          const ssXmlId = item?.table_7_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_MA_BS': {
      if (ssTable7.length > 0) {
        // Kiểm tra từng dòng trong ssTable7
        for (const item of ssTable7) {
          const systemCheckedFlag = !!item.MA_BS

          const ssXmlId = item?.table_7_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_TEN_BS': {
      if (ssTable7.length > 0) {
        // Kiểm tra từng dòng trong ssTable7
        for (const item of ssTable7) {
          const systemCheckedFlag = !!item.TEN_BS

          const ssXmlId = item?.table_7_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }

    case 'CHECK_NGAY_CT': {
      if (ssTable7.length > 0) {
        // Kiểm tra từng dòng trong ssTable7
        for (const item of ssTable7) {
          const systemCheckedFlag = !!item.NGAY_CT

          const ssXmlId = item?.table_7_id || null
          const ruleVisitItemDetail = findRuleVisitItemDetailByXmlId(
            rule.rule_rcd,
            ssXmlId,
            ruleVisitItemDetails,
          )

          const ruleDetailParam = {
            mainPatientVisitId,
            ssXmlId,
            maLK,
            invoiceNo,
            ssXmlTableName,
            manualCheckedFlag: ruleVisitItemDetail?.manual_checked_flag,
            ruleVisitItemDetailId: ruleVisitItemDetail?.rule_visit_item_detail_id,
          }

          results.push(
            createBaseRuleVisitItemDetail(
              rule,
              systemCheckedFlag,
              ruleDetailParam,
              ruleVisitItemDetail,
            ),
          )
        }
      }
      break
    }
  }

  return results
}

/**
 * Validates a rule based on its rule_object type
 * @param {Object} rule - The rule to validate
 * @param {Object} context - Includes patientVisit, ss_table_1, medicalCodings
 * @returns {Array} Results of the validation
 */
export function validateRuleByObject(rule, context, ruleVisitItemDetailNlViewsData) {
  switch (rule.rule_object) {
    case 'VISIT':
      return validateVisitRule(
        rule,
        context?.patientVisit,
        context?.medicalCodings,
        ruleVisitItemDetailNlViewsData,
      )
    case 'XML':
      return validateXmlRule(
        rule,
        context?.allSSXmlTables,
        context?.patientVisit,
        ruleVisitItemDetailNlViewsData,
      )
    default:
      return []
  }
}

/**
 * Validates all rules in a list and returns the results
 * @param {Array} ruleRefsData - Rules to validate
 * @param {Object} context - Context containing patientVisit, ssTable1, etc.
 * @param {Array} ruleVisitItemDetailNlViewsData - Existing rule visit item detail views data
 * @returns {Array} The rules with system_checked_flag set
 */
export const getValidatedRules = (ruleRefsData, context, ruleVisitItemDetailNlViewsData = []) => {
  let validatedRuleVisitItemDetails = []
  for (const rule of ruleRefsData) {
    validatedRuleVisitItemDetails.push(
      ...validateRuleByObject(rule, context, ruleVisitItemDetailNlViewsData),
    )
  }
  return validatedRuleVisitItemDetails
}

// export const validateRuleAndCreateDetails = async ({
//   ruleRefs,
//   patientVisitMappingView,
//   medicalCodings,
//   ssTable1,
// }) => {
//   try {
//     for (const ruleRef of ruleRefs) {
//       let isValid = false
//       // Check if the rule is an XML rule or a VISIT rule
//       if (ruleRef?.rule_object == 'VISIT') {
//         isValid = validateVisitRule(ruleRef?.rule_rcd, patientVisitMappingView, medicalCodings)
//       } else if (ruleRef?.rule_object == 'XML') {
//         isValid = validateXmlRule(ruleRef?.rule_rcd, ssTable1)
//       } else {
//         continue
//       }

//       let dataSource = null

//       if (ruleRef?.rule_object == 'VISIT') {
//         dataSource = patientVisitMappingView
//       } else if (ruleRef?.rule_object == 'XML') {
//         dataSource = ssTable1
//       }
//     }
//   } catch (error) {}
// }
/**
 * Creates a rule_visit_item_detail record
 */

/**
 * Maps rule_rcd to the XML column name
 */
function getColumnNameForRule(rule_rcd) {
  const columnMappings = {
    // Existing mappings
    CHECK_CHAN_DOAN_VAO: 'CHAN_DOAN_VAO',
    CHECK_CHAN_DOAN_RV: 'CHAN_DOAN_RV',
    CHECK_MA_BENH_CHINH: 'MA_BENH_CHINH',
    CHECK_KET_QUA_DTRI: 'KET_QUA_DTRI',
    CHECK_MA_LOAI_RV: 'MA_LOAI_RV',
    CHECK_LY_DO_VV: 'LY_DO_VV',
    CHECK_T_THUOC: 'T_THUOC',
    CHECK_T_VTYT: 'T_VTYT',
    CHECK_T_TONGCHI_BV: 'T_TONGCHI_BV',
    CHECK_T_TONGCHI_BH: 'T_TONGCHI_BH',
    CHECK_T_BNTT: 'T_BNTT',
    CHECK_T_BNCCT: 'T_BNCCT',
    CHECK_T_BHTT: 'T_BHTT',

    // Medications (Table 2)
    CHECK_MA_THUOC: 'MA_THUOC',
    CHECK_MA_NHOM: 'MA_NHOM',
    CHECK_TEN_THUOC: 'TEN_THUOC',
    CHECK_DON_VI_TINH: 'DON_VI_TINH',
    CHECK_HAM_LUONG: 'HAM_LUONG',
    CHECK_DUONG_DUNG: 'DUONG_DUNG',
    CHECK_LIEU_DUNG: 'LIEU_DUNG',
    CHECK_SO_DANG_KY: 'SO_DANG_KY',
    CHECK_TT_THAU: 'TT_THAU',
    CHECK_PHAM_VI: 'PHAM_VI',

    // Procedures (Table 3)
    CHECK_PP_VO_CAM: 'PP_VO_CAM',

    // Lab/Diagnostics (Table 4)
    CHECK_MA_DICH_VU: 'MA_DICH_VU',
    CHECK_MA_CHI_SO: 'MA_CHI_SO',
    CHECK_TEN_CHI_SO: 'TEN_CHI_SO',
    CHECK_GIA_TRI: 'GIA_TRI',
    CHECK_DON_VI_DO: 'DON_VI_DO',
    CHECK_KET_LUAN: 'KET_LUAN',
    CHECK_NGAY_KQ: 'NGAY_KQ',
    CHECK_MA_BS_DOC_KQ: 'MA_BS_DOC_KQ',

    // Treatment (Table 7)
    CHECK_PP_DIEUTRI: 'PP_DIEUTRI',
    CHECK_MA_BS: 'MA_BS',
    CHECK_TEN_BS: 'TEN_BS',
    CHECK_NGAY_CT: 'NGAY_CT',
  }

  return columnMappings[rule_rcd] || null
}

export const mapRuleRefsWithDetails = (ruleRefs, ruleVisitItemDetails) => {
  return ruleRefs.map((ruleRef) => {
    const matchingItem = ruleVisitItemDetails.find(
      (ruleItemDetail) => ruleRef.rule_rcd == ruleItemDetail.rule_rcd,
    )
    return {
      ...matchingItem,
      //      rule_ref: ruleRef,
      rule_rcd: ruleRef.rule_rcd,
      rule_name: ruleRef.name,
      rule_error_message: ruleRef.error_message,
      rule_object: ruleRef.rule_object,
      warning_type: ruleRef.warning_type,
    }
  })
}

export const prepareRulesData = (validatedRules, mainPatientVisitId, currentUser, context) => {
  const dataProcessing = { VISIT: context.patientVisit || {}, XML: context.ssTable1 || {} }
  // Phân loại items cần tạo mới
  const itemsToCreate = validatedRules
    .filter((flitedItem) => !flitedItem.rule_visit_item_detail_id)
    .map((item) => {
      return {
        ar_invoice_detail_id: null,
        charge_detail_id: null,
        rule_rcd: item?.rule_rcd,
        patient_visit_id: mainPatientVisitId,
        system_checked_flag: item.system_checked_flag || false,
        manual_checked_flag: false, // Default value cho item mới
        MA_LK: item.MA_LK,
        ss_xml_id: item.ss_xml_id,
        ss_xml_column_name: getColumnNameForRule(item?.rule_rcd),
        invoice_no_: item.invoice_no_,
        ss_xml_table_name: item.ss_xml_table_name,
        lu_updated: dayjs(),
        lu_user_id: currentUser?.User_id,
      }
    })

  // Rest of your function remains the same...
  const itemsToUpdate = validatedRules
    .filter((flitedItem) => !!flitedItem.rule_visit_item_detail_id)
    .map((item) => ({
      rule_visit_item_detail_id: item.rule_visit_item_detail_id,
      system_checked_flag: item.system_checked_flag,
      lu_user_id: currentUser?.User_id,
      lu_updated: new Date().toISOString(),
    }))
  return { itemsToCreate, itemsToUpdate }
}

//#region helper funtions
function createBaseRuleVisitItemDetail(
  ruleRef,
  systemCheckedFlag,
  context = {
    mainPatientVisitId: null,
    ssXmlId: null,
    maLK: null,
    invoiceNo: null,
    ssXmlTableName: null,
    chargeDetailId: null,
    ruleVisitItemDetailId: null,
    manualCheckedFlag: false,
  },
  ruleVisitItemDetail,
) {
  return {
    ...ruleVisitItemDetail,
    rule_visit_item_detail_id: context.ruleVisitItemDetailId,
    ar_invoice_detail_id: null,
    charge_detail_id: context.chargeDetailId || null,
    rule_rcd: ruleRef.rule_rcd,
    rule_error_message: ruleRef.error_message,
    rule_object: ruleRef.rule_object,
    rule_name: ruleRef.name,
    warning_type: ruleRef.warning_type,
    patient_visit_id: context.mainPatientVisitId,
    system_checked_flag: systemCheckedFlag,
    manual_checked_flag: context.manualCheckedFlag || false,
    MA_LK: context?.maLK,
    ss_xml_id: context?.ssXmlId,
    ss_xml_column_name: getColumnNameForRule(ruleRef.rule_rcd),
    invoice_no_: context?.invoiceNo,
    ss_xml_table_name: context?.ssXmlTableName,
  }
}

export const getUpdatedPatientVisitWarningStatus = (
  patientVisit,
  validatedRuleVisitItemDetails,
) => {
  const validatedVisitRuleObjects = validatedRuleVisitItemDetails.filter(
    (item) => item.rule_object == 'VISIT',
  )

  if (!patientVisit.patient_visit_id || validatedVisitRuleObjects.length == 0) return

  const warningStatus = validatedVisitRuleObjects.some((item) => item.system_checked_flag == false)
    ? RULE_WARNING_STATUS.INVALID.key
    : RULE_WARNING_STATUS.VALID.key

  return {
    patient_visit_id: patientVisit.patient_visit_id,
    warning_status: warningStatus,
  }
}

export const getUpdatedAllSSXmlTablesWarningStatus = (
  allSSXmlTables,
  validatedRuleVisitItemDetails,
  isUpdateNEWStatus = false,
) => {
  const result = {}
  if (!allSSXmlTables || validatedRuleVisitItemDetails.length == 0) return

  for (let i = 0; i < XML_LIST.length; i++) {
    const xmlTable = XML_LIST[i]
    // Process each SS table based on table_name
    switch (xmlTable.table_name) {
      case 'ssTable1': {
        // Special processing for ssTable1 as it's an object
        const ssTableData = allSSXmlTables[xmlTable.table_name] || {}
        if (!ssTableData || !ssTableData[xmlTable.primary_key_name]) continue

        const ssTableRuleDetails = validatedRuleVisitItemDetails.filter(
          (item) => item.ss_xml_table_name == xmlTable.list_name && item.rule_object == 'XML',
        )

        const warningStatus = isUpdateNEWStatus
          ? RULE_WARNING_STATUS.NEW.key
          : ssTableRuleDetails.some((item) => item.system_checked_flag == false)
            ? RULE_WARNING_STATUS.INVALID.key
            : RULE_WARNING_STATUS.VALID.key

        result[xmlTable.table_name] = {
          [xmlTable.primary_key_name]: ssTableData[xmlTable.primary_key_name],
          warning_status: warningStatus,
        }

        break
      }

      // For array-type tables (ssTable2, ssTable3, ssTable4, ssTable5, ssTable7, etc.)
      case 'ssTable2':
      case 'ssTable3':
      case 'ssTable4':
      case 'ssTable5':
      case 'ssTable7': {
        // Process array-type tables
        const tableData = allSSXmlTables[xmlTable.table_name] || []

        if (!tableData || !tableData.length) continue

        const updatedSsTableData = tableData
          .filter((item) => item && item[xmlTable.primary_key_name]) // Chỉ giữ item có key
          .map((item) => {
            const tableRuleDetails = validatedRuleVisitItemDetails.filter(
              (detail) =>
                detail.ss_xml_table_name === xmlTable.list_name &&
                detail.rule_object === 'XML' &&
                detail.ss_xml_id === item[xmlTable.primary_key_name],
            )

            const tableWarningStatus = isUpdateNEWStatus
              ? RULE_WARNING_STATUS.NEW.key
              : tableRuleDetails.some((detail) => detail.system_checked_flag === false)
                ? RULE_WARNING_STATUS.INVALID.key
                : RULE_WARNING_STATUS.VALID.key

            return {
              [xmlTable.primary_key_name]: item[xmlTable.primary_key_name],
              warning_status: tableWarningStatus,
            }
          })

        result[xmlTable.table_name] = updatedSsTableData
        break
      }

      default:
        // Do nothing for unhandled table types
        break
    }
  }
  return result
}

//
