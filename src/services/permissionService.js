import { handleError } from '../common/helpers'
import lists from '../common/lists'
import {
  getItemsService,
  getItemService,
  addListItemService,
  updateListItemService,
  deleteListItemService,
} from '../common/services'
import { v4 as uuidv4 } from 'uuid'

// User Group Management
export const getUserGroups = async (options = {}) => {
  try {
    const filter = options.filter || '(active_flag eq true)'
    const orderBy = options.orderBy || 'name_e asc'
    const data = await getItemsService(lists.user_group_ref, {
      filter,
      orderBy,
      top: options.top || 1000,
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getUserGroups')
    throw error
  }
}

export const getUserGroupById = async (userGroupId) => {
  try {
    const data = await getItemService(lists.user_group_ref, userGroupId)
    return data
  } catch (error) {
    handleError(error, 'getUserGroupById')
    throw error
  }
}

export const createUserGroup = async (userGroup) => {
  try {
    // Generate UUID if not provided
    if (!userGroup.user_group_id) {
      userGroup.user_group_id = uuidv4()
    }

    // Set active flag if not provided
    if (userGroup.active_flag === undefined) {
      userGroup.active_flag = true
    }

    // Set update timestamp if not provided
    if (!userGroup.lu_updated) {
      userGroup.lu_updated = new Date().toISOString()
    }

    const data = await addListItemService(lists.user_group_ref, userGroup)
    return data
  } catch (error) {
    handleError(error, 'createUserGroup')
    throw error
  }
}

export const updateUserGroup = async (userGroupId, userGroup) => {
  try {
    // Set update timestamp
    userGroup.lu_updated = new Date().toISOString()

    const data = await updateListItemService(lists.user_group_ref, userGroupId, userGroup)
    return data
  } catch (error) {
    handleError(error, 'updateUserGroup')
    throw error
  }
}

export const deleteUserGroup = async (userGroupId) => {
  try {
    // Soft delete by setting active_flag to false
    const data = await updateListItemService(lists.user_group_ref, userGroupId, {
      active_flag: false,
      lu_updated: new Date().toISOString(),
    })
    return data
  } catch (error) {
    handleError(error, 'deleteUserGroup')
    throw error
  }
}

// Permission Management
export const getPermissions = async (options = {}) => {
  try {
    const filter = options.filter
    const orderBy = options.orderBy || 'name asc'
    const data = await getItemsService(lists.permission_ref, {
      filter,
      orderBy,
      top: options.top || 1000,
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getPermissions')
    throw error
  }
}

export const getPermissionByCode = async (permissionRcd) => {
  try {
    const data = await getItemService(lists.permission_ref, permissionRcd)
    return data
  } catch (error) {
    handleError(error, 'getPermissionByCode')
    throw error
  }
}

export const createPermission = async (permission) => {
  try {
    // Set active flag if not provided
    if (permission.active_flag === undefined) {
      permission.active_flag = true
    }

    // Set update timestamp if not provided
    if (!permission.lu_updated) {
      permission.lu_updated = new Date().toISOString()
    }

    const data = await addListItemService(lists.permission_ref, permission)
    return data
  } catch (error) {
    handleError(error, 'createPermission')
    throw error
  }
}

export const updatePermission = async (permissionRcd, permission) => {
  try {
    // Set update timestamp
    permission.lu_updated = new Date().toISOString()

    const data = await updateListItemService(lists.permission_ref, permissionRcd, permission)
    return data
  } catch (error) {
    handleError(error, 'updatePermission')
    throw error
  }
}

export const deletePermission = async (permissionRcd) => {
  try {
    // Soft delete by setting active_flag to false
    const data = await deleteListItemService(lists.permission_ref, permissionRcd)
    return data
  } catch (error) {
    handleError(error, 'deletePermission')
    throw error
  }
}

// User Group Membership Management
export const getUserGroupMemberships = async (options = {}) => {
  try {
    const filter = options.filter || '(active_flag eq true)'
    const orderBy = options.orderBy || ''
    const data = await getItemsService(lists.user_group_membership, {
      filter,
      orderBy,
      top: options.top || 1000,
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getUserGroupMemberships')
    throw error
  }
}

export const getUserGroupMembershipsByUserId = async (userId) => {
  try {
    const filter = `(user_id eq ${userId}) and (active_flag eq true)`
    const data = await getItemsService(lists.user_group_membership, {
      filter,
    })
    return data
  } catch (error) {
    handleError(error, 'getUserGroupMembershipsByUserId')
    throw error
  }
}

export const getUserGroupMembershipsByGroupId = async (userGroupId) => {
  try {
    const filter = `(user_group_id eq ${userGroupId}) and (active_flag eq true)`
    const data = await getItemsService(lists.user_group_membership, {
      filter,
    })
    return data
  } catch (error) {
    handleError(error, 'getUserGroupMembershipsByGroupId')
    throw error
  }
}

export const addUserToGroup = async (userId, userGroupId) => {
  try {
    const membership = {
      user_group_membership_id: uuidv4(),
      user_id: userId,
      user_group_id: userGroupId,
      active_flag: true,
      lu_updated: new Date().toISOString(),
    }

    const data = await addListItemService(lists.user_group_membership, membership)
    return data
  } catch (error) {
    handleError(error, 'addUserToGroup')
    throw error
  }
}

export const removeUserFromGroup = async (membershipId) => {
  try {
    // Soft delete by setting active_flag to false
    const data = await updateListItemService(lists.user_group_membership, membershipId, {
      active_flag: false,
      lu_updated: new Date().toISOString(),
    })
    return data
  } catch (error) {
    handleError(error, 'removeUserFromGroup')
    throw error
  }
}

// Permission Group Mapping Management
export const getGroupPermissionMappings = async (options = {}) => {
  try {
    const filter = options.filter || '(active_flag eq true)'
    const orderBy = options.orderBy || ''
    const data = await getItemsService(lists.user_group_permission_mapping, {
      filter,
      orderBy,
      top: options.top || 1000,
      skip: options.skip || 0,
      count: options.count || false,
    })
    return data
  } catch (error) {
    handleError(error, 'getGroupPermissionMappings')
    throw error
  }
}

export const getGroupPermissionMappingsByGroupId = async (userGroupId) => {
  try {
    const filter = `(user_group_id eq ${userGroupId}) and (active_flag eq true)`
    const data = await getItemsService(lists.user_group_permission_mapping, {
      filter,
    })
    return data
  } catch (error) {
    handleError(error, 'getGroupPermissionMappingsByGroupId')
    throw error
  }
}

export const getGroupPermissionMappingsByPermissionCode = async (permissionRcd) => {
  try {
    const filter = `(permission_rcd eq '${permissionRcd}') and (active_flag eq true)`
    const data = await getItemsService(lists.user_group_permission_mapping, {
      filter,
    })
    return data
  } catch (error) {
    handleError(error, 'getGroupPermissionMappingsByPermissionCode')
    throw error
  }
}

export const assignPermissionToGroup = async (permissionRcd, userGroupId) => {
  try {
    const mapping = {
      user_group_permission_mapping_id: uuidv4(),
      permission_rcd: permissionRcd,
      user_group_id: userGroupId,
      active_flag: true,
      lu_updated: new Date().toISOString(),
    }

    const data = await addListItemService(lists.user_group_permission_mapping, mapping)
    return data
  } catch (error) {
    handleError(error, 'assignPermissionToGroup')
    throw error
  }
}

export const removePermissionFromGroup = async (mappingId) => {
  try {
    // Soft delete by setting active_flag to false
    const data = await updateListItemService(lists.user_group_permission_mapping, mappingId, {
      active_flag: false,
      lu_updated: new Date().toISOString(),
    })
    return data
  } catch (error) {
    handleError(error, 'removePermissionFromGroup')
    throw error
  }
}

// Check if user has permission
export const checkUserPermission = async (userId, permissionRcd) => {
  try {
    // Get user's group memberships
    const memberships = await getUserGroupMembershipsByUserId(userId)

    if (!memberships || !memberships.value || memberships.value.length === 0) {
      return false
    }

    // Get all user group IDs the user belongs to
    const userGroupIds = memberships.value.map((membership) => membership.user_group_id)

    // Check if any of the user's groups have the permission
    const filter = `(permission_rcd eq ${permissionRcd}) and (active_flag eq true) and (${userGroupIds.map((id) => `user_group_id eq ${id}`).join(' or ')})`

    const mappings = await getItemsService(lists.user_group_permission_mapping, {
      filter,
    })

    return mappings && mappings.value && mappings.value.length > 0
  } catch (error) {
    handleError(error, 'checkUserPermission')
    return false
  }
}
