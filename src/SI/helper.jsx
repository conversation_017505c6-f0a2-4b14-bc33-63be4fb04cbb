import { Typography } from 'antd'
import { XMLParser } from 'fast-xml-parser'
import { FORM_MODE } from '../common/constant'
import { getColumnFilterList } from '../common/helpers'
import { SearchOutlined } from '@ant-design/icons'

export function generateXMLToolTableColumns(jsonData, systemSettingTableColumns) {
  if (!Array.isArray(jsonData) || jsonData.length === 0) {
    return []
  }

  // Extract unique keys from the first object in the array
  const keys = Object.keys(jsonData[0])
  // console.log('entityType', entityType)
  //   // Map the keys to the column configuration
  let tableColumns = keys.map((key) => ({
    //field_name: key
    title: key, // Format title: Replace underscores and capitalize
    dataIndex: key, // Use the key as the dataIndex
    key: key, // Use the key as the unique column key
    editable: true,
    width: '40%',
    dataType: typeof key, // Get the data type of the value for this key
  }))

  return tableColumns
}

export function mapSystemSettingTableColumnList(rawTableColumns, systemSettingTableColumns) {
  // Create a map of systemSettingTableColumns by xml_col_name
  const settingsMap = {}
  let tableColumns = []
  if (!Array.isArray(systemSettingTableColumns)) {
    return tableColumns
  }
  for (const setting of systemSettingTableColumns) {
    settingsMap[setting.xml_col_name] = setting
  }

  // Merge tableColumns with systemSettingTableColumns
  tableColumns = rawTableColumns.map((col) => {
    const setting = settingsMap[col.dataIndex]
    if (setting) {
      return {
        ...col,
        // set_col_seq_display: setting.col_seq_display,
        // title: setting.col_name_display || col.title,
        // hidden: setting.col_hidden,
        // editable: setting.col_editable,
        // width: setting.col_seq_display ? `${setting.col_seq_display * 10}%` : col.width,
        // dataType: setting.datetime_type
        //   ? 'datetime'
        //   : setting.numeric_type
        //   ? 'number'
        //   : col.dataType

        col_editable: setting.col_editable,
        col_hidden: setting.col_hidden,
        col_seq_display: setting.col_seq_display,
        xml_file: setting.xml_file,

        numeric_type: setting.numeric_type,
        datetime_type: setting.datetime_type,
        max_string: setting.max_string,
        dataType: setting.datetime_type
          ? 'datetime'
          : setting.numeric_type
            ? 'number'
            : col.dataType,
        xml_type: setting.xml_type,
        // width: setting.col_seq_display ? `${setting.col_seq_display * 10}%` : col.width
      }
    }
    return col
  })
  // console.log('tableColumns', tableColumns)
  return tableColumns
}

export const generateXMLToolTableDataSoruce = (dataSource) => {
  if (!Array.isArray(dataSource) || dataSource.length === 0) {
    return []
  }
  return dataSource.map((item, index) => {
    return { key: index + 1, ...item }
  })
}

export const parseMetadata = (xml) => {
  if (!xml) return null
  const parser = new XMLParser({
    ignoreAttributes: false, // Do not ignore attributes
    attributeNamePrefix: '@_', // Add a prefix to attributes
    //  removeNSPrefix: true, // Remove namespace prefixes
    textNodeName: '#text', // Handle text nodes explicitly
    ignoreDeclaration: false, // Include the XML declaration (optional)
  })
  return parser.parse(xml)
}
export const findEntityPropertyByName = (
  parsedMetadata,
  propertyName,
  namespace = 'src.Models.FVH_SI_Table',
) => {
  if (!parsedMetadata) {
    return null
  }
  const entityType = parsedMetadata['edmx:Edmx']['edmx:DataServices']['Schema']
    .find((schema) => schema['@_Namespace'] === namespace)
    ?.['EntityType'].find((entity) => entity['@_Name'] === propertyName)

  return entityType
}

export const convertBase64ToXml = (base64Content) => {
  try {
    // Step 1: Decode the Base64 string

    const binaryString = atob(base64Content)

    // Step 2: Decode binary string as UTF-8
    const utf8Decoder = new TextDecoder('utf-8')
    const utf8Array = new Uint8Array([...binaryString].map((char) => char.charCodeAt(0)))
    const decodedString = utf8Decoder.decode(utf8Array)

    // Step 3: Remove BOM and trim the string
    const cleanedString = decodedString.replace(/^\uFEFF/, '').trim()

    // // Step 4: Log the cleaned string to verify
    // console.log('Cleaned XML String:', cleanedString)

    // // Step 5: Parse the XML content
    // const parser = new DOMParser()
    // const xmlDocument = parser.parseFromString(cleanedString, 'application/xml')

    // // Step 6: Handle parsing errors
    // const parseError = xmlDocument.getElementsByTagName('parsererror')
    // if (parseError.length > 0) {
    //   throw new Error('Error parsing XML: ' + parseError[0].textContent)
    // }

    // console.log('Successfully parsed XML:', xmlDocument)

    return cleanedString
  } catch (error) {
    console.error('Failed to convert Base64 to XML:', error)
    return null
  }
}

export const downloadXMLFile = (xmlContent, fileName) => {
  // Create a Blob object with the XML content
  const blob = new Blob([xmlContent], { type: 'application/xml' })

  // Create a download link
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = fileName

  // Programmatically click the link to trigger the download
  document.body.appendChild(link) // Append link to body
  link.click() // Trigger click
  document.body.removeChild(link) // Clean up
}

export const preventZoomWheel = (e) => {
  if (e.ctrlKey) e.preventDefault() //prevent zoom
}

export const preventZoomKey = (e) => {
  if (
    (e.ctrlKey == true || e.metaKey == true) &&
    (e.key == '-' || e.key == '=' || e.key == '+' || e.key == '-')
  ) {
    e.preventDefault()
  }
}

export const validateForm = async (form, formItems = [], ui) => {
  try {
    formItems.length === 0 ? await form.validateFields() : await form.validateFields(formItems)
    return true
  } catch (error) {
    const errorFields = form.getFieldsError()
    // console.log(errorFields)
    const newErrorFields = errorFields.filter((item) => item.errors.length > 0)

    ui.notiWarning('Thông tin chưa đầy đủ', newErrorFields[0].errors[0])

    return false
  }
}

export const displayFullNameWithSex = (fullName = '', sex) => {
  if (!fullName) {
    return ''
  }

  if (fullName.indexOf('. ') > -1) {
    return fullName
  }

  return `${sex === 'Male' ? 'Mr.' : 'Mrs.'} ${fullName}`
}

export const displayDateTimeFromGate = (stringDate) => {
  if (!stringDate) {
    return ''
  }

  return `${stringDate.slice(0, 4)}-${stringDate.slice(4, 6)}-${stringDate.slice(
    6,
    8,
  )} ${stringDate.slice(8, 10)}:${stringDate.slice(10, 12)}:${stringDate.slice(12, 14)}`
}

export const isUUID = (id) => {
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)
}

export function capitalizeWords(str = '') {
  if (!str) {
    return ''
  }

  return str.trim().charAt(0).toUpperCase() + str.slice(1)
}

export const setCellValueByName = (workbook, name, value) => {
  const namedRanges = workbook.definedNames.getRanges(name)
  if (!namedRanges || namedRanges.ranges?.length === 0) {
    throw new Error(`Defined name "${name}" not found`)
  }

  const [sheetName, cellAddress] = namedRanges.ranges[0].split('!')
  const worksheet = workbook.getWorksheet(sheetName)

  worksheet.getCell(cellAddress).value = value
}

export const handlePrintPDF = (title) => {
  const originalTitle = document.title
  document.title = title

  setTimeout(() => {
    window.print()
    document.title = originalTitle
  }, 500)
}
