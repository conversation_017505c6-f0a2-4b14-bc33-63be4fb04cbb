// Print margin variable
$print-margin: 20px;
$print-width: 1240px;

// #medical-record-form-print {
//   width: 1240px;
// }

@media print {
  thead { 
    display: table-header-group; 
  }
  tfoot {
    display: table-footer-group;
  }

  @page {
    size: A4;
    margin: $print-margin; /* Set margin size, you can change this to your desired value */
  }

  body * {
    visibility: hidden;
    overflow: visible !important;
    position: static !important;
    height: 0px;
  }

  #medical-record-form-print,
  #medical-record-form-print * {
    visibility: visible;
    height: auto;
  }

  #medical-record-form-print .no-print {
    visibility: hidden;
  }

  #medical-record-form-print {
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100%;
    height: auto; /* full height trang in */
    margin: 0;
    padding: 0 ;
    overflow: visible;
    box-sizing: border-box;
  }

  /* Remove sticky headers */
  .sticky-top {
    display: none !important;
  }
}
