import React, { createContext, useContext, useState } from 'react'
import LoadingPage from './components/LoadingPage'
import { PropTypes } from 'prop-types'
import useNotification from 'antd/es/notification/useNotification'
import useMessage from 'antd/es/message/useMessage'
import useApp from 'antd/es/app/useApp'

const UIContext = createContext(null)

const UIProvider = ({ children }) => {
  const [loading, setLoading] = useState(false)
  const [api, contextHolder] = useNotification()
  const [messageApi, messageContextHolder] = useMessage()
  const app = useApp()

  const messageConfig = {
    style: { whiteSpace: 'pre-line' },
  }

  const notiInfo = (mess, desc) =>
    api.info({ message: mess, duration: 15, description: desc, ...messageConfig })
  const notiError = (mess, desc) =>
    api.error({ message: mess, duration: 15, description: desc, ...messageConfig })
  const notiWarning = (mess, desc) =>
    api.warning({
      message: mess,
      duration: 15,
      description: desc,
      ...messageConfig,
    })
  const notiSuccess = (mess, desc) =>
    api.success({ message: mess, duration: 15, description: desc, ...messageConfig })

  const message = {
    success: (mess) => messageApi.success(mess),
    error: (mess) => messageApi.error(mess),
    warning: (mess) => messageApi.warning(mess),
    info: (mess) => messageApi.info(mess),
    loading: (mess) => messageApi.loading(mess),
  }

  const showConfirm = ({ title, content, ...confirmOptions }) => {
    return new Promise((resolve) => {
      app.modal.confirm({
        title: title,
        content: content,
        ...confirmOptions,
        onOk() {
          resolve(true)
        },
        onCancel() {
          resolve(false)
        },
      })
    })
  }

  return (
    <UIContext.Provider
      value={{
        loading,
        setLoading,
        notiInfo,
        notiError,
        notiWarning,
        notiSuccess,
        message,
        showConfirm,
      }}>
      {contextHolder}
      {messageContextHolder}
      {children}
      <LoadingPage open={loading} />
    </UIContext.Provider>
  )
}

UIProvider.propTypes = {
  children: PropTypes.element.isRequired,
}

export function useUI() {
  const {
    loading,
    setLoading,
    notiInfo,
    notiError,
    notiWarning,
    notiSuccess,
    message,
    showConfirm,
  } = useContext(UIContext)

  return {
    loading,
    setLoading,
    notiInfo,
    notiError,
    notiWarning,
    notiSuccess,
    message,
    showConfirm,
  }
}
export default UIProvider
