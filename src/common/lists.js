const lists = {
  ae_logbook_view: {
    listName: 'ae_logbook_view',
  },
  access_type_ref: {
    listName: 'access_type_ref',
  },
  alcohol_test_ref: {
    listName: 'alcohol_test_ref',
  },
  ats_triage_category_ref: {
    listName: 'ats_triage_category_ref',
  },
  ae_report_diagnostics_ref: { listName: 'ae_report_diagnostics_ref' },
  app_ref: {
    listName: 'app_ref',
  },
  app_field_role_indicator_mapping: {
    listName: 'app_field_role_indicator_mapping',
  },
  app_field_user_group_permission: {
    listName: 'app_field_user_group_permission',
  },
  app_record: {
    listName: 'app_record',
  },
  app_record_status_ref: {
    listName: 'app_record_status_ref',
  },
  app_section: {
    listName: 'app_section_ref',
    listPrimaryKeyName: 'app_section_id',
  },
  app_record_detail: {
    listName: 'app_record_detail',
    listPrimaryKeyName: 'app_record_detail_id',
  },
  app_record_detail_change_log: {
    listName: 'app_record_detail_change_log',
    listPrimaryKeyName: 'app_record_detail_change_log_id',
  },
  app_record_detail_change_reason_ref: {
    listName: 'app_record_detail_change_reason_ref',
    listPrimaryKeyName: 'app_record_detail_change_reason_rcd',
  },
  api_ar_invoice_detail_view: {
    listName: 'api_ar_invoice_detail_view',
    listPrimaryKeyName: 'app_record_detail_change_reason_rcd',
  },
  api_charge_detail_view: {
    listName: 'api_charge_detail_view',
  },
  api_patient_visit_medical_coding_view: {
    listName: 'api_patient_visit_medical_coding_view',
  },
  patient_medical_coding_mapping: {
    listName: 'patient_medical_coding_mapping',
  },
  api_orion_patient_visit_view: {
    listName: 'api_orion_patient_visit_view',
  },
  api_patient_visit_policy_ss_view: {
    listName: 'api_patient_visit_policy_ss_view',
  },
  api_policy_subscription_ss_view: {
    listName: 'api_policy_subscription_ss_view',
  },
  base_salary_45_months: {
    listName: 'base_salary_45_months',
  },
  card_type_ref: {
    listName: 'card_type_ref',
  },
  databank_field: {
    listName: 'databank_field_ref',
  },
  databank_field_type_ref: {
    listName: 'databank_field_type_ref',
  },
  databank_field_app_mapping: {
    listName: 'databank_field_app_mapping',
    listPrimaryKeyName: 'app_section_id',
  },

  employee_dataset: { listName: 'employeedataset' },
  ethnicity_ref: {
    listName: 'ethnicity_ref',
  },
  Accounts: {
    listName: 'Accounts',
  },
  field: {
    listName: 'field',
  },
  field_type_ref: {
    listName: 'field_type_ref',
  },
  health_insurance_card: {
    listName: 'health_insurance_card',
    primaryKeyName: 'health_insurance_card_id',
  },
  logbook: {
    listName: 'logbook',
  },
  logbook_type: {
    listName: 'logbook_type',
  },
  logbook_type_field: {
    listName: 'logbook_type_field',
  },
  logbook_field_value: {
    listName: 'logbook_field_value',
  },
  occupation_ref: {
    listName: 'occupation_ref',
  },
  patient_dataset: { listName: 'patientdataset' },
  patient_status_ref: {
    listName: 'patient_status_ref',
  },
  patient_visit: { listName: 'patient_visit' },
  patient_visit_mapping_view: { listName: 'patient_visit_mapping_view' },
  person_indicator_dataset: { listName: 'PersonIndicatorDataset' },
  prescription_diagnosis: { listName: 'prescription_diagnosis' },
  role_indicator_ref: {
    listName: 'role_indicator_ref',
  },
  region_nl_view: {
    listName: 'region_nl_view',
  },
  StoreRecords: {
    listName: 'StoreRecords',
  },
  ss_table_0: {
    listName: 'ss_table_0',
    xmlType: 'XML0',
    detailPageUrlParam: 'invoice_no_',
    primaryKeyName: 'table_0_id',

    tableName: 'ssTable0',
  },
  serious_illness: {
    listName: 'serious_illness',
  },
  ss_table_1: {
    listName: 'ss_table_1',
    primaryKeyName: 'table_1_id',
    xmlType: 'XML1',
    detailPageUrlParam: 'invoice_no_',
    displayName: 'Bảng 1',
  },
  ss_table_1_nl_view: {
    listName: 'ss_table_1_nl_view',
    primaryKeyName: 'table_1_id',
    xmlType: 'XML1',
    detailPageUrlParam: 'invoice_no_',
    displayName: 'Bảng 1',
  },
  ss_table_2_nl_view: {
    listName: 'ss_table_2_nl_view',
    primaryKeyName: 'table_2_id',
    xmlType: 'XML2',
    detailPageUrlParam: 'invoice_no_',
    displayName: 'Bảng 2',
  },
  ss_table_3_nl_view: {
    listName: 'ss_table_3_nl_view',
    primaryKeyName: 'table_3_id',
    xmlType: 'XML3',
    detailPageUrlParam: 'invoice_no_',
    displayName: 'Bảng 3',
  },
  ss_table_4_nl_view: {
    listName: 'ss_table_4_nl_view',
    primaryKeyName: 'table_4_id',
    xmlType: 'XML4',
    detailPageUrlParam: 'invoice_no_',
    displayName: 'Bảng 4',
  },
  ss_table_5_nl_view: {
    listName: 'ss_table_5_nl_view',
    primaryKeyName: 'table_5_id',
    xmlType: 'XML5',
    detailPageUrlParam: 'invoice_no_',
    displayName: 'Bảng 5',
  },
  ss_table_7_nl_view: {
    listName: 'ss_table_7_nl_view',
    primaryKeyName: 'table_7_id',
    xmlType: 'XML7',
    detailPageUrlParam: 'invoice_no_',
    displayName: 'Bảng 7',
  },
  ss_table_2: {
    listName: 'ss_table_2',
    xmlType: 'XML2',
    detailPageUrlParam: 'invoice_no_',
    primaryKeyName: 'table_2_id',

    displayName: 'Bảng 2',
  },

  ss_table_3: {
    listName: 'ss_table_3',
    xmlType: 'XML3',
    detailPageUrlParam: 'invoice_no_',
    primaryKeyName: 'table_3_id',

    displayName: 'Bảng 3',
  },

  ss_table_4: {
    listName: 'ss_table_4',
    xmlType: 'XML4',
    detailPageUrlParam: 'invoice_no_',
    primaryKeyName: 'table_4_id',

    displayName: 'Bảng 4',
  },

  ss_table_5: {
    listName: 'ss_table_5',
    xmlType: 'XML5',
    detailPageUrlParam: 'invoice_no_',
    primaryKeyName: 'table_5_id',

    displayName: 'Bảng 5',
  },

  ss_table_7: {
    listName: 'ss_table_7',
    xmlType: 'XML7',
    detailPageUrlParam: 'invoice_no_',
    primaryKeyName: 'table_7_id',

    displayName: 'Bảng 7',
  },

  ss_table_8: {
    listName: 'ss_table_8',
    xmlType: 'XML8',
    detailPageUrlParam: 'invoice_no_',
    primaryKeyName: 'table_8_id',

    displayName: 'Bảng 8',
  },
  ss_table_12: {
    listName: 'ss_table_12',
    xmlType: 'XML12',
    detailPageUrlParam: 'invoice_no_',
    primaryKeyName: 'table_12_id',

    displayName: 'Bảng 12',
  },
  ss_table_13: {
    listName: 'ss_table_13',
    xmlType: 'XML13',
    detailPageUrlParam: 'invoice_no_',
    primaryKeyName: 'table_13_id',

    displayName: 'Bảng 13',
  },
  ss_table_14: {
    listName: 'ss_table_14',
    xmlType: 'XML14',
    detailPageUrlParam: 'invoice_no_',
    primaryKeyName: 'table_14_id',

    displayName: 'Bảng 14',
  },
  subregion_nl_view: {
    listName: 'subregion_nl_view',
  },
  system_setting_table_columns: {
    listName: 'system_setting_table_columns',
  },
  ss_technical_services_view: {
    listName: 'ss_technical_services_view',
  },
  system_referral_type_ref: {
    listName: 'system_referral_type_ref',
  },
  system_referral_disposition_ref_nl_view: {
    listName: 'system_referral_disposition_ref_nl_view',
  },
  tbl_file_tracking: {
    listName: 'tbl_file_tracking',
  },
  tbl_SS_policy_short_code: {
    listName: 'tbl_SS_policy_short_code',
  },
  technical_services: {
    listName: 'technical_services',
    primaryKeyName: 'technical_services_id',
  },
  technical_services_detail: {
    listName: 'technical_services_detail',
    primaryKeyName: 'technical_services_detail_id',
  },
  ss_item_technical_services_mapping: {
    listName: 'ss_item_technical_services_mapping',
    primaryKeyName: 'ss_item_technical_services_mapping_id',
  },
  user_group_ref: { listName: 'user_group_ref' },
  user_group_membership: { listName: 'user_group_membership' },
  permission_ref: { listName: 'permission_ref' },
  user_group_permission_mapping: { listName: 'user_group_permission_mapping' },

  visit_dataset: { listName: 'visitdataset' },
  visit_charge_detail: {
    listName: 'visit_charge_detail',
    primaryKeyName: 'visit_charge_detail_id',
  },
  visit_charge_detail_view: { listName: 'visit_charge_detail_view' },
  visit_type_ref: { listName: 'visit_type_ref' },
  ward_subregion_nl_view: { listName: 'ward_subregion_nl_view' },
  table_xml_tracking: {
    listName: 'table_xml_tracking',
  },
  employee_ss_info: {
    listName: 'employee_ss_info',
  },
  medical_condition: {
    listName: 'medical_condition',
  },
  patient_visit_history: {
    listName: 'patient_visit_history',
    primaryKeyName: 'patient_visit_history_id',
  },
  medical_treatment_type_ref: {
    listName: 'medical_treatment_type_ref',
  },
  medical_treatment_invoice_mapping: {
    listName: 'medical_treatment_invoice_mapping',
  },
  visit_type_ref_nl_view: {
    listName: 'visit_type_ref_nl_view',
  },
  notification_subscription: {
    listName: 'notification_subscription',
  },
  medical_record_form: {
    listName: 'medical_record_form',
  },
  medical_supplies_his_mapping: {
    listName: 'medical_supplies_his_mapping',
    primaryKeyName: 'medical_supplies_his_mapping_id',
  },
  medicine_his_mapping: {
    listName: 'medicine_his_mapping',
    primaryKeyName: 'medicine_his_mapping_id',
  },
  technical_services_his_mapping: {
    listName: 'technical_services_his_mapping',
    primaryKeyName: 'technical_services_his_mapping_id',
  },
  medicine: {
    listName: 'medicine',
    primaryKeyName: 'medicine_id',
  },
  medical_supplies: {
    listName: 'medical_supplies',
    primaryKeyName: 'medical_supplies_id',
  },
  api_item_view: {
    listName: 'api_item_view',
    primaryKeyName: 'item_id',
  },
  medicine_detail: {
    listName: 'medicine_detail',
    primaryKeyName: 'medicine_detail_id',
  },
  medical_supplies_detail: {
    listName: 'medical_supplies_detail',
    primaryKeyName: 'medical_supplies_detail_id',
  },
  rule_ref: {
    listName: 'rule_ref',
  },
  rule_visit_item_mapping: {
    listName: 'rule_visit_item_mapping',
  },
  rule_visit_item_detail_nl_view: {
    listName: 'rule_visit_item_detail_nl_view',
  },
  rule_visit_item_detail_note_nl_view: {
    listName: 'rule_visit_item_detail_note_nl_view',
  },
  rule_group_ref: {
    listName: 'rule_group_ref',
  },
  rule_visit_item_detail: {
    listName: 'rule_visit_item_detail',
  },
  rule_visit_item_detail_note: {
    listName: 'rule_visit_item_detail_note',
  },
  chemo_prescription_nl_view: {
    listName: 'chemo_prescription_nl_view',
    primaryKeyName: 'chemo_prescription_nl_view_id',
  },
  chemo_prescription_detail_nl_view: {
    listName: 'chemo_prescription_detail_nl_view',
    primaryKeyName: 'chemo_prescription_detail_nl_view_id',
  },
  general_prescription_nl_view: {
    listName: 'general_prescription_nl_view',
    primaryKeyName: 'general_prescription_nl_view_id',
  },
  general_prescription_detail_nl_view: {
    listName: 'general_prescription_detail_nl_view',
    primaryKeyName: 'general_prescription_detail_nl_view_id',
  },
  department_mapping_si: {
    listName: 'department_mapping_si',
    primaryKeyName: 'department_id',
  },
   patient_visit_note: {
    listName: 'patient_visit_note',
  },
}

export default lists
