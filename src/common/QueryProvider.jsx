import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import PropTypes from 'prop-types'
import config from './config'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // refetchOnWindowFocus: config.IN_TEST ? false : true, // turn off refetch in test mode
      refetchOnWindowFocus: false, // turn off by default, turn on when needed in specific query
      // retry: config.IN_TEST ? 1 : 3,
    },
  },
})

const QueryProvider = ({ children }) => {
  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
}

QueryProvider.propTypes = {
  children: PropTypes.node.isRequired,
}

export default QueryProvider
