import React from 'react'
import { Image, Popover, Button, Tag, Divider, Checkbox } from 'antd'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faSignOutAlt } from '@fortawesome/free-solid-svg-icons'
import PropTypes from 'prop-types'
import { useDispatch, useSelector } from 'react-redux'
import { useAuth } from '../AuthProvider'
import { authActions, MODULE_AUTH } from '../../store/auth'
import imgLogo2 from '../../assets/logoFV-withtext.png'
import imgAvatar from '../../assets/avatar.png'

import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons'
import config from '../config'
import NotificationButton from './NotificationButton'
import useApp from 'antd/es/app/useApp'
import { clearProcessingPersonForUser } from '../../components/Visit/VisitService'
import AsyncButton from './AsyncButton'
import { PERMISSION } from '../../components/Auth/AuthConstant'
import { MODE_VIEW_DATA } from '../constant'
import { useSearchParams } from 'react-router-dom'
import { getUserGroupById, getUserGroupMembershipsByUserId } from '../../services/permissionService'

const propTypes = {
  collapsed: PropTypes.bool,
  setCollapsed: PropTypes.func,
}

const CustomHeader = ({ collapsed, setCollapsed }) => {
  const auth = useAuth()
  const app = useApp()
  const dispatch = useDispatch()
  const [searchParams] = useSearchParams()

  const { currentUser, permission } = useSelector((state) => state[MODULE_AUTH])

  const handleShowUserGroup = async () => {
    let memberships = await getUserGroupMembershipsByUserId(currentUser.User_id)
    memberships = memberships.value || []

    let groups = await Promise.all(
      memberships.map(async (m) => {
        let group = await getUserGroupById(m.user_group_id)
        return group
      }),
    )
    console.log(' groups:', groups)

    app.notification.info({
      message: (
        <div>
          {groups.map((g) => (
            <div className="mb-1" key={g.user_group_id}>
              <Tag>{g.name_e}</Tag>
            </div>
          ))}
        </div>
      ),
    })
  }

  const content = (
    <div>
      <div>
        <Button
          type="text"
          block
          className="justify-content-start"
          icon={<i className="fas fa-info-circle" />}
          onClick={() => {
            app.modal.info({
              title: 'Check permission',
              content: (
                <div>
                  <div>
                    <Button onClick={handleShowUserGroup}>Check group</Button>
                  </div>
                  <div>user_id: {currentUser?.User_id}</div>
                  <div>Permission:</div>
                  {permission.map((p) => (
                    <div className="mb-1" key={p.permission_rcd}>
                      <Tag>{p.permission_rcd}</Tag>
                    </div>
                  ))}
                </div>
              ),
            })
          }}>
          Check permission
        </Button>
      </div>
      <div>
        <AsyncButton
          className="justify-content-start"
          block
          type="text"
          onClick={async () => {
            try {
              // Clear processing person information for the current user
              if (currentUser?.User_id) {
                await clearProcessingPersonForUser(currentUser.User_id)
              }
            } catch (error) {
              console.error('Error clearing processing person during logout:', error)
            }
            // Continue with normal logout
            auth.logout()
          }}>
          <FontAwesomeIcon icon={faSignOutAlt} className="mr-1" /> {/* Thêm icon Logout */}
          Logout
        </AsyncButton>
      </div>
    </div>
  )

  return (
    <div className="row align-items-center bg-linear-to-r from-[#F8F6DC] to-[#BAD5D5] px-1 ">
      <Button
        size="large"
        type="text"
        icon={
          collapsed ? (
            <MenuUnfoldOutlined style={{ fontSize: '16px' }} />
          ) : (
            <MenuFoldOutlined style={{ fontSize: '16px' }} />
          )
        }
        onClick={() => setCollapsed(!collapsed)}
        style={{
          fontSize: '16px',
          width: 64,
          height: 33,
        }}
      />

      <div className="col flex items-center">
        <Image height={27} preview={false} src={imgLogo2} alt="Logo" />
        <div>
          <Tag className="fw-bold" color="green">
            LAST VERSION: {config.LAST_VERSION}
          </Tag>
        </div>
        <div>
          {searchParams.get('modeViewData') === MODE_VIEW_DATA.NORMAL ? null : (
            <Tag className="fw-bold" color="">
              {searchParams.get('modeViewData')}
            </Tag>
          )}
        </div>
      </div>

      <div className="col flex justify-end gap-2 items-center">
        {auth?.checkPermission(PERMISSION.SUPER_ADMIN) && (
          <Checkbox onClick={(e) => dispatch(authActions.setIsDebugMode(e.target.checked))}>
            Debug mode
          </Checkbox>
        )}
        <NotificationButton />
        <div className="flex items-center">
          <div className="mr-2">{currentUser.Employee_name}</div>
          <span className="text-lg cursor-pointer">
            <Popover content={content} trigger="click">
              <div className="flex items-center p-1 justify-center w-10 rounded-full border border-gray-300 cursor-pointer hover:bg-gray-200">
                <img src={imgAvatar} className="text-lg" />
              </div>
            </Popover>
          </span>
        </div>
      </div>
    </div>
  )
}
CustomHeader.propTypes = propTypes
export default CustomHeader
