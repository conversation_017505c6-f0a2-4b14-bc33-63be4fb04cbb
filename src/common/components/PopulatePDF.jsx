import React, { useState, useCallback, useEffect } from 'react'
import { PDFDocument } from 'pdf-lib'
import { Card, Select, Space, Row, Col, App } from 'antd'
import { handleError } from '../helpers'
import { uploadFileToDocLibService } from '../services'
import PdfViewer from './PdfViewer'
import DroppablePdfViewer from './DroppablePdfViewer'
import AsyncButton from './AsyncButton'
import { v4 } from 'uuid'
import config from '../config'
import * as fontkit from 'fontkit'
import DragDropFieldMapper from './DragDropFieldMapper'
/**
 * Dynamic PDF Form Filling Component
 *
 * @param {Object} props - Component props
 * @param {Array} props.templates - Array of PDF template configurations
 * @param {string} props.storeID - Store ID for DocumentStore
 * @param {string} props.dataSource - Data source for DocumentStore
 * @param {Function} props.onSaveSuccess - Callback when PDF is saved successfully
 * @param {Object} props.savedFieldValues - Previously saved field values from note column
 * @param {Object} props.availableFields - Available data fields for drag-and-drop mapping
 * @param {Object} props.defaultFieldPositions - Default field positions for templates
 */
const PopulatePDF = ({
  templates = [],
  storeID,
  dataSource,
  onSaveSuccess,
  savedFieldValues = {},
  availableFields = {},
  defaultFieldPositions = {},
}) => {
  const { message } = App.useApp()

  // State management
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [pdfFile, setPdfFile] = useState(null)
  const [previewPdf, setPreviewPdf] = useState(null)
  const [loading, setLoading] = useState(false)
  const [fieldPositions, setFieldPositions] = useState([]) // Array of field positions with x,y,page,fieldKey

  /**
   * Load saved field positions from savedFieldValues
   */
  useEffect(() => {
    if (savedFieldValues.fieldPositions) {
      setFieldPositions(savedFieldValues.fieldPositions)
    }
  }, [savedFieldValues])

  /**
   * Handle field position changes
   */
  const handleFieldPositionsChange = useCallback((newPositions) => {
    setFieldPositions(newPositions)
  }, [])

  /**
   * Generate PDF with positioned fields
   */
  const generatePDFWithPositions = useCallback(
    async (positions) => {
      if (!pdfFile) {
        throw new Error('No PDF file loaded')
      }

      try {
        let pdfBytes
        if (pdfFile instanceof File || pdfFile instanceof Blob) {
          pdfBytes = await pdfFile.arrayBuffer()
        } else {
          throw new Error('Invalid PDF file format')
        }

        const pdfDoc = await PDFDocument.load(pdfBytes)
        pdfDoc.registerFontkit(fontkit)

        // Load custom font for Vietnamese support
        let customFont = null
        try {
          const fontResponse = await fetch(config.HOME_PAGE + '/webfonts/FV_DIN_Light.otf')
          if (fontResponse.ok) {
            const fontBytes = await fontResponse.arrayBuffer()
            customFont = await pdfDoc.embedFont(fontBytes)
          }
        } catch (error) {
          handleError(error, 'loadCustomFont')
        }

        if (!customFont) {
          throw new Error('Failed to load custom font for Vietnamese text support')
        }

        const pages = pdfDoc.getPages()

        // Add positioned fields to PDF
        positions.forEach((position) => {
          const page = pages[position.page - 1] // Convert to 0-based index
          if (page) {
            const { height } = page.getSize()
            // Convert coordinates (PDF coordinate system has origin at bottom-left)
            // Account for the transform translate(-50%, -50%) used in the preview
            const pdfY = height - position.y

            // Draw text with consistent positioning
            page.drawText(String(position.value || ''), {
              x: position.x,
              y: pdfY,
              size: 12, // Match preview font size
              font: customFont,
              maxWidth: 200,
            })
          }
        })

        return await pdfDoc.save()
      } catch (error) {
        handleError(error, 'generatePDFWithPositions')
        throw error
      }
    },
    [pdfFile],
  )

  /**
   * Load PDF template from URL
   */
  const loadTemplate = useCallback(
    async (templateUrl) => {
      try {
        setLoading(true)
        const response = await fetch(templateUrl)
        if (!response.ok) {
          throw new Error('Failed to fetch PDF template')
        }
        const blob = await response.blob()
        setPdfFile(blob)
      } catch (error) {
        handleError(error, 'loadTemplate')
        message.error('Không thể tải template PDF')
      } finally {
        setLoading(false)
      }
    },
    [message],
  )

  /**
   * Handle template selection
   */
  const handleTemplateChange = (templateId) => {
    const template = templates.find((t) => t.id === templateId)
    if (template) {
      setSelectedTemplate(template)
      loadTemplate(template.url)
    }
  }

  /**
   * Generate preview PDF with positioned fields
   */
  const handlePreview = useCallback(
    async (positions = fieldPositions) => {
      if (!pdfFile) {
        message.error('Chưa có template PDF')
        return
      }

      try {
        setLoading(true)
        const filledPdfBytes = await generatePDFWithPositions(positions)

        // Convert PDF bytes to Blob for PdfViewer
        const pdfBlob = new Blob([filledPdfBytes], { type: 'application/pdf' })
        setPreviewPdf(pdfBlob)

        message.success('Tạo preview thành công')
      } catch (error) {
        handleError(error, 'handlePreview')
        message.error('Không thể tạo preview PDF')
      } finally {
        setLoading(false)
      }
    },
    [pdfFile, fieldPositions, generatePDFWithPositions, message],
  )

  /**
   * Save filled PDF to DocumentStore
   */
  const handleSaveAttachment = useCallback(async () => {
    if (!pdfFile || !storeID || !dataSource) {
      message.error('Thiếu thông tin cần thiết để lưu file')
      return
    }

    try {
      setLoading(true)

      // Generate filled PDF with positioned fields
      const filledPdfBytes = await generatePDFWithPositions(fieldPositions)
      const fileName = `${selectedTemplate?.name || 'filled-form'}_${new Date().getTime()}.pdf`

      // Create file blob
      const file = new Blob([filledPdfBytes], { type: 'application/pdf' })

      // Upload to DocumentStore
      const uniqueFileName = `${v4()}_${fileName}`
      await uploadFileToDocLibService('/DocumentStore', uniqueFileName, storeID, dataSource, file)

      // Save field positions to note column (will be handled by parent component)
      if (onSaveSuccess) {
        onSaveSuccess({
          fieldPositions: fieldPositions,
          templateId: selectedTemplate?.id,
          fileName: uniqueFileName,
        })
      }

      message.success('Lưu file thành công')
    } catch (error) {
      handleError(error, 'handleSaveAttachment')
      message.error('Không thể lưu file')
    } finally {
      setLoading(false)
    }
  }, [
    pdfFile,
    storeID,
    dataSource,
    selectedTemplate,
    fieldPositions,
    generatePDFWithPositions,
    onSaveSuccess,
    message,
  ])

  // If no templates configured, return null (fallback to upload functionality)
  if (!templates || templates.length === 0) {
    return null
  }

  // Main drag & drop interface
  const dragDropInterface = (
    <Row gutter={[16, 16]}>
      <Col span={8}>
        <DragDropFieldMapper
          availableFields={availableFields}
          fieldPositions={fieldPositions}
          onFieldPositionsChange={handleFieldPositionsChange}
          onPreview={handlePreview}
          defaultFieldPositions={defaultFieldPositions[selectedTemplate?.id] || {}}
          templateId={selectedTemplate?.id || ''}
        />
      </Col>
      <Col span={16}>
        {selectedTemplate && (
          <DroppablePdfViewer
            fileContent={pdfFile}
            fileName={selectedTemplate?.name}
            fieldPositions={fieldPositions}
            onFieldPositionsChange={handleFieldPositionsChange}
            enableDrop={true}
          />
        )}
      </Col>
    </Row>
  )

  return (
    <div>
      <Card title="Điền thông tin PDF Template" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* Template Selection */}
          <div>
            <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
              Chọn Template:
            </label>
            <Select
              placeholder="Chọn template PDF"
              onChange={handleTemplateChange}
              value={selectedTemplate?.id}
              style={{ width: '100%' }}>
              {templates.map((template) => (
                <Select.Option key={template.id} value={template.id}>
                  {template.name}
                </Select.Option>
              ))}
            </Select>
          </div>

          {/* Action Buttons */}
          {selectedTemplate && (
            <Space>
              <AsyncButton
                type="primary"
                onClick={handlePreview}
                loading={loading}
                icon={<i className="fa fa-eye" />}>
                Preview
              </AsyncButton>
              <AsyncButton
                type="primary"
                onClick={handleSaveAttachment}
                loading={loading}
                icon={<i className="fa fa-save" />}>
                Lưu vào DocumentStore
              </AsyncButton>
            </Space>
          )}
        </Space>
      </Card>

      {/* Drag & Drop Interface */}
      {selectedTemplate && (
        <Card title="Kéo thả trường vào PDF" style={{ marginBottom: 16 }}>
          {dragDropInterface}
        </Card>
      )}

      {/* PDF Preview */}
      {previewPdf && (
        <Card title="Preview PDF">
          <PdfViewer fileContent={previewPdf} fileName={selectedTemplate?.name} />
        </Card>
      )}
    </div>
  )
}

export default PopulatePDF
