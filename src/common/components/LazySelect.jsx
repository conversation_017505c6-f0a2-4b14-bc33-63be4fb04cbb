import React, { useState, useEffect, useMemo, useRef } from 'react'
import { Select, Spin } from 'antd'
import PropTypes from '../PropTypes'
import { getItemsService } from '../services'
import debounce from 'lodash/debounce'

const propTypes = {
  list: PropTypes.object.isRequired,
  filter: PropTypes.string,
  orderBy: PropTypes.string,
  select: PropTypes.string,
  expand: PropTypes.string,
  itemsPerPage: PropTypes.number,
  searchFields: PropTypes.array,
  keyProp: PropTypes.string.isRequired,
  isKeyPropString: PropTypes.bool,
  setSelectedUser: PropTypes.func, // Thêm prop setSelected
  renderOption: PropTypes.func.isRequired,
  defaultSelected: PropTypes.object, // Thêm prop defaultSelected
  resetOnSelect: PropTypes.bool,
  excludedKeys: PropTypes.array,
}

const LazySelect = ({
  list,
  filter = '',
  orderBy = '',
  select = '',
  expand = '',
  itemsPerPage = 20,
  searchFields = [],
  keyProp,
  isKeyPropString = false,
  setSelectedUser = () => {}, // Thêm hàm set giá trị được chọn
  renderOption,
  defaultSelected = null, // Giá trị mặc định
  resetOnSelect = false,
  excludedKeys = [],
  ...rest
}) => {
  /* [15-05-25] Sửa lại vì: Khi items trống, Select không tìm thấy <Option> tương ứng với value 
  (ví dụ "1342") nên sẽ hiển thị thẳng value mà không show phần children (mã – tên) tại renderOption.
  Khi items chứa defaultSelected, Select tìm ra <Option> khớp và hiển thị children (mã – tên) 
  tại renderOption.*/
  const [items, setItems] = useState(defaultSelected ? [defaultSelected] : [])
  const [loading, setLoading] = useState(false)
  const [skip, setSkip] = useState(0)
  const [hasMore, setHasMore] = useState(true)
  const [searchValue, setSearchValue] = useState('')
  const [selectedItem, setSelectedItem] = useState(defaultSelected) // Quản lý giá trị được chọn
  const [dropdownOpen, setDropdownOpen] = useState(false) // Cờ để xác định xem dropdown có đang mở hay không

  const fetchData = async (isLoadMore = false) => {
    try {
      setLoading(true)

      let conditions = []
      if (filter) {
        conditions.push(filter)
      }
      if (searchValue && searchFields.length > 0) {
        conditions.push(
          `(${searchFields.map((field) => `contains(${field},'${searchValue}')`).join(' or ')})`,
        )
      }

      let response = {
        value: [],
        '@odata.count': 0,
      }

      response = await getItemsService(list, {
        filter: conditions.join(' and '),
        orderBy,
        select,
        expand,
        top: itemsPerPage,
        skip: isLoadMore ? items.length : skip,
      })

      const newItems = response?.value || []
      const total = response?.['@odata.count'] || 0
      const nextSkip = skip + itemsPerPage

      // get defaultSelect
      if (defaultSelected) {
        if (!newItems.find((item) => item[keyProp] === defaultSelected?.[keyProp])) {
          const defaultItem = await getItemsService(list, {
            filter: `${keyProp} eq ${isKeyPropString ? `'${defaultSelected?.[keyProp]}'` : defaultSelected?.[keyProp]}`,
          }).then((res) => res.value[0])

          newItems.push(defaultItem)
        }

        const newSelectedItem = newItems.find(
          (item) => item[keyProp] === defaultSelected?.[keyProp],
        )

        setSelectedItem(newSelectedItem)
      }

      if (isLoadMore) {
        setItems((prev) => [...prev, ...newItems])
      } else {
        setItems(newItems)
      }

      setSkip((prev) => prev + itemsPerPage)
      setHasMore(total > nextSkip)
    } finally {
      setLoading(false)
    }
  }

  const handleScroll = (e) => {
    try {
      const { target } = e
      const isBottom = target.scrollTop + target.offsetHeight >= target.scrollHeight - 10
      if (!loading && hasMore && isBottom) {
        fetchData(true)
      }
    } catch (error) {
      //
    }
  }

  const debounceSearch = useMemo(
    () =>
      debounce((value) => {
        setSearchValue(value)
        setSkip(0)
        setHasMore(true)
      }, 400),
    [],
  )

  const handleSearch = (value) => {
    if (dropdownOpen) {
      setSearchValue(value)
      setSkip(0)
      setHasMore(true)
      debounceSearch(value)
    }
  }

  const handleSelect = (_, option) => {
    if (!option?.item) {
      return
    }

    setSelectedUser(option.item)

    if (resetOnSelect) {
      handleOnClear()
    } else {
      setSelectedItem(option.item)
    }
  }

  const handleOnClear = () => {
    setSelectedItem(null)
    setSelectedUser(null)
    setSearchValue('')
    // setItems([])
  }

  useEffect(() => {
    fetchData()
  }, [list, filter, orderBy, select, expand, searchValue])

  return (
    <Select
      {...rest}
      value={selectedItem?.[keyProp]}
      showSearch
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleSelect}
      onClear={handleOnClear}
      autoClearSearchValue={true}
      allowClear
      onDropdownVisibleChange={(open) => {
        setDropdownOpen(open)
      }}
      onPopupScroll={handleScroll}
      loading={loading}
      virtual={false}
      notFoundContent={
        loading ? <Spin size="small" /> : searchValue ? 'Không có dữ liệu' : 'Vui lòng nhập từ khóa'
      }
      dropdownRender={(menu) => (
        <>
          {menu}
          {loading && (
            <div style={{ textAlign: 'center', padding: '8px' }}>
              <Spin size="small" />
            </div>
          )}
        </>
      )}>
      {items
        .filter((item) => !excludedKeys.includes(item[keyProp]))
        .map((item, index) => renderOption(item, index))}
    </Select>
  )
}

LazySelect.propTypes = propTypes
export default LazySelect
