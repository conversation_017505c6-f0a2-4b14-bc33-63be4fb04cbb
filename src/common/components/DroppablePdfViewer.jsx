import React, { useEffect, useState, useRef, useCallback } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import { Button, Select, App } from 'antd'
import { downloadBase64File, fileToBase64, handleError } from '../helpers'
import { getFileService } from '../services'
import { useUI } from '../UIProvider'
import { preventZoomKey, preventZoomWheel } from '../../SI/helper'

import 'react-pdf/dist/Page/AnnotationLayer.css'
import 'react-pdf/dist/Page/TextLayer.css'

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url,
).toString()

/**
 * Droppable PDF Viewer Component
 * Extends PdfViewer with drag-and-drop functionality for field positioning
 *
 * @param {Object} props - Component props
 * @param {string} props.serverRelativeUrl - Server URL for PDF file
 * @param {string} props.fileName - File name for download
 * @param {Blob} props.fileContent - PDF file content as blob
 * @param {string} props.width - Viewer width
 * @param {Array} props.fieldPositions - Array of positioned fields
 * @param {Function} props.onFieldPositionsChange - Callback when field positions change
 * @param {boolean} props.enableDrop - Enable drop functionality
 */
const DroppablePdfViewer = ({
  serverRelativeUrl,
  fileName = 'download.pdf',
  fileContent,
  width = '100%',
  fieldPositions = [],
  onFieldPositionsChange,
  enableDrop = false,
}) => {
  const { message } = App.useApp()
  const ui = useUI()
  const [selectedFile, setSelectedFile] = useState()
  const [pageNumbers, setPageNumbers] = useState([])
  const [scale, setScale] = useState(1.5)
  const [isAllPageLoaded, setIsAllPageLoaded] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const [draggingField, setDraggingField] = useState(null)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [showCoordinates, setShowCoordinates] = useState(false)
  const [hoveredField, setHoveredField] = useState(null)
  const documentRef = useRef(null)
  const documentContainerRef = useRef(null)
  const pageRefs = useRef({})

  const getFileContent = async () => {
    try {
      let fileContent = await getFileService(serverRelativeUrl)
      setSelectedFile(await fileToBase64(fileContent))
    } catch (error) {
      handleError(error, 'getFileContent')
    }
  }

  // case serverRelativeUrl
  useEffect(() => {
    if (!serverRelativeUrl) {
      return
    }
    getFileContent()
  }, [serverRelativeUrl])

  // case fileContent
  useEffect(() => {
    if (!fileContent) {
      return
    }
    const initFileContent = async () => {
      setSelectedFile(await fileToBase64(fileContent))
    }
    initFileContent()
  }, [fileContent])

  // prevent zoom event
  useEffect(() => {
    document.body.onwheel = preventZoomWheel
    document.body.onkeydown = preventZoomKey
  }, [])

  function onDocumentLoadSuccess(args) {
    const arr = []
    for (let i = 1; i <= args.numPages; i++) {
      arr.push(i)
    }
    setPageNumbers(arr)
  }

  const onPageRenderSuccess = (page) => {
    if (page.pageNumber === pageNumbers.length) {
      setIsAllPageLoaded(true)
    }
  }

  // Handle drag over
  const handleDragOver = useCallback(
    (e) => {
      if (!enableDrop) return
      e.preventDefault()
      e.dataTransfer.dropEffect = draggingField ? 'move' : 'copy'
      setDragOver(true)

      // Update mouse position for coordinate display - calculate relative to individual PDF page
      const pageElement = e.target.closest('.react-pdf__Page')
      if (pageElement) {
        // Try to get the canvas element for more accurate positioning
        const canvasElement = pageElement.querySelector('canvas')
        const targetElement = canvasElement || pageElement
        const pageRect = targetElement.getBoundingClientRect()

        // Calculate position relative to this specific page (not container)
        const relativeX = e.clientX - pageRect.left
        const relativeY = e.clientY - pageRect.top

        // Convert to PDF coordinates for this page
        const pdfX = relativeX / scale
        const pdfY = relativeY / scale

        // Find page number by looking at the parent div with data-page-number or by position
        let pageNumber = 1
        const pageContainer = pageElement.closest('[data-page-number]')
        if (pageContainer) {
          pageNumber = parseInt(pageContainer.getAttribute('data-page-number'))
        } else {
          // Fallback: find page number by checking which page container this element belongs to
          const allPageContainers =
            documentContainerRef.current?.querySelectorAll('[data-page-number]')
          if (allPageContainers) {
            for (let i = 0; i < allPageContainers.length; i++) {
              if (allPageContainers[i].contains(pageElement)) {
                pageNumber = parseInt(allPageContainers[i].getAttribute('data-page-number'))
                break
              }
            }
          }
        }

        setMousePosition({
          x: e.clientX,
          y: e.clientY,
          pdfX: Math.round(Math.max(0, pdfX)), // Ensure non-negative coordinates
          pdfY: Math.round(Math.max(0, pdfY)), // Ensure non-negative coordinates
          page: pageNumber,
        })

        // Check if hovering over an existing field (only when dragging)
        if (draggingField) {
          const hoveredField = fieldPositions.find((pos) => {
            if (pos.page !== pageNumber || pos.id === draggingField.id) return false

            // Use PDF coordinates for consistent collision detection
            const fieldX = pos.x
            const fieldY = pos.y
            const fieldWidth = 120 / scale // Field width in PDF coordinates
            const fieldHeight = 40 / scale // Field height in PDF coordinates

            // Check if mouse is within field boundaries (PDF coordinates)
            const isWithinX = pdfX >= fieldX && pdfX <= fieldX + fieldWidth
            const isWithinY = pdfY >= fieldY && pdfY <= fieldY + fieldHeight

            return isWithinX && isWithinY
          })

          setHoveredField(hoveredField || null)
        } else {
          setHoveredField(null)
        }
      } else {
        setMousePosition({ x: e.clientX, y: e.clientY })
        setHoveredField(null)
      }
      setShowCoordinates(true)
    },
    [enableDrop, draggingField, scale, fieldPositions],
  )

  // Handle drag leave
  const handleDragLeave = useCallback(
    (e) => {
      if (!enableDrop) return
      // Only set dragOver to false if we're leaving the document container
      if (!documentContainerRef.current?.contains(e.relatedTarget)) {
        setDragOver(false)
        setShowCoordinates(false)
        setHoveredField(null) // Reset hovered field when leaving drag area
      }
    },
    [enableDrop],
  )

  // Handle drop
  const handleDrop = useCallback(
    (e) => {
      if (!enableDrop) return
      e.preventDefault()
      setDragOver(false)

      try {
        const data = e.dataTransfer.getData('text/plain')
        const { fieldKey, fieldValue } = JSON.parse(data)

        // Get the page element that was dropped on
        const pageElement = e.target.closest('.react-pdf__Page')
        if (!pageElement) {
          message.error('Không thể xác định trang PDF')
          return
        }

        // Get page number from the page element
        const pageNumber = parseInt(pageElement.getAttribute('data-page-number'))
        if (!pageNumber) {
          message.error('Không thể xác định số trang')
          return
        }

        // Calculate relative position within the page
        const canvasElement = pageElement.querySelector('canvas')
        const targetElement = canvasElement || pageElement
        const pageRect = targetElement.getBoundingClientRect()
        const relativeX = e.clientX - pageRect.left
        const relativeY = e.clientY - pageRect.top

        // Convert to PDF coordinates (accounting for scale)
        const pdfX = relativeX / scale
        const pdfY = relativeY / scale

        // Create new field position
        const newPosition = {
          id: Date.now() + Math.random(),
          fieldKey,
          value: fieldValue,
          x: pdfX,
          y: pdfY,
          page: pageNumber,
        }

        // Add to field positions
        const newPositions = [...fieldPositions, newPosition]
        onFieldPositionsChange(newPositions)

        message.success(`Đã đặt trường "${fieldKey}" tại trang ${pageNumber}`)
      } catch (error) {
        handleError(error, 'handleDrop')
        message.error('Lỗi khi đặt trường vào PDF')
      }
    },
    [enableDrop, scale, fieldPositions, onFieldPositionsChange, message],
  )

  // Handle field drag start (for repositioning existing fields)
  const handleFieldDragStart = useCallback(
    (e, field) => {
      if (!enableDrop) return
      e.stopPropagation()

      setDraggingField(field)

      // Calculate offset from mouse to field top-left
      const fieldElement = e.currentTarget
      const rect = fieldElement.getBoundingClientRect()
      const offsetX = e.clientX - rect.left
      const offsetY = e.clientY - rect.top
      setDragOffset({ x: offsetX, y: offsetY })

      // Create a custom drag image that matches the field appearance
      const dragImage = document.createElement('div')
      dragImage.style.position = 'absolute'
      dragImage.style.top = '-1000px'
      dragImage.style.left = '-1000px'
      dragImage.style.minWidth = '120px'
      dragImage.style.maxWidth = '300px'
      dragImage.style.backgroundColor = '#faad14'
      dragImage.style.color = 'white'
      dragImage.style.padding = '6px 12px'
      dragImage.style.borderRadius = '6px'
      dragImage.style.fontSize = '12px'
      dragImage.style.fontWeight = '500'
      dragImage.style.whiteSpace = 'nowrap'
      dragImage.style.border = '1px solid rgba(255, 255, 255, 0.4)'
      dragImage.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.3)'
      dragImage.style.display = 'flex'
      dragImage.style.alignItems = 'center'
      dragImage.style.gap = '6px'
      dragImage.innerHTML = `
        <i class="fa fa-hand-rock" style="font-size: 11px; opacity: 0.9;"></i>
        <div>
          <div style="font-size: 11px; font-weight: 600; opacity: 0.9; margin-bottom: 1px;">${field.fieldKey}</div>
          <div style="font-size: 10px; opacity: 0.8; font-weight: 400;">${String(field.value || '').substring(0, 25)}${String(field.value || '').length > 25 ? '...' : ''}</div>
        </div>
      `
      document.body.appendChild(dragImage)

      // Set drag data
      e.dataTransfer.effectAllowed = 'move'
      e.dataTransfer.setDragImage(dragImage, 0, 0) // Use top-left for drag image
      e.dataTransfer.setData(
        'text/plain',
        JSON.stringify({
          type: 'reposition',
          fieldId: field.id,
        }),
      )

      // Clean up drag image after a short delay
      setTimeout(() => {
        if (document.body.contains(dragImage)) {
          document.body.removeChild(dragImage)
        }
      }, 100)
    },
    [enableDrop],
  )

  // Handle field drag end
  const handleFieldDragEnd = useCallback(() => {
    setDraggingField(null)
    setDragOffset({ x: 0, y: 0 })
    setShowCoordinates(false)
    setHoveredField(null) // Reset hovered field when drag ends
  }, [])

  // Enhanced drop handler to support repositioning
  const handleDropEnhanced = useCallback(
    (e) => {
      if (!enableDrop) return
      e.preventDefault()
      setDragOver(false)
      setHoveredField(null)

      try {
        const data = e.dataTransfer.getData('text/plain')
        const dropData = JSON.parse(data)

        // Get the page element that was dropped on
        const pageElement = e.target.closest('.react-pdf__Page')
        if (!pageElement) {
          message.error('Không thể xác định trang PDF')
          return
        }

        // Get page number from the page element
        const pageNumber = parseInt(pageElement.getAttribute('data-page-number'))
        if (!pageNumber) {
          message.error('Không thể xác định số trang')
          return
        }

        // Calculate relative position within the page
        const canvasElement = pageElement.querySelector('canvas')
        const targetElement = canvasElement || pageElement
        const pageRect = targetElement.getBoundingClientRect()
        let relativeX = e.clientX - pageRect.left
        let relativeY = e.clientY - pageRect.top

        // Convert to PDF coordinates first
        let pdfX = relativeX / scale
        let pdfY = relativeY / scale

        // For repositioning, account for drag offset (in PDF coordinates)
        if (dropData.type === 'reposition') {
          pdfX = pdfX - dragOffset.x / scale
          pdfY = pdfY - dragOffset.y / scale
        }

        // Get page dimensions to ensure coordinates are within boundaries
        const pageWidth = targetElement.offsetWidth / scale
        const pageHeight = targetElement.offsetHeight / scale
        const fieldWidth = 120 / scale // Field width in PDF coordinates
        const fieldHeight = 40 / scale // Field height in PDF coordinates

        // Ensure coordinates are within page boundaries (accounting for field size)
        pdfX = Math.max(0, Math.min(pdfX, pageWidth - fieldWidth))
        pdfY = Math.max(0, Math.min(pdfY, pageHeight - fieldHeight))

        // Check if dropping on an existing field - improved collision detection
        const existingFieldAtPosition = fieldPositions.find((pos) => {
          if (pos.page !== pageNumber) return false

          // Calculate field boundaries in PDF coordinates
          const fieldX = pos.x
          const fieldY = pos.y
          const fieldWidth = 120 / scale // Field width in PDF coordinates
          const fieldHeight = 40 / scale // Field height in PDF coordinates

          // Check if drop position is within field boundaries (PDF coordinates)
          const isWithinX = pdfX >= fieldX && pdfX <= fieldX + fieldWidth
          const isWithinY = pdfY >= fieldY && pdfY <= fieldY + fieldHeight

          return isWithinX && isWithinY
        })

        if (dropData.type === 'reposition') {
          // Repositioning existing field
          const fieldId = dropData.fieldId

          // Check if we're trying to drop on the same field (no-op)
          if (existingFieldAtPosition && existingFieldAtPosition.id === fieldId) {
            // Just update position, no replacement
            const newPositions = fieldPositions.map((pos) =>
              pos.id === fieldId ? { ...pos, x: pdfX, y: pdfY, page: pageNumber } : pos,
            )
            onFieldPositionsChange(newPositions)
            message.success('Đã cập nhật vị trí trường')
            return
          }

          // If dropping on another field, replace it
          let newPositions = fieldPositions.map((pos) =>
            pos.id === fieldId ? { ...pos, x: pdfX, y: pdfY, page: pageNumber } : pos,
          )

          if (existingFieldAtPosition && existingFieldAtPosition.id !== fieldId) {
            newPositions = newPositions.filter((pos) => pos.id !== existingFieldAtPosition.id)
            message.success(`Đã thay thế trường "${existingFieldAtPosition.fieldKey}"`)
          } else {
            message.success('Đã cập nhật vị trí trường')
          }

          onFieldPositionsChange(newPositions)
        } else {
          // Adding new field
          const { fieldKey, fieldValue } = dropData

          // Check if field with same key already exists on this page at similar position
          const duplicateField = fieldPositions.find((pos) => {
            if (pos.page !== pageNumber || pos.fieldKey !== fieldKey) return false
            const distance = Math.sqrt(Math.pow(pos.x - pdfX, 2) + Math.pow(pos.y - pdfY, 2))
            return distance < 20 // 20px tolerance for duplicate detection
          })

          if (duplicateField) {
            message.warning(`Trường "${fieldKey}" đã tồn tại tại vị trí tương tự`)
            return
          }

          let newPositions = [...fieldPositions]

          // If dropping on an existing field, replace it
          if (existingFieldAtPosition) {
            newPositions = newPositions.filter((pos) => pos.id !== existingFieldAtPosition.id)
            message.success(
              `Đã thay thế trường "${existingFieldAtPosition.fieldKey}" bằng "${fieldKey}"`,
            )
          } else {
            message.success(
              `Đã đặt trường "${fieldKey}" tại trang ${pageNumber} (X:${Math.round(pdfX)}, Y:${Math.round(pdfY)})`,
            )
          }

          const newPosition = {
            id: Date.now() + Math.random(),
            fieldKey,
            value: fieldValue,
            x: pdfX,
            y: pdfY,
            page: pageNumber,
          }

          newPositions.push(newPosition)
          onFieldPositionsChange(newPositions)
        }
      } catch (error) {
        handleError(error, 'handleDropEnhanced')
        message.error('Lỗi khi đặt trường vào PDF')
      }
    },
    [enableDrop, scale, fieldPositions, onFieldPositionsChange, message, dragOffset],
  )

  return (
    <div className="pdf-viewer">
      <div className="pdf-viewer--header">
        <div className="pdf-viewer--header--right d-flex align-items-center">
          <Button
            onClick={(e) => {
              e.preventDefault()
              downloadBase64File(fileName, selectedFile)
            }}
            className="me-2"
            icon={<i className="fa-solid fa-arrow-down"></i>}>
            Tải về
          </Button>
          <Select
            className="me-2"
            style={{ width: 70 }}
            value={scale * 100}
            defaultValue={150}
            onChange={(value) => setScale(value / 100)}>
            {[50, 75, 100, 125, 150, 175, 200].map((n) => (
              <Select.Option key={n}>{n}</Select.Option>
            ))}
          </Select>
          <Button
            shape="circle"
            className="me-2"
            onClick={() => setScale(parseFloat(scale) - 0.25 < 0 ? 0 : parseFloat(scale) - 0.25)}
            icon={<i className="fa-solid fa-minus" style={{ marginLeft: '0.5em' }}></i>}></Button>
          <Button
            shape="circle"
            className="me-2"
            onClick={() => setScale(parseFloat(scale) + 0.25)}
            icon={<i className="fa-solid fa-plus" style={{ marginLeft: '0.5em' }}></i>}></Button>
          <Button ghost type="primary" onClick={() => setScale(1)}>
            Reset
          </Button>
        </div>
      </div>

      <div
        className="pdf-viewer--content"
        ref={documentContainerRef}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDropEnhanced}
        style={{
          border: enableDrop && dragOver ? '2px dashed #1890ff' : 'none',
          backgroundColor: enableDrop && dragOver ? '#e6f7ff' : 'transparent',
          transition: 'all 0.3s ease',
        }}>
        <Document
          ref={documentRef}
          className="pdf-viewer--document"
          style={{
            width: width,
          }}
          file={selectedFile}
          onLoadSuccess={onDocumentLoadSuccess}>
          {pageNumbers.map((number) => (
            <div
              key={number}
              style={{ position: 'relative' }}
              data-page-number={number}
              ref={(el) => (pageRefs.current[number] = el)}>
              <Page
                renderTextLayer={false}
                onRenderSuccess={onPageRenderSuccess}
                scale={1 * scale}
                className="pdf-viewer--page"
                pageNumber={number}
              />

              {/* Render positioned fields on this page */}
              {enableDrop &&
                fieldPositions
                  .filter((pos) => pos.page === number)
                  .map((position) => {
                    const isDragging = draggingField?.id === position.id
                    const isHovered = hoveredField?.id === position.id
                    const willBeReplaced =
                      dragOver &&
                      hoveredField?.id === position.id &&
                      draggingField?.id !== position.id

                    return (
                      <div
                        key={position.id}
                        draggable={true}
                        onDragStart={(e) => handleFieldDragStart(e, position)}
                        onDragEnd={handleFieldDragEnd}
                        style={{
                          position: 'absolute',
                          left: position.x * scale,
                          top: position.y * scale,
                          minWidth: '120px',
                          maxWidth: '300px',
                          backgroundColor: willBeReplaced
                            ? '#ff4d4f' // Red when will be replaced
                            : isDragging
                              ? '#faad14' // Yellow when dragging
                              : '#1890ff', // Blue normal state
                          color: 'white',
                          padding: '6px 12px',
                          borderRadius: '6px',
                          fontSize: '11px', // Match PDF font size exactly
                          fontWeight: '500',
                          cursor: isDragging ? 'grabbing' : 'grab',
                          zIndex: isDragging ? 1000 : willBeReplaced ? 100 : 10,
                          whiteSpace: 'nowrap',
                          // No transform - position from top-left
                          border: willBeReplaced
                            ? '2px solid rgba(255, 255, 255, 0.9)'
                            : '1px solid rgba(255, 255, 255, 0.4)',
                          boxShadow: willBeReplaced
                            ? '0 4px 12px rgba(255, 77, 79, 0.5)'
                            : isDragging
                              ? '0 6px 20px rgba(0, 0, 0, 0.3)'
                              : '0 2px 8px rgba(0, 0, 0, 0.15)',
                          transition: isDragging ? 'none' : 'all 0.2s ease',
                          opacity: isDragging ? 0.7 : 1, // Slightly transparent when dragging
                          userSelect: 'none',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                        }}
                        onMouseEnter={(e) => {
                          if (!isDragging && !dragOver) {
                            e.currentTarget.style.transform = 'scale(1.05)'
                            e.currentTarget.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.25)'
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!isDragging && !dragOver) {
                            e.currentTarget.style.transform = 'scale(1)'
                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)'
                          }
                        }}>
                        <i
                          className={
                            willBeReplaced
                              ? 'fa fa-exclamation-triangle'
                              : isDragging
                                ? 'fa fa-hand-rock'
                                : 'fa fa-arrows-alt'
                          }
                          style={{
                            fontSize: '11px',
                            flexShrink: 0,
                            opacity: 0.9,
                          }}
                        />
                        <div
                          style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'flex-start',
                            minWidth: 0,
                            flex: 1,
                          }}>
                          <div
                            style={{
                              fontSize: '11px',
                              fontWeight: '600',
                              opacity: 0.9,
                              marginBottom: '1px',
                            }}>
                            {position.fieldKey}
                          </div>
                          <div
                            style={{
                              fontSize: '10px',
                              opacity: 0.8,
                              fontWeight: '400',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              maxWidth: '100%',
                            }}>
                            {willBeReplaced
                              ? 'Sẽ bị thay thế!'
                              : String(position.value || '').substring(0, 25) +
                                (String(position.value || '').length > 25 ? '...' : '')}
                          </div>
                        </div>
                      </div>
                    )
                  })}
            </div>
          ))}
        </Document>
      </div>

      {/* Drag overlay with coordinates */}
      {enableDrop && dragOver && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            backgroundColor: 'rgba(24, 144, 255, 0.9)',
            color: 'white',
            padding: '20px',
            borderRadius: '8px',
            fontSize: '16px',
            fontWeight: 'bold',
            pointerEvents: 'none',
            zIndex: 1000,
          }}>
          {draggingField ? 'Kéo để điều chỉnh vị trí' : 'Thả vào đây để đặt vị trí trường'}
        </div>
      )}

      {/* Coordinate display */}
      {enableDrop && showCoordinates && (
        <div
          style={{
            position: 'fixed',
            left: mousePosition.x + 15,
            top: mousePosition.y - 30,
            backgroundColor: 'rgba(0, 0, 0, 0.9)',
            color: 'white',
            padding: '6px 10px',
            borderRadius: '6px',
            fontSize: '11px',
            fontWeight: 'bold',
            pointerEvents: 'none',
            zIndex: 1001,
            whiteSpace: 'nowrap',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
          }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <i className="fa fa-crosshairs" style={{ fontSize: '10px', opacity: 0.8 }} />
            <span>
              {mousePosition.pdfX !== undefined &&
              mousePosition.pdfY !== undefined &&
              mousePosition.page ? (
                <>
                  Trang {mousePosition.page} - X: {mousePosition.pdfX} | Y: {mousePosition.pdfY}
                </>
              ) : (
                'Di chuyển chuột vào PDF'
              )}
            </span>
          </div>
          {draggingField && (
            <div style={{ fontSize: '9px', opacity: 0.7, marginTop: '2px' }}>
              Đang kéo: {draggingField.fieldKey}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default DroppablePdfViewer
