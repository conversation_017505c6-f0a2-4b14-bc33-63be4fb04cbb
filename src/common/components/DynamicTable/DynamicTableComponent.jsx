import React, { useEffect, useMemo, useState } from 'react'
import { Form, Input, InputNumber, Popconfirm, Table, Typography } from 'antd'
import PropTypes from '../../PropTypes'
import EditableCellComponent from './EditableCellComponent'
import {
  addListItemService,
  customUpdateListItemService,
  patchMultiRecordDetails,
} from '../../services'
import lists from '../../lists'
import { SearchOutlined } from '@ant-design/icons'
import {
  determineDataType,
  getColumnFilterList,
  handleError,
  makeResizableColumns,
} from '../../helpers'
import dayjs from '../../dayjs'
import { useUI } from '../../UIProvider'
import _ from 'lodash'
import { useLocation, useNavigate } from 'react-router-dom'
import AsyncButton from '../AsyncButton'
import COLOR from '../../color'
import { useSelector } from 'react-redux'
import { MODULE_AUTH } from '../../../store/auth'
import ExcelHandler from '../ExcelHandler'
import ResizableTitle from '../ResizableTitle'

const propTypes = {
  canNavigateToDetailPage: PropTypes.bool,
  onSave: PropTypes.func,
  isEditableTable: PropTypes.bool,
  tableColumns: PropTypes.array.isRequired,
  tableDataSource: PropTypes.array.isRequired,
  tableMetadataEntity: PropTypes.object,
  tablePrimaryKeyName: PropTypes.string.isRequired,
  tableName: PropTypes.string.isRequired,
}

const DynamicTableComponent = ({
  canNavigateToDetailPage,
  onSave,
  isEditableTable,
  tableColumns,
  tableDataSource,
  tableMetadataEntity,
  tablePrimaryKeyName,
  tableName,
}) => {
  //hooks
  const [form] = Form.useForm()
  const ui = useUI()
  const navigate = useNavigate()
  const location = useLocation()
  //state
  const [metaEntityProperties, setMetaEntityProperties] = useState([])
  const [dataSource, setDataSource] = useState(tableDataSource)
  const [formMode, setFormMode] = useState('') // State to track form mode ('add' or 'edit')
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  //others
  const [editingKey, setEditingKey] = useState('')
  const isEditing = (record) => record.key === editingKey

  // Update states when `tableMetadataEntity` changes
  useEffect(() => {
    if (tableMetadataEntity) {
      setMetaEntityProperties(tableMetadataEntity?.Property || [])
    }
  }, [tableMetadataEntity])
  useEffect(() => {
    if (!_.isEqual(dataSource, tableDataSource)) {
      setDataSource(tableDataSource)
      setEditingKey('')
    }
  }, [tableDataSource])

  // Add a new row to the table
  const addRow = () => {
    // Check if there's already a row in editing mode
    if (editingKey) {
      ui.showWarning('Please save or cancel the current edit before adding a new row.') // Optionally show a warning
      return
    }

    const newRow = { key: `new_${Date.now()}` } // Generate a unique key

    // Populate newRow with default values based on metaEntityProperties
    metaEntityProperties.forEach((property) => {
      const fieldName = property['@_Name']
      const fieldType = property['@_Type']
      let defaultValue = null
      if (fieldType === 'Edm.Int32' || fieldType === 'Edm.Decimal') {
        defaultValue = 0
      } else if (fieldType === 'Edm.Boolean') {
        defaultValue = false
      } else if (fieldType === 'Edm.DateTimeOffset') {
        defaultValue = dayjs()
      }
      newRow[fieldName] = defaultValue
    })
    // Add the new row to the data source
    setDataSource((prev) => [...prev, newRow])

    // Set the new row in edit mode
    setEditingKey(newRow.key)
    setFormMode('add') // Set form mode to 'add'

    // Initialize form values for the new row
    form.setFieldsValue(newRow)
  }

  const edit = (record) => {
    const processedRecord = {}

    // Ensure you're using the correct metadata array (e.g., metaEntityProperties)
    for (let index = 0; index < metaEntityProperties.length; index++) {
      const property = metaEntityProperties[index]
      const fieldName = property['@_Name']
      const fieldType = property['@_Type']

      if (fieldType === 'Edm.DateTimeOffset') {
        // Use dayjs to format DateTimeOffset fields
        processedRecord[fieldName] = record[fieldName] ? dayjs(record[fieldName]) : null
      } else if (fieldType === 'Edm.Boolean') {
        // Ensure booleans are correctly formatted
        processedRecord[fieldName] = Boolean(record[fieldName])
      } else {
        // For other types, keep the original value
        processedRecord[fieldName] = record[fieldName]
      }
    }

    form.setFieldsValue({
      name: '',
      age: '',
      address: '',
      ...processedRecord,
    })
    setEditingKey(record.key)
    setFormMode('edit') // Set form mode to 'edit'
  }

  const cancel = (record) => {
    if (typeof record.key === 'string' && record?.key?.startsWith('new_')) {
      setDataSource((prev) => prev.filter((item) => item.key !== record.key))
    }
    setEditingKey('')
    setFormMode('') // Reset form mode
  }
  const save = async (key) => {
    try {
      const updatedRow = await form.validateFields()

      //const updatedRow = await form.getFieldsValue()
      let processedRecord = {}
      for (let index = 0; index < metaEntityProperties.length; index++) {
        const property = metaEntityProperties[index]
        const fieldName = property['@_Name']
        const fieldType = property['@_Type']
        const fieldValue =
          updatedRow[fieldName] || tableDataSource.find((item) => item.key === key)?.[fieldName]

        // Ensure the primary key is included in the processed record
        if (fieldName === tablePrimaryKeyName) {
          if (formMode === 'add') {
            continue
          } else {
            processedRecord[fieldName] =
              updatedRow[fieldName] || tableDataSource.find((item) => item.key === key)?.[fieldName]
            continue
          }
        }

        if (fieldName === 'lu_updated') {
          processedRecord[fieldName] = dayjs().format()
          continue
        }
        if (fieldName === 'lu_user_id') {
          processedRecord[fieldName] = currentUser.User_id
          continue
        }

        if (fieldType === 'Edm.DateTimeOffset') {
          // Use dayjs to format DateTimeOffset fields
          processedRecord[fieldName] = fieldValue ? dayjs(fieldValue).format() : null
        } else if (fieldType === 'Edm.Boolean') {
          // Ensure booleans are correctly formatted
          processedRecord[fieldName] = Boolean(fieldValue)
        } else {
          // For other types, keep the original value
          processedRecord[fieldName] = fieldValue
        }
      }
      let addItemResponse = {}
      try {
        if (formMode === 'add') {
          addItemResponse = await addListItemService(lists?.[tableName], {})
          processedRecord[tablePrimaryKeyName] = addItemResponse[tablePrimaryKeyName]
          await patchMultiRecordDetails(lists?.[tableName], [processedRecord])
        } else {
          await patchMultiRecordDetails(lists?.[tableName], [processedRecord])
        }

        setEditingKey('') // Exit editing mode

        if (onSave) {
          onSave(processedRecord)
        }

        ui.notiSuccess('Thành công')
      } catch (error) {
        handleError(error)
        ui.notiError('Thất bại')
      }
    } catch (errInfo) {
      const errorFields = form.getFieldsError()
      const newErrorFields = errorFields.filter((item) => item.errors.length > 0)

      ui.notiWarning('Thông tin chưa đầy đủ', newErrorFields[0].errors[0])
    }
  }

  const navigateToDetailPage = (record) => {
    const column = lists[tableName].detailPageUrlParam || lists[tableName].primaryKeyName

    navigate(`${location.pathname}/${record[column]}`)
  }

  //const variable
  const displayColumns = tableColumns.filter(
    (column) => column?.col_hidden === false || column?.col_hidden == null,
  )

  const operationColumn = {
    title: 'Hành động',
    dataIndex: 'operation',
    render: (_, record) => {
      const editable = isEditing(record)
      return editable ? (
        <span>
          <Typography.Link onClick={() => save(record.key)} style={{ marginRight: 8 }}>
            Save
          </Typography.Link>
          <Popconfirm title="Sure to cancel?" onConfirm={cancel}>
            <a>Cancel</a>
          </Popconfirm>
        </span>
      ) : (
        <>
          {isEditableTable && (
            <Typography.Link disabled={editingKey !== ''} onClick={() => edit(record)}>
              Edit
            </Typography.Link>
          )}
          {canNavigateToDetailPage && (
            <Typography.Link
              onClick={() => {
                navigateToDetailPage(record)
              }}>
              {' '}
              See Detail{' '}
            </Typography.Link>
          )}
        </>
      )
    },
    fixed: 'right',
  }

  const mergedColumns = [...displayColumns]

  if (isEditableTable || canNavigateToDetailPage) {
    mergedColumns.push(operationColumn)
  }
  const totalWidth = mergedColumns.reduce((sum) => sum + 170, 0)

  return (
    <Form form={form} component={false}>
      <div className=""> Priamry key: {tablePrimaryKeyName}</div>
      {isEditableTable && (
        <AsyncButton variant="solid" color="cyan" onClick={addRow} disabled={editingKey !== ''}>
          Tạo mới
        </AsyncButton>
      )}
      {/* <ExcelHandler
        dataToExport={tableDataSource}
        dataTableName={tableMetadataEntity?.['@_Name'] || null}
        //  onImport={handleImport}
        // onSave={handleConfirmImportExcel}
      ></ExcelHandler> */}
      <Table
        size="small"
        className="custom-table"
        components={{
          body: {
            cell: EditableCellComponent, //editingKey ? EditableCellComponent : null
          },
        }}
        bordered
        dataSource={dataSource}
        // rowHoverable={false}
        rowHoverable={false}
        // tableLayout="fixed"
        scroll={{
          x: totalWidth,
          y: 50 * 11,
        }}
        columns={mergedColumns.map((col) => {
          return {
            ...col,
            width: 200,
            onCell: (record) => ({
              record,
              dataIndex: col.dataIndex,
              inputType: determineDataType(
                col, //systemSettingTableCols
                tableMetadataEntity?.Property.find((prop) => prop['@_Name'] == col.dataIndex),
              ),
              isRequire: col?.col_require, // Add the isRequire prop here
              title: col.title,
              editing: col.editable ? isEditing(record) : undefined,
              fieldProp: tableMetadataEntity?.Property.find((prop) => prop['@_Name'] == col.title),
              align: col?.numeric_type == true ? 'right' : 'left',
            }),
            // for filter
            filterSearch: true,
            filters: getColumnFilterList(dataSource, col.dataIndex),
            onFilter: (value, record) => {
              if (record.children) {
                return true
              }

              const fieldValue = record[col.dataIndex]
              if (fieldValue == null) return false
              return fieldValue.toString().toLowerCase().includes(value.toLowerCase())
            },
            filterIcon: (filtered) => (
              <div className="ps-1">
                <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
              </div>
            ),
          }
        })}
        rowClassName="editable-row"
        pagination={false}
      />
    </Form>
  )
}

DynamicTableComponent.propTypes = propTypes

export default DynamicTableComponent
