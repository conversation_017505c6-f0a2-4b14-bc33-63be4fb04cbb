import React, { useState } from 'react'
import { Select } from 'antd'

const { Option } = Select

const ResetSelectOnChange = () => {
  const [selected, setSelected] = useState(null)

  const handleChange = (value) => {
    console.log('Selected:', value)
    setSelected(null) // reset lại sau khi chọn
  }

  return (
    <Select
      placeholder="Chọn một giá trị"
      value={selected}
      onChange={handleChange}
      style={{ width: 200 }}>
      <Option value="1">Option 1</Option>
      <Option value="2">Option 2</Option>
    </Select>
  )
}

export default ResetSelectOnChange
