import React, { useState, useCallback } from 'react'
import { Card, Row, Col, Tag, Button, Space, Typography, Divider, App } from 'antd'
import {
  DragOutlined,
  DeleteOutlined,
  CheckOutlined,
  EyeOutlined,
  ExportOutlined,
} from '@ant-design/icons'

const { Title, Text } = Typography

/**
 * Drag and Drop Field Mapper Component
 * Allows users to drag available data fields directly onto PDF viewer to set position (x,y,page)
 *
 * @param {Object} props - Component props
 * @param {Object} props.availableFields - Available data fields for mapping
 * @param {Array} props.fieldPositions - Array of field positions with x,y,page,fieldKey
 * @param {Function} props.onFieldPositionsChange - Callback when field positions change
 * @param {Function} props.onPreview - Callback to preview PDF with positioned fields
 * @param {Object} props.defaultFieldPositions - Default field positions for template
 * @param {string} props.templateId - Current template ID for export config
 */
const DragDropFieldMapper = ({
  availableFields = {},
  fieldPositions = [],
  onFieldPositionsChange,
  onPreview,
  defaultFieldPositions = {},
  templateId = '',
}) => {
  const { message } = App.useApp()
  const [draggedField, setDraggedField] = useState(null)

  // Handle drag start for available fields
  const handleDragStart = useCallback((e, fieldKey, fieldValue) => {
    setDraggedField({ key: fieldKey, value: fieldValue })
    e.dataTransfer.effectAllowed = 'copy'
    e.dataTransfer.setData('text/plain', JSON.stringify({ fieldKey, fieldValue }))

    // Create a custom drag image for better visual feedback
    const dragImage = document.createElement('div')
    dragImage.style.position = 'absolute'
    dragImage.style.top = '-1000px'
    dragImage.style.left = '-1000px'
    dragImage.style.backgroundColor = 'rgba(24, 144, 255, 0.9)'
    dragImage.style.color = 'white'
    dragImage.style.padding = '6px 10px'
    dragImage.style.borderRadius = '6px'
    dragImage.style.fontSize = '12px'
    dragImage.style.fontWeight = 'bold'
    dragImage.style.whiteSpace = 'nowrap'
    dragImage.style.border = '1px solid rgba(255, 255, 255, 0.3)'
    dragImage.innerHTML = `<i class="fa fa-plus" style="margin-right: 4px;"></i>${fieldKey}`
    document.body.appendChild(dragImage)

    // Set custom drag image
    e.dataTransfer.setDragImage(dragImage, 0, 0)

    // Clean up drag image after a short delay
    setTimeout(() => {
      if (document.body.contains(dragImage)) {
        document.body.removeChild(dragImage)
      }
    }, 100)
  }, [])

  // Handle drag end
  const handleDragEnd = useCallback(() => {
    setDraggedField(null)
  }, [])

  // Add default field positions to current positions
  const handleAddDefaultFields = useCallback(() => {
    if (!defaultFieldPositions || Object.keys(defaultFieldPositions).length === 0) {
      message.warning('Không có cấu hình mặc định cho template này')
      return
    }

    const newPositions = [...fieldPositions]
    Object.entries(defaultFieldPositions).forEach(([fieldKey, position]) => {
      // Check if field already exists
      const existingIndex = newPositions.findIndex((p) => p.fieldKey === fieldKey)
      if (existingIndex === -1) {
        newPositions.push({
          id: Date.now() + Math.random(),
          fieldKey,
          x: position.x,
          y: position.y,
          page: position.page || 1,
          value: availableFields[fieldKey] || '',
        })
      }
    })

    onFieldPositionsChange(newPositions)
    message.success('Đã thêm các trường mặc định')
  }, [defaultFieldPositions, fieldPositions, onFieldPositionsChange, availableFields, message])

  // Remove a field position
  const handleRemoveField = useCallback(
    (fieldId) => {
      const newPositions = fieldPositions.filter((p) => p.id !== fieldId)
      onFieldPositionsChange(newPositions)
      message.success('Đã xóa trường')
    },
    [fieldPositions, onFieldPositionsChange, message],
  )

  // Handle preview
  const handlePreview = useCallback(() => {
    if (fieldPositions.length === 0) {
      message.warning('Chưa có trường nào được đặt vị trí')
      return
    }
    onPreview(fieldPositions)
  }, [fieldPositions, onPreview, message])

  // Handle export config
  const handleExportConfig = useCallback(() => {
    if (fieldPositions.length === 0) {
      message.warning('Chưa có trường nào để export')
      return
    }

    if (!templateId) {
      message.warning('Chưa có template ID. Vui lòng chọn template trước khi export.')
      return
    }

    // Convert field positions to config format
    const config = {}
    fieldPositions.forEach((position) => {
      config[position.fieldKey] = {
        x: Math.round(position.x),
        y: Math.round(position.y),
        page: position.page,
      }
    })

    // Create the export object
    const exportConfig = {
      [templateId]: config,
    }

    // Log to console in copy-paste friendly format
    console.group('🎯 PDF Field Position Configuration')
    console.log('📋 Copy and paste this into your defaultFieldPositions object:')
    console.log('')
    console.log('%c' + JSON.stringify(exportConfig, null, 2), 'color: #52c41a; font-weight: bold;')
    console.log('')
    console.log('📝 Usage example:')
    console.log(
      `%cconst defaultFieldPositions = {
  ...existingConfigs,
  ${JSON.stringify(exportConfig, null, 2).slice(1, -1)}
}`,
      'color: #1890ff; font-style: italic;',
    )
    console.groupEnd()

    message.success(`✅ Đã export cấu hình cho ${Object.keys(config).length} trường vào console`)
  }, [fieldPositions, templateId, message])

  return (
    <div>
      <Row gutter={[16, 16]}>
        {/* Available Fields Panel */}
        <Col span={12}>
          <Card
            title={
              <Space>
                <DragOutlined />
                <span>Dữ liệu có sẵn</span>
              </Space>
            }
            size="small">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Kéo các trường dữ liệu vào PDF để đặt vị trí
            </Text>
            <Divider style={{ margin: '12px 0' }} />

            {/* Action Buttons */}
            <Space style={{ marginBottom: '12px' }} wrap>
              <Button
                size="small"
                icon={<CheckOutlined />}
                onClick={handleAddDefaultFields}
                disabled={
                  !defaultFieldPositions || Object.keys(defaultFieldPositions).length === 0
                }>
                Thêm trường mặc định
              </Button>
              <Button
                size="small"
                type="primary"
                icon={<EyeOutlined />}
                onClick={handlePreview}
                disabled={fieldPositions.length === 0}>
                Preview PDF
              </Button>
              <Button
                size="small"
                icon={<ExportOutlined />}
                onClick={handleExportConfig}
                disabled={fieldPositions.length === 0 || !templateId}
                style={{
                  backgroundColor: '#52c41a',
                  borderColor: '#52c41a',
                  color: 'white',
                  fontWeight: 'bold',
                }}
                title={
                  !templateId ? 'Vui lòng chọn template trước' : 'Export cấu hình vị trí ra console'
                }>
                Export Config
              </Button>
            </Space>

            <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
              <Space direction="vertical" style={{ width: '100%' }} size="small">
                {Object.entries(availableFields).map(([key, value]) => {
                  const isDragging = draggedField?.key === key
                  return (
                    <Tag
                      key={key}
                      draggable
                      onDragStart={(e) => handleDragStart(e, key, value)}
                      onDragEnd={handleDragEnd}
                      style={{
                        cursor: 'grab',
                        padding: '6px 10px',
                        margin: '2px',
                        border: '1px solid #d9d9d9',
                        borderRadius: '6px',
                        display: 'block',
                        backgroundColor: isDragging ? '#e6f7ff' : '#fafafa',
                        transition: 'all 0.2s ease',
                        userSelect: 'none',
                        opacity: isDragging ? 0.5 : 1,
                        transform: isDragging ? 'scale(0.95)' : 'scale(1)',
                      }}
                      onMouseDown={(e) => (e.target.style.cursor = 'grabbing')}
                      onMouseUp={(e) => (e.target.style.cursor = 'grab')}
                      onMouseEnter={(e) => {
                        if (!isDragging) {
                          e.target.style.backgroundColor = '#e6f7ff'
                          e.target.style.borderColor = '#1890ff'
                          e.target.style.transform = 'scale(1.02)'
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!isDragging) {
                          e.target.style.backgroundColor = '#fafafa'
                          e.target.style.borderColor = '#d9d9d9'
                          e.target.style.transform = 'scale(1)'
                        }
                      }}>
                      <DragOutlined style={{ marginRight: '6px', color: '#1890ff' }} />
                      <strong>{key}:</strong> {String(value || '').substring(0, 30)}
                      {String(value || '').length > 30 && '...'}
                    </Tag>
                  )
                })}
              </Space>
            </div>
          </Card>
        </Col>

        {/* Field Positions Panel */}
        <Col span={12}>
          <Card
            title={
              <Space>
                <span>Vị trí trường ({fieldPositions.length})</span>
              </Space>
            }
            size="small">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Danh sách các trường đã được đặt vị trí trên PDF
            </Text>
            <Divider style={{ margin: '12px 0' }} />
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              <Space direction="vertical" style={{ width: '100%' }} size="small">
                {fieldPositions.length === 0 ? (
                  <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                    <Text type="secondary">
                      Chưa có trường nào được đặt vị trí.
                      <br />
                      Kéo trường từ bên trái vào PDF để đặt vị trí.
                    </Text>
                  </div>
                ) : (
                  fieldPositions.map((position) => (
                    <div
                      key={position.id}
                      style={{
                        padding: '8px',
                        border: '1px solid #d9d9d9',
                        borderRadius: '4px',
                        backgroundColor: '#f6ffed',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}>
                      <div style={{ flex: 1 }}>
                        <Text strong style={{ fontSize: '13px' }}>
                          {position.fieldKey}
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          Trang {position.page} - X: {Math.round(position.x)}, Y:{' '}
                          {Math.round(position.y)}
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '10px', color: '#999' }}>
                          💡 Kéo trường trên PDF để điều chỉnh vị trí
                        </Text>
                        <br />
                        <Text style={{ fontSize: '11px', color: '#666' }}>
                          {String(position.value || '').substring(0, 30)}
                          {String(position.value || '').length > 30 && '...'}
                        </Text>
                      </div>
                      <Button
                        type="text"
                        size="small"
                        icon={<DeleteOutlined />}
                        onClick={() => handleRemoveField(position.id)}
                        style={{ color: '#ff4d4f' }}
                      />
                    </div>
                  ))
                )}
              </Space>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default DragDropFieldMapper
