import { Button, Dropdown, Layout, Menu } from 'antd'
const { Sider } = Layout
import React, { useState } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { useAuth } from './AuthProvider'
import PropTypes from 'prop-types'
import config from './config'
import { LOGIN_TYPES, MODE_VIEW_DATA } from './constant'
import { useMsal } from '@azure/msal-react'
import { useDispatch, useSelector } from 'react-redux'
import { authActions, MODULE_AUTH } from '../store/auth'
import imgAvatar from '../assets/avatar.png'
import imgLogo from '../assets/Logo.png'
import imgHome from '../assets/menu-home.png'
import CustomHeader from './components/CustomHeader'
import { PERMISSION } from '../components/Auth/AuthConstant'
import { clearProcessingPersonForUser } from '../components/Visit/VisitService'

const MainLayout = ({ children }) => {
  const { logout, loginType, checkPermission } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()
  const [onBreakpoint, setOnBreakpoint] = useState(false)
  const [collapsed, setCollapsed] = useState(false)
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])
  const { instance, accounts } = useMsal()
  const dispatch = useDispatch()

  const imageSize = '25px'

  const logoutRequest = {
    account: instance.getAccountByHomeId(accounts.length > 0 ? accounts[0].homeAccountId : null),
    postLogoutRedirectUri: `${window.location.origin}/login`,
    onRedirectNavigate: () => {
      localStorage.removeItem(config.LOCAL_PROFILE)
      localStorage.removeItem(config.LOCAL_AUTHENTICATED)
      localStorage.removeItem(config.LOCAL_ACCESS_TOKEN)
      localStorage.removeItem(config.LOCAL_REFRESH_TOKEN)
      localStorage.removeItem(config.LOCAL_LOGIN_TYPE)
      return true
    },
  }

  const handleClickLogout = async () => {
    try {
      // Clear processing person information for all visits associated with the current user
      if (currentUser?.User_id) {
        await clearProcessingPersonForUser(currentUser.User_id)
      }

      // Proceed with normal logout
      if (loginType === LOGIN_TYPES.OFFICE365) {
        await instance.logoutRedirect(logoutRequest)
      } else {
        await logout()
      }
    } catch (error) {
      console.error('Error during logout:', error)
      // Still attempt to logout even if clearing processing person fails
      if (loginType === LOGIN_TYPES.OFFICE365) {
        await instance.logoutRedirect(logoutRequest)
      } else {
        await logout()
      }
    }
  }

  const navigateTo = (url) => {
    navigate(url)
    if (onBreakpoint) {
      setCollapsed(!collapsed)
    }
  }

  const handleChangeMode = (mode) => {
    if (mode === MODE_VIEW_DATA.NORMAL) {
      dispatch(authActions.setModeViewData(mode))
    } else {
      dispatch(authActions.setModeViewData(mode))
    }
  }

  const items = [
    {
      key: '/',
      label: 'Home',
      visible: 'true',
      icon: <img src={imgHome} width={imageSize} />,
      onClick: () => navigateTo('/'),
    },
    {
      key: '/his',
      label: 'Danh mục SI App',
      visible: 'true',
      icon: <img src={null} width={imageSize} />,
      children: [
        {
          key: '/his/visit',
          icon: <img src={null} width={imageSize} />,
          visible: 'true',
          label: 'Danh sách Visit',
          onClick: () => navigateTo('/his/visit'),
        },
        {
          key: '/his/medicine',
          icon: <img src={null} width={imageSize} />,
          visible: checkPermission(PERMISSION.SUPER_ADMIN),
          label: 'Danh sách Medicine',
          onClick: () => navigateTo('/his/medicine'),
        },
        {
          key: '/his/medical-supplies',
          icon: <img src={null} width={imageSize} />,
          visible: checkPermission(PERMISSION.SUPER_ADMIN),
          label: 'Danh sách VTYT',
          onClick: () => navigateTo('/his/medical-supplies'),
        },
        {
          key: '/his/technical-services',
          icon: <img src={null} width={imageSize} />,
          visible: checkPermission(PERMISSION.SUPER_ADMIN),
          label: 'Danh sách DVKT',
          onClick: () => navigateTo('/his/technical-services'),
        },
        {
          key: '/his/group-conditions',
          icon: <img src={null} width={imageSize} />,
          visible: checkPermission(PERMISSION.SUPER_ADMIN),
          label: 'Danh sách nhóm điều kiện',
          onClick: () => navigateTo('/his/group-conditions'),
        },
        {
          key: '/his/conditions',
          icon: <img src={null} width={imageSize} />,
          visible: checkPermission(PERMISSION.SUPER_ADMIN),
          label: 'Danh sách các điều kiện',
          onClick: () => navigateTo('/his/conditions'),
        },
        {
          key: '/his/xml',
          icon: <img src={null} width={imageSize} />,
          visible: checkPermission(PERMISSION.SUPER_ADMIN),
          label: 'DS dữ liệu nhập bảng xml',
          onClick: () => navigateTo('/his/xml'),
        },
      ],
    },
    {
      key: '/DM_BHYT',
      label: 'Danh mục BHYT',
      icon: <img src={null} width={imageSize} />,
      visible: checkPermission(PERMISSION.SUPER_ADMIN),
      children: [
        {
          key: '/DM_BHYT/medicine',
          icon: <img src={null} width={imageSize} />,
          label: 'Danh sách thuốc BHYT',
          onClick: () => navigateTo('/DM_BHYT/medicine'),
        },
        {
          key: '/DM_BHYT/medical-supplies',
          icon: <img src={null} width={imageSize} />,
          label: 'Danh sách VTYT BHYT',
          onClick: () => navigateTo('/DM_BHYT/medical-supplies'),
        },
        {
          key: '/DM_BHYT/technical-services',
          icon: <img src={null} width={imageSize} />,
          label: 'Danh sách DVKT BHYT',
          onClick: () => navigateTo('/DM_BHYT/technical-services'),
        },
      ],
    },
    {
      key: '/permission',
      label: 'Permission',
      visible: checkPermission(PERMISSION.SUPER_ADMIN),
      icon: <i className="fas fa-key" />,
      onClick: () => navigateTo('/permission'),
    },
    {
      key: '/mapping',
      label: 'Mapping Management',
      visible: checkPermission(PERMISSION.SUPER_ADMIN),
      icon: <i className="fas fa-link" />,
      onClick: () => navigateTo('/mapping'),
    },
    {
      key: '/ssitem',
      label: 'Quản lý danh mục',
      visible: checkPermission(PERMISSION.SUPER_ADMIN),
      icon: <i className="fas fa-list" />,
      onClick: () => navigateTo('/ssitem'),
    },
  ]

  // Function to flatten menu items and return array of paths
  const flattenMenu = (items) => {
    let paths = []
    items.forEach((item) => {
      paths.push(item.key)
      if (item.children) {
        paths = paths.concat(flattenMenu(item.children))
      }
    })
    return paths
  }

  const allPaths = flattenMenu(items)

  // Function to find the longest matching prefix in paths
  const findSelectedKey = (paths, pathname) => {
    let selectedKey = '/'
    for (const path of paths) {
      if (pathname.startsWith(path) && path.length > selectedKey.length) {
        selectedKey = path
      }
    }
    return selectedKey
  }

  const selectedKey = findSelectedKey(allPaths, location.pathname) || '/'

  return (
    <Layout>
      <Sider
        theme="light"
        breakpoint="sm"
        collapsedWidth="0"
        collapsed={collapsed}
        // collapsed={true}
        onBreakpoint={(broken) => {
          // old
          setCollapsed(true)
          setOnBreakpoint(broken)
        }}
        onCollapse={(collapsed) => {
          setCollapsed(collapsed)
        }}
        width="280px">
        {/* Logo */}
        <div
          className="logo"
          onClick={() => {
            navigate('/')
            if (onBreakpoint) {
              setCollapsed(!collapsed)
            }
          }}>
          <img className="Logo m-2" src={imgLogo} alt="Logo" />
        </div>
        {/* Sidebar */}
        <div
          className="sidebar"
          style={{
            display: collapsed ? 'none' : '',
          }}>
          <Menu
            selectedKeys={[selectedKey]}
            mode="inline"
            items={items
              .filter((item) => item.visible)
              .map((item) => ({
                ...item,
                visible: '', // remove after filter
                children: item.children
                  ?.filter((ch) => ch.visible)
                  .map((ch) => ({ ...ch, visible: '' })),
              }))}
            className="overflow-y-scroll flex-1 custom-scrollbar text-left"
          />
          <div
            className="sidebar-account"
            style={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              background: '#d5f1f7',
            }}>
            <img src={imgAvatar} style={{ width: '60px' }} alt="Avatar" />
            <Link to="/profile">
              <div className="mt-2 fw-bold">{currentUser?.Employee_name}</div>
            </Link>
            <div className="mt-1 d-flex justify-content-between">
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'CASHIER',
                      label: 'Cashier',
                      onClick: () => handleChangeMode(MODE_VIEW_DATA.CASHIER),
                    },
                    {
                      key: 'OT',
                      label: 'OT',
                      onClick: () => handleChangeMode(MODE_VIEW_DATA.OT),
                    },
                    {
                      key: 'MS',
                      label: 'MS',
                      onClick: () => handleChangeMode(MODE_VIEW_DATA.MS),
                    },
                    {
                      key: 'NORMAL',
                      label: 'Normal',
                      onClick: () => handleChangeMode(MODE_VIEW_DATA.NORMAL),
                    },
                  ],
                }}
                trigger={['click']}>
                <Button icon={<i className="fas fa-cog"></i>} className="ms-2" size="small">
                  Chọn chế độ
                </Button>
              </Dropdown>
              <Button
                icon={<i className="fa-solid fa-arrow-right-from-bracket"></i>}
                className="ms-2"
                size="small"
                onClick={handleClickLogout}>
                Đăng xuất
              </Button>
            </div>
            <div style={{ fontSize: '0.7em' }} className="text-center mt-1">
              Last version: {config.LAST_VERSION}{' '}
              {config.IN_TEST && <b className="text-danger">TEST</b>}
            </div>
            <div>Copyright © 2024 by FV Hospital</div>
          </div>
        </div>
      </Sider>
      {/* Content */}
      {/* old */}
      {/* <Layout
          className="custom-scrollbar"
          style={{
            minWidth: 360,
            maxHeight: '100vh',
            overflowY: 'scroll',
            minHeight: '100vh',
            background: 'white'
          }}>
          {children}
        </Layout> */}
      {/* new */}
      <Layout
        className="custom-scrollbar"
        style={{
          flex: 1,
          minHeight: '100vh',
          maxHeight: '100vh',
          minWidth: 360,
          overflowY: 'scroll',
          overflowX: 'hidden', // Prevents horizontal scrolling
          background: 'white',
        }}>
        <CustomHeader collapsed={collapsed} setCollapsed={setCollapsed}></CustomHeader>
        <div
          style={{
            flex: 1,
            minWidth: 360,
            overflowY: 'scroll',
            background: 'white',
          }}>
          {children}
        </div>
      </Layout>
      {/* Resize guide is now rendered through the ResizeProvider portal */}
    </Layout>
  )
}

MainLayout.propTypes = {
  children: PropTypes.node.isRequired,
}

export default MainLayout
