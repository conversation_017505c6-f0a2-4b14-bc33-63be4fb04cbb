import { ConfigProvider, App } from 'antd'
import React, { useEffect } from 'react'
import { BrowserRouter, Route, Routes } from 'react-router-dom'
import MainLayout from './MainLayout'
import AuthProvider from './AuthProvider'
import UIProvider from './UIProvider'
import QueryProvider from './QueryProvider'
import LoginPage from '../components/Auth/LoginPage'
import ChangePasswordPage from '../components/Auth/ChangePasswordPage'
import HomePage from '../components/Home/HomePage'
import { Provider } from 'react-redux'
import { store } from '../store'
import NotFoundPage from '../components/Auth/NotFoundPage'
import * as services from '../common/services'
import * as helpers from '../common/helpers'
import lists from './lists'
import _ from 'lodash'
import moment from 'moment'
import dayjs from 'dayjs'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

import locale from 'antd/locale/en_US' // Change locale to English
import { EventType, PublicClientApplication } from '@azure/msal-browser'
import { office365MsalConfig } from '../office365AuthConfig'
import config from './config'
import XmlTool from '../components/Tool/XmlToolPage'
import DetailXMLTool from '../components/Tool/DetailXmlToolPage'
import LearningPage from '../components/Learning/LearningPage'
import MedicinePage from '../components/DM_BHYT/MedicinePage'
import TechnicalServicesPage from '../components/DM_BHYT/TechnicalServicesPage'
import MedicalSuppliesPage from '../components/DM_BHYT/MedicalSuppliesPage'
import VisitListPage from '../components/Visit/VisitListPage'
import VisitDetailPage from '../components/Visit/VisitDetailPage'
import PortalSubmissionListPage from '../components/Visit/PortalSubmissionListPage'
import { DANH_SACH_LUOT_KHAM_LINK } from './constant'
import { ResizeProvider } from './contexts/ResizeContext'
import NinhPage from '../components/Visit/NinhPage'
import PermissionManagement from '../components/Permission'
import MappingManagement from '../components/Mapping/MappingManagement'
import {
  SSItemManagement,
  MedicalSupplyDetail,
  MedicineDetail,
  TechnicalServiceDetail,
} from '../components/SSItem'

dayjs.locale('en')
/**
 * MSAL should be instantiated outside of the component tree to prevent it from being re-instantiated on re-renders.
 * For more, visit: https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-react/docs/getting-started.md
 */

export const msalInstance = new PublicClientApplication(office365MsalConfig)

// Default to using the first account if no account is active on page load

if (!msalInstance.getActiveAccount() && msalInstance.getAllAccounts().length > 0) {
  // Account selection logic is app dependent. Adjust as needed for different use cases.

  msalInstance.setActiveAccount(msalInstance.getAllAccounts()[0])
}

// Listen for sign-in event and set active account

msalInstance.addEventCallback((event) => {
  if (event.eventType === EventType.LOGIN_SUCCESS && event.payload.account) {
    const account = event.payload.account

    msalInstance.setActiveAccount(account)
  }
})

const Root = () => {
  useEffect(() => {
    window.services = services
    window.helpers = helpers
    window.lists = lists
    window._ = _
    window.moment = moment
  }, [])

  return (
    //<MsalProvider instance={msalInstance}>
    <Provider store={store}>
      <App>
        <QueryProvider>
          <UIProvider>
            {/* custom antd */}
            <ConfigProvider
              locale={locale}
              theme={{
                token: {
                  colorPrimary: '#5ac2dc',
                  // colorPrimary: '#73AAAD',
                  cyan: '#145F75',
                  green: '#41ba54',
                },
                components: {
                  Table: {
                    cellPaddingBlockSM: 2, // Padding trên/dưới của ô (td, th)
                    cellPaddingInlineSM: 5, // Padding trái/phải của ô (td, th)
                  },
                },
              }}>
              <ResizeProvider>
                {/* Router */}
                <BrowserRouter basename={config.HOME_PAGE}>
                  <Routes>
                    {/* public routes */}
                    <Route path="/login" element={<LoginPage />} />
                    {/* <Route path="/forgot-password" element={<ForgotPassword />} /> */}
                    <Route path="/notfound" element={<NotFoundPage />} />
                    <Route path="/ninh" element={<NinhPage />} />

                    {/* auth routes */}
                    <Route element={<AuthProvider />}>
                      <Route path="/change-password" element={<ChangePasswordPage />} />
                      <Route
                        path="/"
                        element={
                          <MainLayout>
                            <HomePage />
                          </MainLayout>
                        }
                      />
                      <Route
                        path="/tool"
                        element={
                          <MainLayout>
                            <XmlTool />
                          </MainLayout>
                        }
                      />
                      <Route
                        path="/tool/:invoice_no_"
                        element={
                          <MainLayout>
                            <DetailXMLTool />
                          </MainLayout>
                        }
                      />
                      <Route
                        path="/learning"
                        element={
                          <MainLayout>
                            <LearningPage></LearningPage>
                          </MainLayout>
                        }
                      />
                      <Route
                        path={DANH_SACH_LUOT_KHAM_LINK} // /his/visit
                        element={
                          <MainLayout>
                            <VisitListPage></VisitListPage>
                          </MainLayout>
                        }></Route>
                      <Route
                        path={DANH_SACH_LUOT_KHAM_LINK + '/:id'} // /his/visit/:id
                        element={
                          <MainLayout>
                            <VisitDetailPage></VisitDetailPage>
                          </MainLayout>
                        }></Route>
                      <Route
                        path="/DM_BHYT/medicine"
                        element={
                          <MainLayout>
                            <MedicinePage></MedicinePage>
                          </MainLayout>
                        }></Route>
                      <Route
                        path="/DM_BHYT/technical-services"
                        element={
                          <MainLayout>
                            <TechnicalServicesPage></TechnicalServicesPage>
                          </MainLayout>
                        }></Route>
                      <Route
                        path="/DM_BHYT/medical-supplies"
                        element={
                          <MainLayout>
                            <MedicalSuppliesPage></MedicalSuppliesPage>
                          </MainLayout>
                        }></Route>
                      <Route
                        path="/his/visit/portal-submission"
                        element={
                          <MainLayout>
                            <PortalSubmissionListPage></PortalSubmissionListPage>
                          </MainLayout>
                        }></Route>

                      <Route
                        path="/permission"
                        element={
                          <MainLayout>
                            <PermissionManagement></PermissionManagement>
                          </MainLayout>
                        }></Route>

                      <Route
                        path="/mapping"
                        element={
                          <MainLayout>
                            <MappingManagement></MappingManagement>
                          </MainLayout>
                        }></Route>

                      <Route
                        path="/ssitem"
                        element={
                          <MainLayout>
                            <SSItemManagement />
                          </MainLayout>
                        }></Route>

                      <Route
                        path="/ssitem/medical_supplies/:id"
                        element={
                          <MainLayout>
                            <MedicalSupplyDetail />
                          </MainLayout>
                        }></Route>

                      <Route
                        path="/ssitem/medicine/:id"
                        element={
                          <MainLayout>
                            <MedicineDetail />
                          </MainLayout>
                        }></Route>

                      <Route
                        path="/ssitem/technical_services/:id"
                        element={
                          <MainLayout>
                            <TechnicalServiceDetail />
                          </MainLayout>
                        }></Route>
                    </Route>

                    {/* notfound */}
                    <Route path="*" element={<NotFoundPage />} />
                  </Routes>
                </BrowserRouter>
              </ResizeProvider>
            </ConfigProvider>
          </UIProvider>
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryProvider>
      </App>
    </Provider>
    // </MsalProvider>
  )
}

export default Root
