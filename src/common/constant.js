export const MINUTE = [0, 15, 30, 45]

export const HOUR = Array.from({ length: 24 }, (_, i) => i)

export const LOGIN_TYPES = { OFFICE365: 'OFFICE365LOGINTYPE', MANUAL: 'MANUALLOGINTYPE' }

export const FIELDS = [
  { databank_field_id: 1, name: 'FullName', display_e: 'FullName', display_l: 'Họ và tên' },
  { databank_field_id: 2, name: 'Address', display_e: 'Address', display_l: 'Địa chỉ' },
  { databank_field_id: 3, name: 'DateOfBirth', display_e: 'DateOfBirth', display_l: 'Ngày sinh' },
  { databank_field_id: 4, name: 'Sex', display_e: 'Sex', display_l: 'Giới tính' },
  {
    databank_field_id: 5,
    name: 'RecordLogbookNo',
    display_e: 'Record Logbook No.',
    display_l: 'Record Logbook No.',
  },
  {
    databank_field_id: 6,
    name: 'H<PERSON>',
    display_e: 'HN',
    display_l: 'HN',
  },
]

export const LOGBOOKS = {
  Value: [
    [
      {
        app_id: '330e8400-e29b-41d4-a716-************',
        logbook_type_id: 'LT002',
        log_book_status_rcd: null,
        logbook_field_values: [
          {
            logbook_field_value_id: '220e8400-e29b-41d4-a716-************',
            app_id: '330e8400-e29b-41d4-a716-************',
            databank_field_id: '550e8400-e29b-41d4-a716-************',
            value_text: 'Klein Moretti',
            value_date: null,
            value_number: null,
          },
        ],
      },
      {
        app_id: '330e8400-e29b-41d4-a716-************',
        logbook_type_id: 'LT002',
        log_book_status_rcd: null,
        logbook_field_values: [],
      },
    ],
  ],
}

export const CREATED_LOGBOOK_FIELD_VALUES = {
  logbook_field_value_id: '',
  app_id: '330e8400-e29b-41d4-a716-************',
  databank_field_id: '550e8400-e29b-41d4-a716-************',
  value_text: 'Klein Moretti',
  value_date: null,
  value_number: null,
}

export const UPDATED_LOGBOOK_FIELD_VALUES = {
  logbook_field_value_id: '330e8400-e29b-41d4-a716-************',
  app_id: '330e8400-e29b-41d4-a716-************',
  databank_field_id: '550e8400-e29b-41d4-a716-************',
  value_text: 'Klein Moretti',
  value_date: null,
  value_number: null,
}

export const FORMAT_DATE = 'YYYY-MM-DD'

export const FORMAT_DATE_DMY = 'DD/MM/YYYY'

export const FORMAT_DATETIME = 'YYYY-MM-DD HH:mm:ss'
export const FORMAT_DATETIME_WITHOUT_SECOND = 'YYYY-MM-DD HH:mm'
export const FORMAT_DATE_UTC = 'YYYY-MM-DDTHH:mm:ssZ'

export const FORMAT_TIME = 'h:mm A'
export const FORMAT_MONTHTIME = 'MMM-YYYY'

export const BUTTON_FONT_WEIGHT = 600

export const DANH_SACH_LUOT_KHAM_LINK = '/his/visit'

export const MODE_VIEW_DATA = {
  CASHIER: 'CASHIER',
  OT: 'OT',
  MS: 'MS',
  NORMAL: 'NORMAL',
}

export const FORM_MODE = {
  edit: 'edit',
  new: 'new',
  view: 'view',
}
