import React, { createContext, useContext, useEffect, useState } from 'react'
import { Navigate, Outlet, useLocation, useNavigate, useSearchParams } from 'react-router-dom'
import { handleError } from './helpers'
import { getAuth, refreshTokenService } from './services'
import { useReadLocalStorage } from 'usehooks-ts'
import LoadingPage from './components/LoadingPage'
import { useDispatch, useSelector } from 'react-redux'
import { commonActions, getMetadataXml } from '../store/common'
import config from './config'
import { authActions, MODULE_AUTH } from '../store/auth'
import { visitActions } from '../store/Visit'
import { clearProcessingPersonForUser } from '../components/Visit/VisitService'

const AuthContext = createContext(null)

const AuthProvider = () => {
  // localStorage
  const [profile, setProfile] = useState(useReadLocalStorage(config.LOCAL_PROFILE))
  const [loginType, setLoginType] = useState(useReadLocalStorage(config.LOCAL_LOGIN_TYPE))
  // state
  const [isGettingUser, setIsGettingUser] = useState(true)
  const [isRefreshPage, setIsRefreshPage] = useState(true)
  const { permission } = useSelector((state) => state[MODULE_AUTH])
  // hook
  const location = useLocation()
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const [searchParams] = useSearchParams()

  const pModeViewData = searchParams.get('modeViewData')
  const pVisitDetailMode = searchParams.get('visitDetailMode')

  useEffect(() => {
    if (pModeViewData) {
      dispatch(authActions.setModeViewData(pModeViewData))
    }
    if (pVisitDetailMode) {
      dispatch(visitActions.setVisitDetailMode(pVisitDetailMode))
    }
  }, [pModeViewData, pVisitDetailMode])

  const logout = async () => {
    try {
      // Clear processing person information for the current user
      const currentUser = JSON.parse(localStorage.getItem(config.LOCAL_PROFILE))?.user
      if (currentUser?.User_id) {
        await clearProcessingPersonForUser(currentUser.User_id)
      }
    } catch (error) {
      console.error('Error clearing processing person during logout:', error)
    } finally {
      // Continue with normal logout process
      localStorage.removeItem(config.LOCAL_PROFILE)
      localStorage.removeItem(config.LOCAL_AUTHENTICATED)
      localStorage.removeItem(config.LOCAL_ACCESS_TOKEN)
      localStorage.removeItem(config.LOCAL_REFRESH_TOKEN)
      localStorage.removeItem(config.LOCAL_LOGIN_TYPE)

      setProfile()
      setLoginType()

      navigate('/login', {
        state: {
          returnUrl: location.pathname,
        },
      })

      window.location.reload()
    }
  }

  const fetchUser = async () => {
    setIsGettingUser(true)

    var tryTime = 3
    try {
      const newProfile = await getAuth()

      setProfile(newProfile)
      dispatch(authActions.setPermission(newProfile.permission))
      dispatch(authActions.setCurrentUser(newProfile.user))

      localStorage.setItem(config.LOCAL_PROFILE, JSON.stringify(newProfile))
    } catch (error) {
      if (handleError(error) === '"401"' && tryTime >= 0) {
        try {
          tryTime--

          await handleRefreshToken()
          await fetchUser()

          return
        } catch (error) {
          logout()
        }
      } else {
        logout()
      }
    }

    setIsGettingUser(false)
    setIsRefreshPage(false)
  }

  const getWorkLocationAndMapping = async () => {
    try {
      let workLocation = []

      dispatch(commonActions.setWorkLocationList(workLocation))
    } catch (error) {
      handleError(error)
    }
  }

  const handleRefreshToken = async () => {
    try {
      const refreshToken = localStorage.getItem(config.LOCAL_REFRESH_TOKEN)
      const accessToken = localStorage.getItem(config.LOCAL_ACCESS_TOKEN)
      const newToken = await refreshTokenService(accessToken, refreshToken)

      localStorage.setItem(config.LOCAL_ACCESS_TOKEN, newToken.accessToken)
      localStorage.setItem(config.LOCAL_REFRESH_TOKEN, newToken.refreshToken)

      return newToken
    } catch (error) {
      logout()
    }
  }

  const handleGetMetadataXml = () => {
    dispatch(getMetadataXml())
  }

  const checkPermission = (permissionToCheck = '') => {
    return !!permission.find((p) => p.permission_rcd === permissionToCheck)
  }

  useEffect(() => {
    fetchUser()
    handleGetMetadataXml()
  }, [])

  if (isGettingUser && isRefreshPage) {
    return <LoadingPage open={true} />
  }

  if (profile && !isRefreshPage) {
    return (
      <AuthContext.Provider value={{ profile, logout, fetchUser, loginType, checkPermission }}>
        <Outlet />
      </AuthContext.Provider>
    )
  }

  if (!profile && !isGettingUser) {
    return <Navigate to="/login" state={{ returnUrl: location.pathname }} />
  }
}

export function useAuth() {
  const { profile, logout, fetchUser, loginType, checkPermission } = useContext(AuthContext)
  return { profile, logout, fetchUser, loginType, checkPermission }
}

export default AuthProvider
