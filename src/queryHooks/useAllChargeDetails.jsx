import { useQuery, useQueryClient } from '@tanstack/react-query'
import {
  getAllMergedPatientVisitMappingViewsByParentPatientVisitId,
  getAllVisitChargeDetailsByPatientVisitId,
} from '../components/Visit/VisitService'

// Keys for React Query
export const ALL_VISIT_CHARGE_QUERY_KEYS = {
  ALL_VISIT_CHARGE_DETAILS: 'allVisitChargeDetails',
}

export const useAllChargeDetails = (patientVisitId, enabled = true) => {
  const queryClient = useQueryClient()

  // Query to get merged visits
  const mergedVisitsQuery = useQuery({
    queryKey: ['mergedVisits', patientVisitId],
    queryFn: async () => {
      const data = await getAllMergedPatientVisitMappingViewsByParentPatientVisitId(patientVisitId)
      return data?.value || []
    },
    enabled: !!patientVisitId && enabled,
  })

  // Query to get all charge details (main visit + merged visits)
  const allChargeDetailsQuery = useQuery({
    queryKey: [ALL_VISIT_CHARGE_QUERY_KEYS.ALL_VISIT_CHARGE_DETAILS, patientVisitId],
    queryFn: async () => {
      // Get main visit charge details
      const mainVisitChargeDetailsResponse =
        await getAllVisitChargeDetailsByPatientVisitId(patientVisitId)
      const mainVisitChargeDetails = mainVisitChargeDetailsResponse?.value || []

      // Get merged visits charge details
      const mergedVisits = mergedVisitsQuery.data || []
      const mergedVisitsChargeDetailsPromises = mergedVisits.map(async (mergedVisit) => {
        const mergedVisitId = mergedVisit.patient_visit_id
        const response = await getAllVisitChargeDetailsByPatientVisitId(mergedVisitId)

        return response?.value || []
      })

      // Wait for all promises to resolve
      const mergedVisitsChargeDetailsArrays = await Promise.all(mergedVisitsChargeDetailsPromises)

      // Flatten the array of arrays into a single array
      const mergedVisitsChargeDetails = mergedVisitsChargeDetailsArrays.flat()

      // Combine main visit and merged visits charge details
      const allChargeDetails = [...mainVisitChargeDetails, ...mergedVisitsChargeDetails]

      // Calculate charge details
      return allChargeDetails
    },
    enabled: !!patientVisitId && enabled && mergedVisitsQuery.isSuccess,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  })

  // Filter charge details by manual_ss_cover_flag
  const coveredChargeDetails =
    allChargeDetailsQuery.data?.filter((item) => item.manual_ss_cover_flag === true) || []
  const uncoveredChargeDetails =
    allChargeDetailsQuery.data?.filter((item) => item.manual_ss_cover_flag === false) || []

  // Manually refetch data
  const refetchData = async () => {
    queryClient.invalidateQueries({
      queryKey: [ALL_VISIT_CHARGE_QUERY_KEYS.ALL_VISIT_CHARGE_DETAILS, patientVisitId],
    })
  }

  return {
    allChargeDetailsQuery,
    allChargeDetails: allChargeDetailsQuery.data || [],
    coveredChargeDetails,
    uncoveredChargeDetails,
    isLoading: allChargeDetailsQuery.isLoading || mergedVisitsQuery.isLoading,
    isSuccess: allChargeDetailsQuery.isSuccess && mergedVisitsQuery.isSuccess,
    isError: allChargeDetailsQuery.isError || mergedVisitsQuery.isError,
    refetchData,
  }
}
