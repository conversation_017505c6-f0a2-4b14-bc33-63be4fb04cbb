import { useQuery, useQueryClient } from '@tanstack/react-query'
import { getItemsService } from '../common/services'
import lists from '../common/lists'
import { uniq, uniqBy } from 'lodash'

// Keys for React Query
export const DEPARTMENT_MAPPING_KEYS = {
  DEPARTMENT_MAPPING: 'departmentMapping',
}

export const useDepartmentMapping = ({ enabled = true } = {}) => {
  const queryClient = useQueryClient()
  const departmentMappingQueryKey = [DEPARTMENT_MAPPING_KEYS.DEPARTMENT_MAPPING]

  const departmentMappingQuery = useQuery({
    queryKey: departmentMappingQueryKey,
    queryFn: async () => {
      const data = await getItemsService(lists.department_mapping_si, {
        orderBy: 'ten_khoa_vn asc',
      })

      return data.value
    },
    enabled: enabled,
    initialData: [],
  })

  // Manually refetch all data
  const refetchAll = async () => {
    queryClient.invalidateQueries({ queryKey: departmentMappingQueryKey })
  }

  return {
    departmentMappings: departmentMappingQuery.data || [],
    departmentSS: uniqBy(
      (departmentMappingQuery.data || []).map((dept) => ({
        ma_khoa_bhyt: dept.ma_khoa_bhyt,
        ten_khoa_bhyt: dept.ten_khoa_bhyt,
      })),
      (r) => r.ma_khoa_bhyt + r.ten_khoa_bhyt,
    ).sort((a, b) => a.ma_khoa_bhyt.localeCompare(b.ma_khoa_bhyt)),
    departmentMappingQuery,
    refetchAll,
    isLoading: departmentMappingQuery.isLoading,
    isError: departmentMappingQuery.isError,
  }
}
