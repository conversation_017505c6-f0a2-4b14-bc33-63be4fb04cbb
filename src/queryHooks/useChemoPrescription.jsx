import { useQuery, useQueryClient } from '@tanstack/react-query'
import { getItemsService } from '../common/services'
import lists from '../common/lists'
import dayjs from '../common/dayjs'

export const CHEMO_PRESCRIPTION_KEYS = {
  CHEMO_PRESCRIPTION: 'chemo_prescription',
  CHEMO_PRESCRIPTION_DETAIL: 'chemo_prescription_detail',
}

// patient_visit_id = '4930c8f9-33b7-487b-0671-08dc8b7a671d'
export const useChemoPrescription = ({ patientVisitId, byDate, byEmployeeId } = {}) => {
  const queryClient = useQueryClient()
  const chemoPrescriptionQueryKey = [
    CHEMO_PRESCRIPTION_KEYS.CHEMO_PRESCRIPTION,
    patientVisitId,
    byDate,
  ]

  const chemoPrescriptionQuery = useQuery({
    queryKey: chemoPrescriptionQueryKey,
    queryFn: async () => {
      const filterList = [`patient_visit_id eq ${patientVisitId}`]

      if (byDate) {
        filterList.push(`created_date eq ${dayjs(byDate).format('YYYY-MM-DD')}`)
      }
      if (byEmployeeId) {
        filterList.push(`verified_emp_id eq ${byEmployeeId}`)
      }

      const data = await getItemsService(lists.chemo_prescription_nl_view, {
        filter: filterList.join(' and '),
        top: 1,
      })

      return data.value[0] || {}
    },
    enabled: !!patientVisitId,
  })

  const prescriptionId = chemoPrescriptionQuery.data?.prescription_id
  const chemoPrescriptionDetailQueryKey = [
    CHEMO_PRESCRIPTION_KEYS.CHEMO_PRESCRIPTION_DETAIL,
    patientVisitId,
    prescriptionId,
  ]
  const chemoPrescriptionDetailQuery = useQuery({
    queryKey: chemoPrescriptionDetailQueryKey,
    queryFn: async () => {
      const data = await getItemsService(lists.chemo_prescription_detail_nl_view, {
        filter: `prescription_id eq ${prescriptionId}`,
      })

      return data.value
    },
    enabled: !!prescriptionId,
  })

  // Manually refetch all data
  const refetchAll = async () => {
    queryClient.invalidateQueries({ queryKey: chemoPrescriptionQueryKey })
    queryClient.invalidateQueries({ queryKey: chemoPrescriptionDetailQueryKey })
  }

  return {
    chemoPrescription: chemoPrescriptionQuery.data || {},
    chemoPrescriptionDetail: chemoPrescriptionDetailQuery.data || [],
    chemoPrescriptionQuery,
    chemoPrescriptionDetailQuery,
    isChemoPrescriptionLoading: chemoPrescriptionQuery.isLoading,
    isChemoPrescriptionDetailLoading: chemoPrescriptionDetailQuery.isLoading,
    isChemoPrescriptionFetching:
      chemoPrescriptionQuery.isFetching || chemoPrescriptionDetailQuery.isFetching,

    refetchAll,
  }
}
