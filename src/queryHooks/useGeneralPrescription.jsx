import { useQuery, useQueryClient } from '@tanstack/react-query'
import { getItemsService } from '../common/services'
import lists from '../common/lists'
import dayjs from '../common/dayjs'

export const GENERAL_PRESCRIPTION_KEYS = {
  GENERAL_PRESCRIPTION: 'general_prescription',
  GENERAL_PRESCRIPTION_DETAIL: 'general_prescription_detail',
}

// patient_visit_id = '4930c8f9-33b7-487b-0671-08dc8b7a671d'
export const useGeneralPrescription = ({ patientVisitId, byDate, byEmployeeId } = {}) => {
  const queryClient = useQueryClient()
  const generalPrescriptionQueryKey = [
    GENERAL_PRESCRIPTION_KEYS.GENERAL_PRESCRIPTION,
    patientVisitId,
    byDate,
  ]

  const generalPrescriptionQuery = useQuery({
    queryKey: generalPrescriptionQueryKey,
    queryFn: async () => {
      const filterList = [`patient_visit_id eq ${patientVisitId}`]

      if (byDate) {
        filterList.push(`created_date_time eq ${dayjs(byDate).format('YYYY-MM-DD')}`)
      }
      if (byEmployeeId) {
        filterList.push(`prescriber_employee_id eq ${byEmployeeId}`)
      }

      const data = await getItemsService(lists.general_prescription_nl_view, {
        filter: filterList.join(' and '),
        top: 10,
      })

      return data.value || []
    },
    enabled: !!patientVisitId,
  })

  const prescriptionIdList = generalPrescriptionQuery.data?.map((item) => item.ph_prescription_id)
  const generalPrescriptionDetailQueryKey = [
    GENERAL_PRESCRIPTION_KEYS.GENERAL_PRESCRIPTION_DETAIL,
    patientVisitId,
    prescriptionIdList,
  ]
  const generalPrescriptionDetailQuery = useQuery({
    queryKey: generalPrescriptionDetailQueryKey,
    queryFn: async () => {
      const prmiseses = prescriptionIdList.map(async (prescriptionId) => {
        const response = await getItemsService(lists.general_prescription_detail_nl_view, {
          filter: `ph_prescription_id eq ${prescriptionId}`,
        })

        return response.value
      })

      const results = await Promise.all(prmiseses)
      return results.flat()
    },
    enabled: !!prescriptionIdList?.[0] && generalPrescriptionQuery.isSuccess,
  })

  // Manually refetch all data
  const refetchAll = async () => {
    queryClient.invalidateQueries({ queryKey: generalPrescriptionQueryKey })
    queryClient.invalidateQueries({ queryKey: generalPrescriptionDetailQueryKey })
  }

  return {
    generalPrescription: generalPrescriptionQuery.data || [],
    generalPrescriptionDetail: generalPrescriptionDetailQuery.data || [],
    generalPrescriptionQuery,
    generalPrescriptionDetailQuery,
    isGeneralPrescriptionLoading: generalPrescriptionQuery.isLoading,
    isGeneralPrescriptionDetailLoading: generalPrescriptionDetailQuery.isLoading,
    isGeneralPrescriptionFetching:
      generalPrescriptionQuery.isFetching || generalPrescriptionDetailQuery.isFetching,

    refetchAll,
  }
}
