import { useQuery, useQueryClient } from '@tanstack/react-query'
import { getItemsService } from '../common/services'
import lists from '../common/lists'
import { getOccupationRefs } from '../components/Visit/VisitService'

// Keys for React Query
export const OCCUPATION_KEYS = {
  OCCUPATION: 'occupation',
}

export const useOccupation = ({ enabled = true } = {}) => {
  const queryClient = useQueryClient()
  const occupationQueryKey = [OCCUPATION_KEYS.OCCUPATION]

  const occupationQuery = useQuery({
    queryKey: occupationQueryKey,
    queryFn: async () => {
      const data = await getOccupationRefs()

      return data.value
    },
    enabled: enabled,
  })

  // Manually refetch all data
  const refetchAll = async () => {
    queryClient.invalidateQueries({ queryKey: occupationQueryKey })
  }

  return {
    occupationList: occupationQuery.data || [],
    occupationQuery,
    refetchAll,
    isLoading: occupationQuery.isLoading,
    isError: occupationQuery.isError,
  }
}
