import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'

import { handleError } from '../common/helpers'
import { useUI } from '../common/UIProvider'
import nProgress from 'nprogress'
import { getItemService, getItemsService } from '../common/services'
import lists from '../common/lists'
import {
  MEDICAL_RECORD_FORM_TYPE,
  MEDICAL_RECORD_VIEW_MODE,
} from '../components/MedicalRecord/MedicalRecordConstant'

// Keys for React Query
export const MEDICAL_RECORD_KEYS = {
  MEDICAL_RECORD: 'medicalRecord',
  MEDICAL_CONDITION: 'medicalCondition',
}

export const useMedicalRecord = ({
  patientVisitId,
  medicalRecordFormId,
  patientId,
  medicalRecordViewMode = MEDICAL_RECORD_VIEW_MODE.BY_VISIT,
  enabled = true,
} = {}) => {
  const queryClient = useQueryClient()
  const medicalRecordListQueryKey = [
    MEDICAL_RECORD_KEYS.MEDICAL_RECORD,
    patientVisitId,
    patientId,
    medicalRecordViewMode,
  ]
  const medicalRecordDetailQueryKey = [MEDICAL_RECORD_KEYS.MEDICAL_RECORD, medicalRecordFormId]

  const medicalRecordListQuery = useQuery({
    queryKey: medicalRecordListQueryKey,
    queryFn: async () => {
      // validate
      if (medicalRecordViewMode === MEDICAL_RECORD_VIEW_MODE.BY_PATIENT && !patientId) {
        return []
      }
      if (
        medicalRecordViewMode === MEDICAL_RECORD_VIEW_MODE.BY_VISIT &&
        (!patientVisitId || !patientId)
      ) {
        return []
      }

      let filter = []

      if (medicalRecordViewMode === MEDICAL_RECORD_VIEW_MODE.BY_PATIENT) {
        filter.push(`patient_id eq ${patientId}`)
      }

      if (medicalRecordViewMode === MEDICAL_RECORD_VIEW_MODE.BY_VISIT) {
        filter.push(
          `(patient_visit_id eq ${patientVisitId} or 
            (patient_id eq ${patientId} and 
            medical_record_form_type_rcd eq '${MEDICAL_RECORD_FORM_TYPE.GIAY_CHAP_THUAN.key}'))`,
        )
      }

      let data = await getItemsService(lists.medical_record_form, {
        filter: filter.join(' and '),
        orderBy: 'form_date_time asc',
      })
      data = data.value || []

      return data
    },
    enabled: (!!patientVisitId || !!patientId) && !!medicalRecordViewMode && enabled,
  })

  const medicalRecordDetailQuery = useQuery({
    queryKey: medicalRecordDetailQueryKey,
    queryFn: async () => {
      const data = await getItemService(lists.medical_record_form, medicalRecordFormId)
      return data
    },
    enabled: !!medicalRecordFormId && enabled,
  })

  // Fetch the associated medical_condition if this is a consultation form
  const medicalConditionQuery = useQuery({
    queryKey: [MEDICAL_RECORD_KEYS.MEDICAL_CONDITION, medicalRecordFormId],
    queryFn: async () => {
      // First get the medical record form to check if it has a medical_condition_id
      const medicalRecordForm = medicalRecordDetailQuery.data

      // If this is a consultation form and has a medical_condition_id, fetch the medical condition
      if (
        medicalRecordForm.medical_record_form_type_rcd ===
          MEDICAL_RECORD_FORM_TYPE.PHIEU_HOI_CHAN.key &&
        medicalRecordForm.medical_condition_id
      ) {
        const medicalCondition = await getItemService(
          lists.medical_condition,
          medicalRecordForm.medical_condition_id,
        )
        return medicalCondition
      }

      return null
    },
    enabled: !!medicalRecordDetailQuery.data && enabled,
  })

  // Manually refetch all data
  const refetchAll = async () => {
    queryClient.invalidateQueries({ queryKey: medicalRecordListQueryKey })
    queryClient.invalidateQueries({ queryKey: medicalRecordDetailQueryKey })
  }

  const refetchMedicalRecordDetail = () => {
    queryClient.invalidateQueries({ queryKey: medicalRecordDetailQueryKey })
  }

  return {
    medicalRecordList: medicalRecordListQuery.data || [],
    medicalRecordListQuery,
    medicalRecordDetail: medicalRecordDetailQuery.data || {},
    medicalRecordDetailQuery,
    medicalCondition: medicalConditionQuery.data || {},
    medicalConditionQuery,
    refetchAll,
    refetchMedicalRecordDetail,
  }
}
