import { useQuery, useQueryClient } from '@tanstack/react-query'
import { getItemService, getItemsService } from '../common/services'
import lists from '../common/lists'
import { formatHN } from '../common/helpers'

// Keys for React Query
export const PATIENT_KEYS = {
  PATIENT: 'PatientDataset',
  PATIENT_LIST: 'PatientDatasetList',
}

export const usePatient = ({
  filter = {
    filterHN: '',
    strict: false,
  },
  patientId = '',
  enabled = true,
} = {}) => {
  const queryClient = useQueryClient()
  const patientQueryKey = [PATIENT_KEYS.PATIENT, patientId]
  const patientListQueryKey = [PATIENT_KEYS.PATIENT_LIST, filter]

  const patientQuery = useQuery({
    queryKey: patientQueryKey,
    queryFn: async () => {
      const data = await getItemService(lists.patient_dataset, patientId)

      return data
    },
    enabled: !!patientId && enabled,
  })

  const patientListQuery = useQuery({
    queryKey: patientListQueryKey,
    queryFn: async () => {
      const filterString = []

      if (filter.strict && !filter.filterHN) {
        return []
      }

      if (filter.filterHN) {
        const formatedHN = formatHN(filter.filterHN)
        filterString.push(`(HN eq '${formatedHN}')`)
      }

      const data = await getItemsService(lists.patient_dataset, {
        filter: filterString.join(' and '),
        top: filter.strict ? 1 : 100,
      })

      return data.value
    },
    enabled: enabled,
  })

  const refetchAll = async () => {
    queryClient.invalidateQueries({ queryKey: patientQueryKey })
    queryClient.invalidateQueries({ queryKey: patientListQueryKey })
  }

  return {
    patient: patientQuery.data || {},
    patientList: patientListQuery.data || [],
    patientQuery,

    isLoading: patientListQuery.isLoading && patientQuery.isLoading,
    isFetching: patientListQuery.isFetching || patientQuery.isFetching,

    refetchAll,
  }
}
