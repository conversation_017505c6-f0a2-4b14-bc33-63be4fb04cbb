import { useQuery, useQueryClient } from '@tanstack/react-query'
import { getItemsService } from '../common/services'
import lists from '../common/lists'

// Keys for React Query
export const EMPLOYEE_KEYS = {
  EMPLOYEE: 'EmployeeDataset',
}

export const useEmployee = ({ userId, enabled = true } = {}) => {
  const queryClient = useQueryClient()
  const employeeQueryKey = [EMPLOYEE_KEYS.EMPLOYEE, userId]

  const employeeQuery = useQuery({
    queryKey: employeeQueryKey,
    queryFn: async () => {
      const data = await getItemsService(lists.employee_dataset, {
        filter: `user_id eq ${userId}`,
        top: 1,
      })

      return data.value[0]
    },
    enabled: !!userId && enabled,
  })

  // Manually refetch all data
  const refetchAll = async () => {
    queryClient.invalidateQueries({ queryKey: employee<PERSON><PERSON><PERSON><PERSON><PERSON> })
  }

  return {
    employee: employeeQuery.data || {},
    employeeQuery,
    refetchAll,
  }
}
